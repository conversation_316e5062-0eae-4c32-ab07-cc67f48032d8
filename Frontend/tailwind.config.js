/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './public/index.html',
    './src/**/*.{html,js}'
  ],
  theme: {
    extend: {
      colors: {
        'brand': '#1456f0',
        'cream': '#f8f8f8',
        'noah-theme': '#00A98F',
        'gray-1': '#22222299', // 60% opacity
        'gray-2': '#22222259', // 35% opacity
        'gray-3': '#3333330D', // 5% opacity
        'black-1': '#222222',
        'aqua': '#00A98F14',
      },
      boxShadow: {
        'inner-right': 'inset -1px 0 1px -1px rgba(0, 0, 0, 0.3)',
      },
      keyframes: {
        typingPulse: {
          '0%, 100%': { opacity: 0.6 },
          '50%': { opacity: 0.3 },
        },
      },
      animation: {
        'typingPulse': 'typingPulse 1.4s infinite ease-in-out',
      },
    },
  },
  plugins: [
    require('daisyui'),
    require('tailwind-scrollbar'),
    require('@tailwindcss/forms')({
      strategy: 'class',
    }),
    require('@tailwindcss/typography'),
    require('@tailwindcss/container-queries'),
  ],
  daisyui: {
    styled: true,
    base: true,
    utils: true,
    logs: false,
    rtl: false,
    prefix: '',
    darkTheme: 'light', // Force light theme as default
    themes: [
      {
        light: {
          ...require('daisyui/src/theming/themes')['light'],
          'primary': '#eeeeee',
          'primary-content': '#000000',
          'secondary': '#eeeeee',
          'secondary-content': '#333333',
          'accent': '#2b3440',
          'accent-content': '#cdcdcd',
          'neutral': '#3d4451',
          'neutral-content': '#999999',
          'base-100': '#ffffff',
          'base-200': '#e5e7eb',
          'base-300': '#f2f5f8',
          'cream': '#f8f8f8',
          'noah-theme': '#00A98F',
          'gray-1': '#22222299', // 60% opacity
          'gray-2': '#22222259', // 35% opacity
          'gray-3': '#3333330D', // 5% opacity
          'black-1': '#222222',
          'aqua': '#00A98F14',
          // Add explicit color mode to prevent browser preference overrides
          'color-scheme': 'light',
        },
        // dark: {
        //   ...require("daisyui/src/theming/themes")["dark"],
        //   "primary": "#1456f0",
        //   "primary-content": "#ffffff",
        //   "secondary": "#263140",
        //   "secondary-content": "#dddddd",
        //   "accent": "#ffffff",
        //   "accent-content": "#1456f0",
        //   "neutral": "#3d4451",
        //   "neutral-content": "#777777",
        //   "base-100": "#141a20",
        //   "base-200": "#1e293b",
        //   "base-300": "#1e293b",
        // }
      },
    ],
  },
  // Add this to prevent color-scheme preference overrides
  corePlugins: {
    preflight: true,
  },
}

