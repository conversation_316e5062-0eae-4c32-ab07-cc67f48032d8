export function getSessionValue(key, defaultValue) {
  return '123'
  // const value = sessionStorage.getItem(key);
  // if (!value) {
  //     return defaultValue;
  // } else {
  //     return value;
  // }
}

export function getSessionToken() {
  return getSessionValue('noahToken', null);
}

export function setSessionToken(value) {
  sessionStorage.setItem('noahToken', value);
  return null;
}

export function getSessionUserID() {
  return getSessionValue('noahUser', null);
}

export function setSessionUserID(value) {
  sessionStorage.setItem('noahUser', value);
  return null;
}

export function removeSessionUserID() {
  sessionStorage.removeItem('noahUser');
  return null;
}

export function getSessionUserValid() {
  return getSessionValue('noahUserValid', false);
}

export function setSessionUserValid(value) {
  sessionStorage.setItem('noahUserValid', value);
  return null;
}

export function removeSessionUserValid() {
  sessionStorage.removeItem('noahUserValid');
  return null;
}

export function setSessionCSRF(value) {
  sessionStorage.setItem('csrf', value);
  return null;
}

export function getSessionCSRF() {
  return getSessionValue('csrf', '');
}
