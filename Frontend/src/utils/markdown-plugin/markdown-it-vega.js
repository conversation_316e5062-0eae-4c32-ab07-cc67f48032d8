import vegaEmbed from 'vega-embed';

// Function to create a simple hash from string
export function simpleHash(str) {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash).toString(36);
}

// Function to check JSON completeness
function isValidVegaSpec(spec) {
  try {
    // Basic required field check
    if (!spec || typeof spec !== 'object') return false;
    if (!spec.data) return false;
    
    // Check if JSON string is complete (ends with })
    const jsonStr = JSON.stringify(spec);
    if (!jsonStr.trim().endsWith('}')) return false;
    
    return true;
  } catch (e) {
    return false;
  }
}

const loadingState = (chartId) => {
  return (
    `<div class="vega-container" id="${chartId}">
      <div class="flex justify-center items-center h-[150px] text-base-content/50">
        <svg class="animate-spin h-5 w-5 mr-3" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" fill="none"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        Generating chart...
      </div>
    </div>`
  )
}

// Create a DOM cache to store rendered charts
const vegaCache = new Map();

export function markdownItVega(md) {
  console.log('markdownItVega');
  const defaultFence = md.renderer.rules.fence;

  md.renderer.rules.fence = (tokens, idx, options, env, self) => {
    const token = tokens[idx];
    const lang = token.info.trim();

    if (lang === 'vega' || lang === 'vega-lite') {
      try {
        // Create deterministic chart ID based on content hash
        const contentHash = simpleHash(token.content);
        const chartId = `vega-chart-${contentHash}`;
        
        // Return cached HTML if exists
        if (vegaCache.has(chartId)) {
          return vegaCache.get(chartId);
        }

        let spec;
        try {
          spec = JSON.parse(token.content);
        } catch (e) {
          // Return loading placeholder when JSON parsing fails
          return defaultFence(tokens, idx, options, env, self);
        }
        
        // Data validation
        if (!isValidVegaSpec(spec)) {
          return defaultFence(tokens, idx, options, env, self);
        }

        const html = `<div class="vega-container" id="${chartId}" data-spec="${encodeURIComponent(JSON.stringify(spec))}" data-lang="${lang}"></div>`;
        vegaCache.set(chartId, html);
        return html;
      } catch (e) {
        // Show loading state for other errors
        const contentHash = simpleHash(token.content || '');
        const chartId = `vega-chart-${contentHash}`;
        return loadingState(chartId);
      }
    }

    return defaultFence(tokens, idx, options, env, self);
  };
}

// Add debounce function
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// Create a module-level singleton to manage chart states
const VegaChartManager = {
  renderedCharts: new Map(),
  renderingLocks: new Set(),
  
  isChartRendered(chartId) {
    return this.renderedCharts.has(chartId);
  },
  
  setChart(chartId, view) {
    this.renderedCharts.set(chartId, view);
  },
  
  removeChart(chartId) {
    this.renderedCharts.delete(chartId);
  },
  
  cleanup() {
    this.renderedCharts.forEach((view, chartId) => {
      try {
        view.finalize();
      } catch (e) {
        console.error(`Failed to cleanup chart ${chartId}:`, e);
      }
    });
    this.renderedCharts.clear();
    this.renderingLocks.clear();
  }
};

export function renderVegaCharts() {
  // Only select containers that haven't been rendered yet
  const containers = document.querySelectorAll('.vega-container:not([data-vega-rendered="true"])');
  
  // Cleanup charts no longer in DOM
  const currentChartIds = new Set(Array.from(containers).map(c => c.id));
  for (const [ chartId, view ] of VegaChartManager.renderedCharts.entries()) {
    if (!currentChartIds.has(chartId)) {
      try {
        view.finalize();
      } catch (e) {
        console.error(`Failed to cleanup removed chart ${chartId}:`, e);
      }
      VegaChartManager.removeChart(chartId);
      VegaChartManager.renderingLocks.delete(chartId);
    }
  }
  
  containers.forEach(async (container) => {
    const chartId = container.id;
    
    // Skip if chart is currently being rendered
    if (VegaChartManager.renderingLocks.has(chartId)) {
      return;
    }

    // Skip if already rendered successfully
    if (container.getAttribute('data-vega-rendered') === 'true' &&
        container.querySelector('svg')) {
      return;
    }

    // Skip containers without spec data
    if (!container.dataset.spec) {
      return;
    }

    try {
      const spec = JSON.parse(decodeURIComponent(container.dataset.spec));
      if (!isValidVegaSpec(spec)) {
        console.log(`Chart ${chartId} has invalid spec`);
        return;
      }

      // Set rendering lock
      VegaChartManager.renderingLocks.add(chartId);
      
      const lang = container.dataset.lang;
      if (lang === 'vega-lite') {
        spec.$schema = 'https://vega.github.io/schema/vega-lite/v5.json';
      }
      
      // Preserve existing content until new chart is ready
      const existingSvg = container.querySelector('svg');
      if (!existingSvg) {
        container.style.display = 'flex';
        container.style.justifyContent = 'center';
        container.style.width = '100%';
      }

      console.log(`Starting render for chart ${chartId}`, VegaChartManager.renderedCharts);
      const view = await vegaEmbed(container, spec, {
        actions: false,
        renderer: 'svg',
        defaultStyle: true,
        centerAlign: true
      });

      const svg = container.querySelector('svg');
      if (svg) {
        svg.style.maxWidth = '100%';
        svg.style.height = 'auto';
      }

      // Update render records
      VegaChartManager.setChart(chartId, view);
      console.log(`Successfully rendered chart ${chartId}`, VegaChartManager.renderedCharts);

      // Mark as rendered
      container.setAttribute('data-vega-rendered', 'true');
      
      // Cache the complete container HTML
      vegaCache.set(chartId, container.outerHTML);
    } catch (e) {
      console.error(`Failed to render chart ${chartId}:`, e);
      container.innerHTML = `<pre>Failed to render chart: ${e.message}</pre>`;
      VegaChartManager.removeChart(chartId);
    } finally {
      // Release rendering lock
      VegaChartManager.renderingLocks.delete(chartId);
    }
  });
}

// Create debounced version of render function
export const debouncedRenderVegaCharts = debounce(renderVegaCharts, 100);

// Modify cleanup function to use VegaChartManager
export function cleanupVegaCharts(clearCache = false) {
  VegaChartManager.cleanup();
  if (clearCache) {
    vegaCache.clear();
  }
}
