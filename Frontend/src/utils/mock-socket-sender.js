import { Server } from 'mock-socket';

const mockServer = new Server('ws://localhost:8080');
const messages = [
  {
    type: 'CONTENT',
    status: 'CONTINUE',//DONE
    task_id: 'ffdfd',
    thread_id: '123',
    question: 'returned question name from server',
    thought: 'China',
    response: 'Hello, world',
    agent: 'MarketResearch',
  },
  {
    task_status: 'started',
    thread: '123',
    response: 'Hello, world. How are you?',
    agent: 'MarketResearch',
    question: 'returned question name from server',
    thought: 'China-Beijing',
  },
  {
    task_status: 'started',
    thread: '123',
    response:
      'Hello, world. How are you? I am doing well. Thank you for asking. How are you?',
    agent: 'MarketResearch',
    question: 'returned question name from server',
    thought: 'America',
  },
  {
    task_status: 'started',
    thread: '123',
    response:
      'Hello, world. How are you? I am doing well. Thank you for asking. How are you? I am doing well. Thank you for asking. How are you?',
    agent: 'MarketResearch',
    question: 'returned question name from server',
    thought: 'America-New York',
  },
  {
    task_status: 'finished',
    thread: '123',
    response:
      'Hello, world. How are you? I am doing well. Thank you for asking. How are you? I am doing well. Thank you for asking. How are you? I am doing well. Thank you for asking. How are you?',
    agent: 'MarketResearch',
    question: 'returned question name from server',
    thought: 'America-New York-123',
  },
];

mockServer.on('connection', (socket) => {
  let count = 0;
  let intervalId = null;

  socket.on('message', data => {
    const parsedData = JSON.parse(data);

    if (parsedData.type === 'entrance' && parsedData.id) {
      console.log(`Entrance received for id: ${parsedData.id}`);

      if (!intervalId) {
        intervalId = setInterval(() => {
          if (count < messages.length) {
            const messageWithTypeInfo = {
              info: messages[count], // Encapsulate original message within `info`
              type: 'thread_result',
            };
            const messageStr = JSON.stringify(messageWithTypeInfo);
            console.log('sending message from mock server...', messageStr);
            socket.send(messageStr);

            if (messages[count].task_status === 'finished') {
              clearInterval(intervalId);
              intervalId = null;
            }
            count++;
          } else {
            clearInterval(intervalId);
            intervalId = null;
            count = 0;
          }
        }, 1000);
      }
    }
  });

  socket.on('close', () => {
    if (intervalId) {
      clearInterval(intervalId);
    }
  });
});
