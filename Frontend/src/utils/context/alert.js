// Might also rename as Toast
import React, { useCallback, useContext, useEffect, useState } from 'react';

import { CheckCircleIcon, ExclamationTriangleIcon, InformationCircleIcon } from '@heroicons/react/20/solid'
import ReactDOM from 'react-dom';

// TODO: ziwen. will only be one alert, might need factory mode in the future
const AlertContext = React.createContext(null);

export const useAlert = () => {
  return useContext(AlertContext);
};

export const AlertProvider = ({ children }) => {
  const [ alert, setAlert ] = useState({ type: null, text: null });

  const showAlert = useCallback((type, text, specifics) => {
    console.log('in provider', { type, text, specifics })
    setAlert({ type, text, specifics });
  }, []);

  const closeAlert = useCallback(() => {
    setAlert({ type: null, text: null, specifics: [] });
  }, []);

  return (
    <AlertContext.Provider value={showAlert}>
      {children}
      {alert.text && ReactDOM.createPortal(
        <Alert type={alert.type} text={alert.text} specifics={alert?.specifics} onDismiss={closeAlert} />,
        document.body
      )}
    </AlertContext.Provider>
  );
};


function Alert({ type, text, specifics, onDismiss }) {
  useEffect(() => {
    console.log('in alert component')
    const timer = setTimeout(() => {
      onDismiss();
    }, 5000); // Auto-dismiss after 5 seconds

    return () => {
      clearTimeout(timer); // Clear the timeout if the alert is manually closed before the timeout
    };
  }, [ onDismiss ]);

  // Modern color schemes with glassmorphism
  const alertStyles = {
    'success': {
      bg: 'bg-white/90 backdrop-blur-lg border-emerald-200/50',
      iconBg: 'bg-emerald-50',
      iconColor: 'text-emerald-600',
      textColor: 'text-emerald-900',
      accentColor: 'text-emerald-700',
      closeHover: 'hover:bg-emerald-50',
      shadow: 'shadow-lg shadow-emerald-500/10'
    },
    'info': {
      bg: 'bg-white/90 backdrop-blur-lg border-blue-200/50',
      iconBg: 'bg-blue-50',
      iconColor: 'text-blue-600',
      textColor: 'text-blue-900',
      accentColor: 'text-blue-700',
      closeHover: 'hover:bg-blue-50',
      shadow: 'shadow-lg shadow-blue-500/10'
    },
    'error': {
      bg: 'bg-white/95 backdrop-blur-lg border-red-300/60',
      iconBg: 'bg-red-50',
      iconColor: 'text-red-600',
      textColor: 'text-red-900',
      accentColor: 'text-red-700',
      closeHover: 'hover:bg-red-50',
      shadow: 'shadow-xl shadow-red-500/20'
    }
  };

  const currentStyle = alertStyles[type];
  const alertSizeClass = type === 'error' ? 'w-[420px]' : 'w-[380px]';
  const iconSizeClass = type === 'error' ? 'h-6 w-6' : 'h-5 w-5';
  const titleSizeClass = type === 'error' ? 'text-base font-semibold' : 'text-sm font-medium';

  const specificsList = specifics ? specifics.map((item, index) => <li key={index}>{item}</li>) : null;
  
  return (
    <div className={`
      z-[100] fixed top-6 right-6 
      ${alertSizeClass} rounded-2xl border ${currentStyle.bg} ${currentStyle.shadow}
      p-5 transform transition-all duration-300 ease-out
      animate-slide-in-from-right
    `}>
      <div className="flex items-center">
        {/* Icon container with modern styling */}
        <div className={`
          flex-shrink-0 p-2 rounded-xl ${currentStyle.iconBg}
          ${type === 'error' ? 'animate-pulse' : ''}
        `}>
          {type === 'success' && <CheckCircleIcon className={`${iconSizeClass} ${currentStyle.iconColor}`} aria-hidden="true" />}
          {type === 'info' && <InformationCircleIcon className={`${iconSizeClass} ${currentStyle.iconColor}`} aria-hidden="true" />}
          {type === 'error' && <ExclamationTriangleIcon className={`${iconSizeClass} ${currentStyle.iconColor}`} aria-hidden="true" />}
        </div>
        
        {/* Content */}
        <div className="ml-4 flex-1 min-w-0">
          <h3 className={`${titleSizeClass} ${currentStyle.textColor} leading-tight`}>
            {text}
          </h3>

          {specifics && specifics.length > 0 && (
            <div className={`mt-3 text-sm ${currentStyle.accentColor} leading-relaxed`}>
              <ul role="list" className="space-y-1.5">
                {specifics.map((item, index) => (
                  <li key={index} className="flex items-start">
                    <span className="flex-shrink-0 w-1.5 h-1.5 bg-current rounded-full mt-2 mr-2 opacity-60"></span>
                    <span className="flex-1">{item}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
        
        {/* Modern close button */}
        <div className="ml-3 flex-shrink-0">
          <button
            type="button"
            onClick={onDismiss}
            className={`
              group relative p-1.5 rounded-lg
              ${currentStyle.textColor} ${currentStyle.closeHover}
              focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-white
              focus:ring-opacity-60 transition-all duration-200
              hover:scale-105
            `}
          >
            <span className="sr-only">Dismiss</span>
            <svg 
              className="h-4 w-4" 
              fill="none" 
              viewBox="0 0 24 24" 
              strokeWidth="2" 
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
}