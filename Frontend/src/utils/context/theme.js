// this theme is for manual theme switch
// In the app, we use theme-system.js instead to switch theme automatically

import React, { createContext, useContext, useEffect, useState } from 'react';

const ThemeContext = createContext();

export const useTheme = () => useContext(ThemeContext);

export const ThemeProvider = ({ children }) => {
  // Initial theme is fetched from localStorage or defaults to 'light'
  const [ theme, setTheme ] = useState(localStorage.getItem('theme') || 'light');

  useEffect(() => {
    // Apply the theme
    document.querySelector('html').setAttribute('data-theme', theme);
    // Store the theme in localStorage
    localStorage.setItem('theme', theme);
  }, [ theme ]);

  // Method to toggle the theme
  const toggleTheme = () => {
    setTheme(currentTheme => currentTheme === 'light' ? 'dark' : 'light');
  };

  return (
    <ThemeContext.Provider value={{ theme, toggleTheme }}>
      {children}
    </ThemeContext.Provider>
  );
};
