import React, { useRef, useState } from 'react';

import { DocumentCheckIcon, PaperClipIcon } from '@heroicons/react/20/solid';

import { uploadFile } from '../../api';
import { useAlert } from '../../utils/context/alert';
import { useLanguage } from '../../utils/context/lang';
import { i18n } from '../i18n/common';

const UploadButton = ({ className, onSelectFile, onFileUploaded, onUploadError, upload, isMini = false }) => {
  const showAlert = useAlert();
  const { lang } = useLanguage();
  const fileInputRef = useRef(null);
  const [ isUploading, setIsUploading ] = useState(false)
  const handleFileSelect = (event) => {
    const files = event.target.files; // Get all selected files
    const maxFileSize = 10 * 1024 * 1024; // 10MB in bytes

    if (files.length > 0) {
      Array.from(files).forEach((file) => {
        if (file.size > maxFileSize) {
          showAlert('error', `${file.name}`+i18n.FILE_SIZE_EXCEEDED[lang]);
        } else {
          onSelectFile(file); // Notify parent component about each file selection
          handleUploadFile(file);
        }
      });
      setIsUploading(true);
    }
  };

  const handleButtonClick = () => {
    fileInputRef.current.click();
  };

  const handleUploadFile = async (file) => {
    const formData = new FormData();
    // formData.append(
    //   'csrfmiddlewaretoken',
    //   // 'l9W4FDlDzHl4C2nRtq973ThO4FehdIgKer2Q4Wle4PvdsSmmMhDcL7pQoPYxdX00'
    //   ''
    // );
    formData.append('id', '');
    formData.append('upload', file);

    const onUploadProgress = (progressEvent) => {
      const percentCompleted = Math.round(
        (progressEvent.loaded * 100) / progressEvent.total
      );
      // Optionally, you can use this to update progress in the parent component
      // onUploadProgress(percentCompleted);
    };

    try {
      const response = await uploadFile(formData, onUploadProgress);
      onFileUploaded(file, response.data); // Notify parent component about the upload completion
      setIsUploading(false)
    } catch (error) {
      showAlert('error', 'File upload error');
      console.error('Upload error:', error);
      onUploadError(error);
      setIsUploading(false)
    }
  };

  return (
    <div className={className}>
      <button
        type="button"
        onClick={handleButtonClick}
        className="group h-full flex justify-center items-center inline-flex items-center rounded-full pl-4 pr-3 py-2 text-left text-neutral-content"
        disabled={isUploading}
      >

        <PaperClipIcon
          className="h-5 w-5 group-hover:text-primary-content"
          aria-hidden="true"
        />

        {!isMini && <span className="text-sm italic text-primary-content group-hover:text-primary-content">
          Attach a file
        </span>}
      </button>
      <input
        type="file"
        ref={fileInputRef}
        className="hidden"
        accept=".pdf,.doc,.docx,.pptx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,.pages"
        multiple
        onChange={handleFileSelect}
      />
      {/* {upload && (
        <div>
          Upload complete: <a href={upload.upload}>{upload.name}</a>
        </div>
      )} */}
    </div>
  );
};

export default UploadButton;
