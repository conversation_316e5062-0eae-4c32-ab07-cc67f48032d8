import CustomButton from '../../Common/Components/CustomButton.js';
import { usePro } from '../../utils/context/pro.js';
import { ReactComponent as DeepseekSelect } from '../assets/deepseek-select.svg';
import { ReactComponent as Deepseek } from '../assets/deepseek.svg';

export default function DeepSeekButton() {
  const { isDeepSeekOn, switchDeepSeek } = usePro();

  const icon = <Deepseek className="h-3" />;
  const selectIcon = <DeepseekSelect className="h-3" />;
    
  // return (
  //   <CustomButton
  //     btnClassName="ml-2"
  //     isSelected={isDeepSeekOn} 
  //     onBtnClick={switchDeepSeek} 
  //     icon={icon}
  //     selectedIcon={selectIcon} 
  //   />
  // );
  return null;

}
