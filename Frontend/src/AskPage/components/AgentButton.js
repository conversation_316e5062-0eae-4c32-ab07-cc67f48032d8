import CustomButton from '../../Common/Components/CustomButton.js';
import { usePro } from '../../utils/context/pro.js';
import { ReactComponent as AgentSelect } from '../assets/agent-select.svg';
import { ReactComponent as Agent } from '../assets/agent.svg';

export default function AgentButton() {
  const { isAgentOn, switchAgent } = usePro();

  const icon = <Agent className="w-5 h-5 mr-[1px]" />;
  const selectIcon = <AgentSelect className="w-5 h-5 mr-[1px]" />;
  
  return (
    <CustomButton 
      isSelected={isAgentOn} 
      onBtnClick={switchAgent} 
      text="Agent" 
      icon={icon}
      selectedIcon={selectIcon} 
    />
  );
}
