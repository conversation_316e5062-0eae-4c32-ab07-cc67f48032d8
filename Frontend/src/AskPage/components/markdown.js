/* eslint-disable no-useless-escape */
import { memo, useEffect, useRef, useState } from 'react';

import mermaid from 'mermaid';
import { createRoot } from 'react-dom/client';

import ReportDownloadBtn from '../../Common/Components/ReportDownloadBtn';
import { cleanupVegaCharts , debouncedRenderVegaCharts, renderVegaCharts, simpleHash } from '../../utils/markdown-plugin/markdown-it-vega';
import { extractThinkTag, processMarkdownToCleanHTML } from '../../utils/markdown-to-html';

const DisplayFormattedText = memo(({ text, onReferenceClick }) => {
  const contentRef = useRef(null);
  const thinkRef = useRef(null);
  const lastTextRef = useRef('');
  const renderedChartsRef = useRef(new Set());
  // Add WeakMap to store the processing functions corresponding to each link
  const clickHandlersMap = useRef(new WeakMap());

  useEffect(() => {
    // Initialize mermaid for fresh rendering
    mermaid.initialize({
      startOnLoad: false,
      theme: 'default',
      securityLevel: 'strict',
      suppressErrorRendering: true,
    });

    return () => {
      mermaid?.cleanup && mermaid.cleanup();
    };
  }, []);

  useEffect(() => {
    if (!contentRef.current || !onReferenceClick) return;

    // Find new charts that haven't been rendered yet
    const newCharts = Array.from(contentRef.current.querySelectorAll('.vega-container'))
      .filter(chart => !renderedChartsRef.current.has(chart.id) &&
                      !chart.getAttribute('data-vega-rendered'));
    
    if (newCharts.length > 0) {
      newCharts.forEach(chart => renderedChartsRef.current.add(chart.id));
      renderVegaCharts();
    }

    // Store last rendered text
    lastTextRef.current = text;

    return () => {
      // Only cleanup when component is fully unmounted
      if (!text) {
        cleanupVegaCharts(true);
        renderedChartsRef.current.clear();
      }
    };
  }, [ text, onReferenceClick ]);

  useEffect(() => {
    if (!contentRef.current || !onReferenceClick) return;

    // create Intersection Observer
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach(async (entry) => {
          if (entry.isIntersecting) {
            const diagram = entry.target;

            if (diagram.getAttribute('data-processed') === 'true') return;

            try {
              if (!diagram.getAttribute('data-original-text')) {
                diagram.setAttribute('data-original-text', diagram.textContent);
              }
              
              const definition = diagram.textContent;
              const contentHash = simpleHash(definition.substring(0, 10));
              const uniqueId = `mermaid-diagram-${contentHash}`;
              const startTime = Date.now();
              
              console.log(`准备生成 ${uniqueId}`, startTime);
              const { svg } = await mermaid.render(uniqueId, definition);
              const endTime = Date.now();
              
              console.log(`${uniqueId} 图表渲染完成,耗时: ${endTime - startTime}ms`);
              diagram.setAttribute('data-processed', 'true');
              diagram.innerHTML = svg;

              // Cancel observation after rendering is complete
              observer.unobserve(diagram);
            } catch (error) {
              console.error('Failed to render mermaid diagram:', error);
              const originalText = diagram.getAttribute('data-original-text');
              if (originalText) {
                diagram.textContent = originalText;
              }
            }
          }
        });
      },
      {
        root: null,
        rootMargin: '100px', // Start loading 100px in advance
        threshold: 0.1 // As long as 10% enters the viewport, it will trigger
      }
    );

    // Get all mermaid charts and observe them
    const mermaidDiagrams = contentRef.current.querySelectorAll('.mermaid');
    mermaidDiagrams.forEach(diagram => {
      if (diagram.getAttribute('data-processed') !== 'true') {
        observer.observe(diagram);
      }
    });

    // Clean up function
    return () => {
      mermaidDiagrams.forEach(diagram => {
        observer.unobserve(diagram);
      });
      observer.disconnect();
    };
  }, [ text, onReferenceClick ]);

  useEffect(() => {
    if (!contentRef.current || !onReferenceClick) return;

    // Hydrate download buttons
    // \`\`\`bucketdownload
    // status=complete attachmentKey=investment-reports/<EMAIL>/PFE_20250313_062501.zip threadId=baccf704-3c79-4dd5-930b-badaec86a76f
    // \`\`\`
    const downloadContainers = contentRef.current.querySelectorAll('.react-download-btn');
    downloadContainers.forEach(container => {
      const props = JSON.parse(container.dataset.markdownProps || '{}');
      const root = createRoot(container);
      root.render(<ReportDownloadBtn {...props} />);
    });

    // Add click handlers for reference links
    const links = Array.from(contentRef.current.querySelectorAll('a') || []).concat(Array.from(thinkRef?.current?.querySelectorAll('a') || []));
    links.forEach(link => {
      // Remove the previous event handling function (if any) first
      const oldHandler = clickHandlersMap.current.get(link);
      if (oldHandler) {
        link.removeEventListener('click', oldHandler);
      }

      const handleClick = (e) => {
        // Check if this is a reference link (matches n pattern)
        const referenceMatch = link.textContent.match(/^(\d+)$/);
        const pdfMatch = link.href.match(/\.pdf/i) && link.href.match(/noahai-online\.obs\.cn-east-3\.myhuaweicloud\.com/i);
        if (referenceMatch) {
          e.preventDefault();
          const referenceId = referenceMatch[1];
          console.log('referenceId', referenceId);
          onReferenceClick(referenceId - 1); // referenceId-1 is referenceIndex
        }
        if (pdfMatch) {
          e.preventDefault();
          onReferenceClick(link.href);
        }
      };

      // Save new processing function
      clickHandlersMap.current.set(link, handleClick);
      link.addEventListener('click', handleClick);

      // Add hover effect for reference links
      if (link.textContent.match(/^\[\d+\]$/)) {
        link.classList.add('reference-link');
        link.title = 'Click to view reference';
      }
    });

    // Add copy button to code blocks
    const codeBlocks = contentRef.current.querySelectorAll('pre code');
    codeBlocks.forEach(block => {
      const copyButton = document.createElement('button');
      copyButton.innerHTML = 'Copy';
      copyButton.className = 'absolute right-2 top-2 px-2 py-1 rounded bg-base-100 text-sm opacity-50 hover:opacity-100';
      copyButton.onclick = () => {
        navigator.clipboard.writeText(block.textContent);
        copyButton.innerHTML = 'Copied!';
        setTimeout(() => copyButton.innerHTML = 'Copy', 2000);
      };
      block.parentElement.style.position = 'relative';
      block.parentElement.appendChild(copyButton);
    });

    return () => {
      // Cleanup React roots
      downloadContainers.forEach(container => {
        const root = container._reactRoot;
        if (root) {
          root.unmount();
        }
      });
      // Clean up events using saved handling functions
      links.forEach(link => {
        const handler = clickHandlersMap.current.get(link);
        if (handler) {
          link.removeEventListener('click', handler);
          clickHandlersMap.current.delete(link);
        }
      });
    };
  }, [ text, onReferenceClick ]);

  const { think, mainResponse } = extractThinkTag(text || '');
  const mainCleanHTML = processMarkdownToCleanHTML(mainResponse);
  const thinkCleanHTML = processMarkdownToCleanHTML(think);

  const FormattedText = (ref, cleanHTML, formattedClass) => {
    return <div
      ref={ref}
      className={`prose prose-base max-w-none
        prose-h1:text-xl prose-h1:text-black-1 prose-h1:mt-4 prose-h1:mb-0 prose-h1:font-bold prose-h1:no-underline
        prose-h2:text-lg prose-h2:text-black-1 prose-h2:mt-2 prose-h2:mb-0 prose-h2:mt-4 prose-h2:no-underline
        prose-h3:text-base prose-h3:text-black-1 prose-h3:mt-2 prose-h3:mb-0 prose-h3:mt-3 prose-h3:no-underline
        prose-h4:text-base prose-h4:text-black-1 prose-h4:mt-2 prose-h4:mb-0 prose-h4:mt-0 prose-h4:no-underline
        prose-p:text-black-1 prose-p:my-0 prose-p:no-underline 
        prose-li:text-black-1 prose-li:mt-2 prose-li:leading-relaxed 
        prose-ul:pl-5 prose-ul:my-2 prose-ol:pl-5
        prose-li:marker:text-black-1
        prose-pre:bg-base-300 prose-pre:p-4 prose-pre:rounded-lg
        prose-code:text-secondary-content prose-code:bg-base-300 prose-code:px-1 prose-code:rounded
        hover:prose-a:text-black-1 hover:prose-a:underline prose-a:transition-colors
        [&_.reference-link]:text-primary 
        hover:[&_.reference-link]:text-primary-focus 
        [&_.reference-link]:cursor-pointer 
        [&_.reference-link]:border-b 
        [&_.reference-link]:border-dotted
        [&_table]:border-collapse [&_table]:w-[calc(100%-2px)] [&_table]:ml-[1px] [&_table]:my-4 [&_table]:rounded-xl
        [&_table]:overflow-hidden [&_table]:ring-1 [&_table]:ring-[#EEEEEE] [&>div]:overflow-x-auto [&>div>table]:min-w-full
        [&_tr]:border-b [&_tr]:border-[#EEEEEE] [&_thead]:border-[#EEEEEE] [&_thead]:bg-[#EEEEEE] [&_tr:last-child]:border-b-0
        prose-td:p-2 prose-td:whitespace-normal prose-td:break-all prose-td:text-black prose-td:text-sm
        prose-th:p-2 prose-th:whitespace-normal prose-th:break-all prose-th:text-black prose-th:font-medium
        prose-thead:bg-[#F8F8F8] ${formattedClass}`}
      dangerouslySetInnerHTML={{ __html: cleanHTML }}
    />
  }

  return (
    <>
      {
        (think && thinkCleanHTML) &&  FormattedText(thinkRef, thinkCleanHTML, 'bg-primary/10 px-2 border-l-2 border-primary block opacity-60 text-sm mt-2 mb-4')
      }
      {FormattedText(contentRef, mainCleanHTML, '')}
    </>
  );
}, (prevProps, nextProps) => {
  // Re render only when text or onReferenClick changes
  return prevProps.text === nextProps.text && 
         prevProps.onReferenceClick === nextProps.onReferenceClick;});

DisplayFormattedText.displayName = 'DisplayFormattedText';

export default DisplayFormattedText;
