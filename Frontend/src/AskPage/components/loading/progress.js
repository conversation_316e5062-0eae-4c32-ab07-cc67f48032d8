import React from 'react';

const Progress = ({ stages, stage_index }) => {
  const progressPercentage = ((stage_index + 1) / stages.length) * 100;

  return (
    <div>
      <h4 className="sr-only">Status</h4>
      <p className="text-sm font-medium text-gray-900">{stages[stage_index]}...</p>
      <div aria-hidden="true" className="mt-6">
        <div className="overflow-hidden rounded-full bg-gray-200">
          <div style={{ width: `${progressPercentage}%` }} className="h-2 rounded-full bg-indigo-600" />
        </div>
        <div className="mt-6 grid grid-cols-3 text-sm font-medium text-gray-600 sm:grid-cols-4">
          {stages.map((stage, index) => (
            <div
              key={index}
              className={`text-center ${index <= stage_index ? 'text-indigo-600' : ''}`}
            >
              {stage}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Progress;