import { Segmented, Tabs } from 'antd';

import CustomButton from '../../Common/Components/CustomButton.js';
import Tooltip from '../../Common/Components/Tooltip.js';
import TransparentSelect from '../../Common/Components/TransparentSelect.js';
import { useLanguage } from '../../utils/context/lang.js';
import { usePro } from '../../utils/context/pro.js';
import { ReactComponent as AgentSelect } from '../assets/agent-select.svg';
import { ReactComponent as Agent } from '../assets/agent.svg';
import { ReactComponent as Health } from '../assets/health.svg';
import { ReactComponent as Pubmed } from '../assets/pubmed.svg';
import { ReactComponent as SearchSelectIcon } from '../assets/search-select.svg';
import { ReactComponent as Search } from '../assets/search.svg';
import { ReactComponent as WebSelect } from '../assets/web-select.svg';
import { ReactComponent as Web } from '../assets/web.svg';
import { agents as solutions } from '../const/agent.js';
import { i18n } from '../i18n/common';

export function AgentButton({ isSelected, onBtnClick }) {
  const icon = <Agent className="w-5 h-5 mr-[1px]" />;
  const selectIcon = <AgentSelect className="w-5 h-5 mr-[1px]" />;
  
  return (
    <CustomButton 
      isSelected={isSelected} 
      onBtnClick={onBtnClick} 
      text="Agent" 
      icon={icon}
      selectedIcon={selectIcon} 
    />
  );
}

export function SearchButton({ isSelected, onBtnClick }) {
  const icon = <Web className="w-5 h-5 mr-[1px]" />;
  const selectIcon = <WebSelect className="w-5 h-5 mr-[1px]" />;
  
  return (
    <CustomButton 
      isSelected={isSelected} 
      onBtnClick={onBtnClick} 
      text="Search" 
      icon={icon}
      selectedIcon={selectIcon} 
    />
  );
}

export function SearchSelect({ value, onChange, showSelect = false }) {
  const { lang } = useLanguage();

  const inLandingSolutions = solutions.filter((solution) => solution.inLanding);

  if (!showSelect) return null;

  return (
    <TransparentSelect
      className="w-auto"
      dropdownStyle={{
        minWidth: '114px',
      }}
      value={value}
      optionLabelProp="label"
      onChange={onChange}
      labelInValue
      needBlur={true}
      labelRender={(label) => {
        const Icon = inLandingSolutions.find(item => item.category === label.value)?.icon;
        return (
          <div className="flex items-center justify-center flex-1 font-medium text-sm !text-black-1">
            <Icon className="mr-1" />
            <span>{label.title}</span>
          </div>
        );
      }}
      items={inLandingSolutions.map(item => {
        return { value: item.category, icon: item.icon, label: item[`role_${lang}`], tip: item[`role_${lang}_tip`], current: value === item.category  }
      } )}
    />
  );
}

export function SearchSegmented({ topTips = false, alignValue, onChange }) {
  const isAgentSelect = alignValue === 'planning';
  const AgentIcon = isAgentSelect ? AgentSelect : Agent;
  const SearchIcon = isAgentSelect ? Search : SearchSelectIcon;
  const { lang } = useLanguage();

  return (
    <div className="flex items-center p-[1px] rounded-xl bg-[#F7F7F7] relative">
      <div 
        className="absolute h-[30px] bg-white rounded-[10px] border-[0.5px] border-[#51CBB5] transition-all duration-200 shadow-sm" 
        style={{ 
          width: `${isAgentSelect ? '84px' : '90px'}`,
          transform: `translateX(${isAgentSelect ? '0px' : '83px'})`
        }}
      />
      <div className="relative group/tooltip">
        <div
          className={`group flex items-center py-1 px-3 space-x-1 rounded-lg cursor-pointer z-10 transition-all duration-300 ${
            isAgentSelect ? 'text-noah-theme' : 'text-gray-1 hover:text-black'
          }`}
          onClick={() => onChange('planning')}
        >
          <AgentIcon className={`w-4 h-4 transition-opacity duration-300 ease-in-out group-hover:opacity-100 ${isAgentSelect ? 'opacity-100' : 'opacity-60'}`} />
          <div className="font-medium text-sm leading-[22px]">Agent</div>
        </div>
        <Tooltip
          tipClassName={`left-1/2 -translate-x-1/2 ${topTips ? '-top-1 -translate-y-full' : 'top-full translate-y-1'}`}
          width="300px"
          text={i18n.AGENT_SEGMENTED[lang]}
        />
      </div>
      <div className="relative group/tooltip">
        <div
          className={`group flex items-center py-1 px-3 space-x-1 rounded-lg cursor-pointer z-10 transition-all duration-300 ${
            isAgentSelect ? 'text-gray-1 hover:text-black' : 'text-noah-theme'
          }`}
          onClick={() => onChange('mindsearchofficialsite')}
        >
          <SearchIcon className={`w-4 h-4 transition-opacity duration-300 ease-in-out group-hover:opacity-100 ${isAgentSelect ? 'opacity-60' : 'opacity-100'}`} />
          <div className="font-medium text-sm leading-[22px]">Search</div>
        </div>
        <Tooltip
          tipClassName={`left-1/2 -translate-x-1/2 ${topTips ? '-top-1 -translate-y-full' : 'top-full translate-y-1'}`}
          width="300px"
          text={i18n.SEARCH_SEGMENTED[lang]}
        />
      </div>
    </div>
  );
}