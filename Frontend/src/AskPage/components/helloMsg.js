// export a component receives item and index as props
import { ArrowLongRightIcon } from '@heroicons/react/24/outline';

import { useLanguage } from '../../utils/context/lang';
import { agents, BIOMEDICAL_TYPE, CLINICAL_TYPE } from '../const/agent';

import ChatSection from './ChatSection';

export function HelloMsg({ agentType,onSubmit }) {
  const { lang } = useLanguage();
  const agent = agents.filter((agent) => agent.category === agentType).at(0);

  const questions = agent?.exampleQuestions?.map((question,i) => {
    return (<div key={i} className="flex flex-column mb-4">
      <button
        type="button"
        className="inline-block px-4 py-2 mr-2 text-xs font-semibold text-secondary-content bg-secondary rounded-full hover:bg-accent-hover"
        onClick={()=>{onSubmit(question)}}
      >
        {question}
      </button>
    </div>)
  });
  return (
    <div className="px-6 lg:px-8">
      <ChatSection imageUrl={agent?.imageUrl} role={agent[`role_${lang}`]} >
        {`I am your AI assistant for ${agent.name}. You can ask me questions like:`}
        <div className="mt-2 lg:w-5/6 flex flex-wrap">
          {questions}
        </div>
      </ChatSection>
    </div>
  );
}
