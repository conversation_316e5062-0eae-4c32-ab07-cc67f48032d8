import { Fragment, useEffect, useState } from 'react';

import { Switch } from '@headlessui/react';
import { GlobeAltIcon } from '@heroicons/react/24/outline';
import { useLocation } from 'react-router-dom';

import { useAlert } from '../../utils/context/alert'; // Adjust path as needed
import { useLanguage } from '../../utils/context/lang.js';
import { usePro } from '../../utils/context/pro.js';
import { useUser } from '../../utils/context/user';
import { placeholder as placeholderI18n } from '../const/agent.js';
import { i18n } from '../i18n/common';

import { SearchSegmented, SearchSelect } from './ChatInputComponents.js';
import DeepSeekButton from './DeepSeekButton.js';
import FileThumbnail from './fileThumbnail'; // Ensure correct import path
import SendButton from './SentButton.js';
import UploadButton from './uploadBtn';


const maxRows = 6;
export default function SingleLineInput({
  className,
  onSubmit,
  enableUpload = false,
  disabled = false,
  showDeepseekBtn = false,
  onFileCountChange,
  placeholder='write a message...',
  slotContent = null,
  isSending = false,
  focusArea = 'planning',
  setFocusArea = () => {},
  showSearchSegmented = false,
}) {
  const { lang } = useLanguage();
  const { isProOn, switchPro, isWebSearchOn, switchWebSearch, setWebSearch } = usePro();
  // const { user } = useUser();
  const [ textInput, setTextInput ] = useState('');
  const [ uploadInfos, setUploadInfos ] = useState([]);
  const { pathname } = useLocation();

  useEffect(() => {
    // TODO:liyang update path
    const needOpenWebSearch = pathname.startsWith('/discover') || (pathname.startsWith('/ask') && !pathname.startsWith('/ask/history'));
    // The discover & ask page defaults to opening WebSearch, while other pages default to closing WebSearch.
    if (needOpenWebSearch && !isWebSearchOn) {
      setWebSearch(true);
    } else if (!needOpenWebSearch && isWebSearchOn) {
      setWebSearch(false);
    }
  }, [ pathname ]);

  // const [ switchValue, setSwitchValue ] = useState(false);

  // force switch to false
  // useEffect(() => {
  //   // alert(111)
  //   if (forceSwitchValue !== undefined) {
  //     setSwitchValue(forceSwitchValue);
  //   }
  // }, [ forceSwitchValue ]);

  // useEffect(() => {
  //   setSwitchValue(user ? user.price_plan === 'pro' : false);
  // }, [ user ]);

  useEffect(() => {
    onFileCountChange(uploadInfos.length);
  }, [ uploadInfos ]);

  const submitQuestion = () => {
    console.log('submit question ',{ textInput, uploadInfos, isProOn });
    onSubmit(textInput, uploadInfos, isProOn);
    if (isSending) return;

    setUploadInfos([]);
    setTextInput('');
    const textarea = document.querySelector('.resize-none'); // Use a more specific selector if needed
    const container = textarea.parentNode;

    // textarea.style.height = 'auto';  // Reset the height of textarea
    container.style.height = '60px'; // Reset the height of the container to the initial height
  };

  useEffect(() => {
    const handleKeyPress = (e) => {
      if (
        e.key === 'Enter' &&
        !e.shiftKey &&
        textInput.length > 0 &&
        !e.isComposing &&
        !disabled
      ) {
        e.preventDefault(); // Prevent default action to avoid submitting form
        submitQuestion();
      }
    };
    document.addEventListener('keydown', handleKeyPress);
    return () => {
      document.removeEventListener('keydown', handleKeyPress);
    };
  }, [ textInput, disabled ]); // Include `disabled` to ensure the effect updates if the disabled state changes

  const handleSelectFile = (file) => {
    console.log('File selected:', file.name);
    setUploadInfos((prev) => [ ...prev, { name: file.name } ]);
  };
  const handleTextChange = (e) => {
    const textarea = e.target;
    const container = textarea.parentNode; // Assuming the parent node is the container div
    container.style.height = 'auto';
    // Calculate effective height considering padding
    const computedStyle = window.getComputedStyle(textarea);
    const paddingTop = parseInt(computedStyle.paddingTop, 10);
    const paddingBottom = parseInt(computedStyle.paddingBottom, 10);
    const borderHeight =
      parseInt(computedStyle.borderTopWidth, 10) +
      parseInt(computedStyle.borderBottomWidth, 10);

    // Calculate the height to be the greater of scrollHeight (less padding and border) or the minimum height
    const desiredHeight = Math.max(
      textarea.scrollHeight - paddingTop - paddingBottom - borderHeight,
      60
    );

    container.style.height = `${desiredHeight}px`;
    setTextInput(textarea.value); // Update the state with new input
  };
  const handleFileUploaded = (file, uploadData) => {
    console.log('selectedFile', file);
    console.log('File uploaded:', uploadData);
    setUploadInfos((prev) =>
      prev.map((info) =>
        info.name === file.name
          ? {
            ...info,
            upload: uploadData.upload,
            id: uploadData.id,
            name: file.name,
          }
          : info
      )
    );
  };

  const deleteUpload = (fileId) => {
    setUploadInfos((prev) => prev.filter((info) => info.id !== fileId));
  };

  // const onModelChange = (newVal) => {
  //   // if (newVal) {
  //   //   if (user?.price_plan === 'pro') {
  //   //     setSwitchValue(newVal);
  //   //   } else {
  //   //     // optimize this part
  //   //     showAlert('error', i18n.UPGRADE_HINT[lang]);
  //   //   }
  //   // } else {
  //   setSwitchValue(newVal);
  //   onSwitchChange(newVal);
  //   // }
  // };

  return (
    <div
      className={`form-control border border-[#EDEDED] px-5 rounded-3xl bg-base-100 z-10 ${className}`}
    >
      {/* Render optional slotContent if provided */}
      {slotContent && (
        <div className="w-full">
          {slotContent}
        </div>
      )}
      
      <div className="flex overflow-x-scroll scrollbar-none">
        {uploadInfos.map((upload, index) => (
          <FileThumbnail
            key={upload.id || upload.name}
            upload={upload}
            onDelete={() => deleteUpload(upload.id)}
          />
        ))}
      </div>
      <div className="relative input-group flex items-center justify-between h-[60px] max-h-[160px]">
        {/* {enableUpload && (
          <UploadButton
            className=""
            onSelectFile={handleSelectFile}
            onFileUploaded={handleFileUploaded}
            onUploadError={(error) => showAlert('error', 'File upload error')}
            upload={uploadInfos}
            isMini={true}
          />
        )} */}
        <textarea
          id="textarea"
          placeholder={placeholder}
          rows={1}
          className={`relative scrollbar-none flex-1 py-4 px-0 h-full min-h-[60px] max-h-[160px] resize-none leading-6 input border-none focus:outline-none focus:shadow-none focus:ring-0 overflow-scroll ${showDeepseekBtn ? 'w-[calc(100%-12rem)]' : 'w-[calc(100%-5rem)]'}`}
          value={textInput}
          onChange={handleTextChange}
        />
        <div className="relative flex items-center gap-1">
          {
            showSearchSegmented ? (
              <div className="relative flex items-center gap-1">
                <SearchSegmented topTips={true} alignValue={focusArea} onChange={(e) => setFocusArea(e)} />
              </div>
            ) : (
              <div className="relative z-50 group">
                <div className="absolute bottom-full left-1/2 -translate-x-1/2 mb-2 hidden group-hover:block z-[100]">
                  <div className="bg-gray-900 text-white text-sm rounded-md py-1 px-2 whitespace-nowrap">
                web search
                    <div className="absolute top-full left-1/2 -translate-x-1/2 border-4 border-transparent border-t-gray-900"></div>
                  </div>
                </div>
            
                <GlobeAltIcon
                  className={`w-5 h-5 cursor-pointer ${isWebSearchOn ? 'text-blue-400 hover:text-blue-500' : 'text-base-content/50 hover:text-base-content/60'}`}
                  onClick={switchWebSearch}
                />
              </div>
            )
          }
        </div>
        
        <SendButton
          onClick={submitQuestion}
          disabled={textInput.length === 0 || disabled}
          isSending={isSending}
        />
      </div>
    </div>
  );
}
