import React, { memo } from 'react';

import DisplayFormattedText from '../../markdown';
import './styles/PlanStepTimeline.css';

/**
 * PlanStepTimeline - A simplified component for displaying plan information
 * 
 * @param {Object} planUpdate - The plan update object containing plan steps
 * @param {Function} onReferenceClick - Function to handle reference clicks
 * @returns {JSX.Element|null} - The rendered plan info or null if no plan
 */
const PlanStepTimeline = memo(({ planUpdate, onReferenceClick }) => {
  if (!planUpdate || !planUpdate.plan || !Array.isArray(planUpdate.plan) || planUpdate.plan.length === 0) {
    return null;
  }

  // Get the main step and its description
  const mainStep = planUpdate.plan[0] || {};
  const description = mainStep.reason || 'Executing the next part of the plan';
  
  // Find the current active step if any
  const currentStep = planUpdate.plan.find(step => step.status === 'doing');
  const hasError = planUpdate.plan.some(step => step.status === 'error');
  
  // Get completed steps with results
  const completedSteps = planUpdate.plan.filter(step => 
    step.status === 'done' && step.result
  );

  return (
    <div className="w-full border-b border-gray-200 p-3">
      <div className="plan-container">
        {/* Main plan description */}
        <div className="plan-header">
          <h4 className="text-base font-medium">{mainStep.tool || 'Current Plan'}</h4>
          <p className="text-sm text-gray-600">{description}</p>
        </div>
        
        {/* Current status */}
        {currentStep && (
          <div className="plan-status mt-2">
            <span className="text-blue-500 text-sm">
              In progress: {currentStep.tool || 'Processing...'}
            </span>
          </div>
        )}
        
        {/* Error indicator */}
        {hasError && (
          <div className="plan-error mt-2">
            <span className="text-red-500 text-sm">
              An error occurred during plan execution
            </span>
          </div>
        )}
        
        {/* Show all steps when no step is in progress */}
        {!currentStep && planUpdate.plan.length > 1 && (
          <div className="all-steps mt-3">
            <h5 className="text-sm font-medium">Plan Steps:</h5>
            <ul className="text-xs mt-1">
              {planUpdate.plan.map((step, index) => (
                <li key={index} className="mb-1 flex items-start">
                  <span className={`mr-2 ${
                    step.status === 'done' ? 'text-green-500' : 
                      step.status === 'error' ? 'text-red-500' : 'text-gray-500'
                  }`}>
                    {step.status === 'done' ? '✓' : 
                      step.status === 'error' ? '✗' : '○'}
                  </span>
                  <span>{step.tool || `Step ${index + 1}`}</span>
                </li>
              ))}
            </ul>
          </div>
        )}
        
        {/* Show latest result if available */}
        {completedSteps.length > 0 && completedSteps[completedSteps.length - 1].result && (
          <div className="plan-result mt-3 p-2 border border-gray-200 rounded bg-gray-50">
            <h5 className="text-sm font-medium">Latest Result:</h5>
            <div className="text-xs max-h-24 overflow-y-auto">
              <DisplayFormattedText 
                text={completedSteps[completedSteps.length - 1].result} 
                onReferenceClick={onReferenceClick} 
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
});

// Add display name for better debugging
PlanStepTimeline.displayName = 'PlanStepTimeline';

export default PlanStepTimeline; 