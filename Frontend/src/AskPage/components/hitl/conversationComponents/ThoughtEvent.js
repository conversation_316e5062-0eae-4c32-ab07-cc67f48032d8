import React, { useState } from 'react';

// Component for rendering a thought event with minimal modern design
const ThoughtEvent = ({ thought }) => {
  const [ isExpanded, setIsExpanded ] = useState(false);
  
  // Get a preview of the thought content (first ~100 characters)
  const previewContent = thought.content?.length > 100 
    ? `${thought.content.substring(0, 100).trim()}...` 
    : thought.content;
  
  return (
    <div className="mb-3 border-l-2 border-amber-300 pl-3 transition-all duration-200">
      <div 
        className="flex items-center cursor-pointer py-1 group"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex-1">
          <div className="flex items-center">
            <div className="h-2 w-2 rounded-full bg-amber-400 mr-2"></div>
            <h3 className="text-sm font-medium text-amber-700">Thought Process</h3>
            <button className="ml-2 text-xs text-amber-500 opacity-70 group-hover:opacity-100">
              {isExpanded ? 'Hide' : 'Show'}
            </button>
          </div>
          {!isExpanded && (
            <p className="text-xs text-neutral-500 mt-1 ml-4 italic line-clamp-1">{previewContent}</p>
          )}
        </div>
      </div>
      
      {isExpanded && (
        <div className="pt-1 pb-2 pl-4 mt-1 text-sm text-neutral-600 whitespace-pre-wrap bg-amber-50/30 rounded-r-md border-r border-t border-b border-amber-100">
          {thought.content}
        </div>
      )}
    </div>
  );
};

export default ThoughtEvent; 