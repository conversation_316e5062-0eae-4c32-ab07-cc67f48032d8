import React from 'react';

import DisplayFormattedText from '../../markdown';

// Component for rendering a tool result with minimal modern design
const ToolResultEvent = ({ tool, onReferenceClick }) => {
  // Determine status styling
  let dotColor = 'bg-neutral-400';
  let textColor = 'text-neutral-600';
  let borderColor = 'border-neutral-200';
  
  if (tool.status === 'done') {
    dotColor = 'bg-emerald-400';
    textColor = 'text-emerald-600';
    borderColor = 'border-emerald-200';
  } else if (tool.status === 'running') {
    dotColor = 'bg-amber-400';
    textColor = 'text-amber-600';
    borderColor = 'border-amber-200';
  } else if (tool.status === 'error') {
    dotColor = 'bg-rose-400';
    textColor = 'text-rose-600';
    borderColor = 'border-rose-200';
  }
  
  return (
    <div className="mb-3 relative">
      <div className="flex items-center mb-1">
        <div className={`h-2 w-2 rounded-full ${dotColor} mr-2`}></div>
        <span className={`text-sm font-medium ${textColor}`}>{tool.name}</span>
        
        {tool.status && (
          <span className={`ml-2 text-xs px-1.5 py-0.5 rounded-full bg-neutral-100 ${textColor}`}>
            {tool.status}
          </span>
        )}
      </div>
      
      {/* Tool result content */}
      <div className={`pl-4 ml-2 border-l ${borderColor} py-1`}>
        <div className="text-sm text-neutral-600">
          <DisplayFormattedText 
            text={tool.result} 
            onReferenceClick={onReferenceClick}
          />
        </div>
      </div>
    </div>
  );
};

export default ToolResultEvent; 