import React, { useEffect, useRef, useState } from 'react';

import { Input, Tag } from 'antd';
import { Link, useNavigate } from 'react-router-dom';

import { useLanguage } from '../../../../utils/context/lang';
import { useModal } from '../../../../utils/context/modal';
import { useUser } from '../../../../utils/context/user';
import { ReactComponent as ChevronRight } from '../../../assets/chevron-right.svg';
import { i18n } from '../../../i18n/hitl';

const REDIRECT_TOOLS = [ 'Clinical-Trial-Result-Analysis', 'Drug-Analysis', 'Catalyst-Event-Analysis', 'Clinical-Guideline-Analysis' ];
const REDIRECT_TOOL_PATHS = {
  'Clinical-Trial-Result-Analysis': '/tool/clinical-result',
  'Drug-Analysis': '/tool/drug-compete',
  'Catalyst-Event-Analysis': '/tool/catalyst',
  'Clinical-Guideline-Analysis': '/tool/clinical-guidelines',
};
const FILTER_WITH_LOGIC = [ 'drug_feature', 'drug_modality','drug_name', 'drug_names', 'locations','route_of_administration', 'target' ];

// Component for rendering a status update with interactive elements for waiting state
const StatusUpdateEvent = ({ 
  update, 
  onReferenceClick,
  focusArea = 'planning', // Default focus area for the agent
  onFeedback = () => {}, // Single callback that handles all user feedback with {confirm, message} payload
  disable = false,
}) => {
  const { showModal } = useModal();
  const { user } = useUser();
  const [ feedback, setFeedback ] = useState('');
  const navigate = useNavigate();
  const [ feedbackSubmitted, setFeedbackSubmitted ] = useState(false);
  const [ showFeedback, setShowFeedback ] = useState(false);  
  const [ countdown, setCountdown ] = useState(10-(Math.floor(+new Date()/1000 - +update.startedAt))); //小于0就不展示？
  const { lang } = useLanguage();
  const timerRef = useRef(null);
  // console.log('xxxxxxxxxxxxxxxxxxxxx', (update))
  useEffect(() => {
    if (disable) return;
    if (update.agentStatus === 'waiting' && !feedbackSubmitted && !showFeedback && countdown > 0) {
      timerRef.current = setInterval(() => {
        setCountdown(prev => {
          if (prev < 0) {
            clearInterval(timerRef.current);
            timerRef.current = null;
            return;
          } else if (prev <= 1) {            
            clearInterval(timerRef.current);
            timerRef.current = null;
            if (!disable && !feedbackSubmitted && !showFeedback) {
              handleFeedback(true);
            }
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
    return () => clearInterval(timerRef.current);
  }, [ update.agentStatus, feedbackSubmitted, showFeedback, countdown, disable ]);

  useEffect(() => {    
    if ((disable || showFeedback || feedbackSubmitted) && timerRef.current) {
      clearInterval(timerRef.current);
      setCountdown(0);
      timerRef.current = null;
    }
  }, [ disable ]);

  useEffect(() => {
    if (update?.agentStatus === 'waiting' && feedbackSubmitted) {
      setFeedbackSubmitted(false);
      setShowFeedback(false);
      setFeedback('');
      setCountdown(10 - (Math.floor(+new Date()/1000 - +update.startedAt)));
    }
  }, [ update?.startedAt ]);

  // Determine status indicators and styles
  let dotColor = 'bg-neutral-300';
  let textColor = 'text-gray-1';
  
  // Set different indicators and styles based on agent status
  if (update.agentStatus === 'waiting' || update.agentStatus === 'waitingFeedback') {
    dotColor = 'bg-blue-400';
    textColor = 'text-noah-theme';
  } else if (update.agentStatus === 'running') {
    dotColor = 'bg-amber-400';
    textColor = 'text-amber-600';
  } else if (update.agentStatus === 'done') {
    dotColor = 'bg-emerald-400';
    textColor = 'text-emerald-600';
  } else if (update.agentStatus === 'stopped') {
    dotColor = 'bg-noah-theme';
    textColor = 'text-noah-theme';
  }

  const handleFeedback = (confirm, message = null) => {
    setCountdown(0);
    // Call the onFeedback callback with the appropriate parameters
    onFeedback({ 
      type: 'confirmTool',
      approve: confirm,
      feedback: message,
      agent: update?.agent || 'planning',
      thread_id: update.thread_id,
      planning_task: update.id.slice(0, 36),
    });
    setFeedbackSubmitted(true);
    setFeedback('');
  };

  // Dynamic link handler that creates params only when clicked
  const handleRedirectClick = (e) => {
    e.preventDefault(); // Prevent default link behavior immediately
    
    // if user is not logged in, nor already invited, show a modal to invite user to login
    if (!user || !user.invited) {
      location.href = '/signin?redirect=' + encodeURIComponent(window.location.href);
      return;
    }
    
    if (!update?.current_tool?.params || !update?.id) {
      return; // Exit early if no params or id
    }
    
    const basePath = REDIRECT_TOOL_PATHS[update.current_tool.tool];
    // if value is null, remove the key
    const processedParams = Object.entries(update.current_tool.params).map(([ key, value ]) => {
      if (FILTER_WITH_LOGIC.includes(key) && Array.isArray(value)) {
        return {
          [key]: {
            data: value,
            logic: 'or',
          }
        }
      } else {
        return {
          [key]: value,
        }
      }
    }).reduce((acc, curr) => {
      return { ...acc, ...curr };
    }, {});
    const newPath = `${basePath}?search_param=${encodeURIComponent(JSON.stringify(processedParams))}`;

    window.open(newPath, '_blank');
  };

  if (!update.message && !update.stepQuestion && !update.current_tool && update.agentStatus !== 'waiting' && update.agentStatus !== 'stopped') {
    return null;
  }
  
  return (
    <div className="flex flex-col mb-4 group max-w-4xl mx-auto">
      <div className="flex items-center">
        {/* Status indicator dot */}
        {/* {update.agentStatus === 'waiting' && (
          <div className={`h-2 w-2 rounded-full ${dotColor} mr-2 flex-shrink-0`}></div>
        )} */}
        
        {/* Status text */}
        {
          !showFeedback && <div className="flex flex-col flex-grow whitespace-pre-wrap">
            {
              update.stepQuestion && <span className="text-sm font-medium mb-1 text-noah-theme">{update.stepQuestion}</span>
            }

            {
              update.message && <span className={`text-sm font-medium mb-1 ${textColor}`}>{update.message}</span>
            }

            {/* Tool information - only visible on hover */}
            {update.current_tool && (
              <div className="flex items-center justify-between py-2 px-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-2">
                  {update.current_tool.reason && (
                    <span className="text-xs text-gray-500 italic border-l-2 border-l-noah-theme pl-2">{update.current_tool.reason}</span>
                  )}
                </div>
                {REDIRECT_TOOLS.includes(update?.current_tool?.tool) && (
                  <button
                    onClick={handleRedirectClick}
                    className="min-w-[140px] px-4 py-1.5 bg-noah-theme hover:bg-opacity-90
                             text-white shadow-sm rounded-md relative
                             transition-all duration-200 flex items-center justify-center group"
                  >
                    <span className="text-sm font-medium">{i18n.PREVIEW_DATA[lang]}</span>
                  </button>
                )}
              </div>
            )}
          </div>
        }
      </div>

      {/* Interactive elements for waiting status - all shown side by side */}
      {update.agentStatus === 'waiting' && !feedbackSubmitted && (
        <div className="">
          {
            showFeedback ? <>
              <div className="flex items-center flex-wrap justify-between">
                <div className="text-gray-1 text-sm">{i18n.MODIFY_THIS_STEP[lang]}</div>
                <div className="flex items-center cursor-pointer" onClick={() => setShowFeedback(false)}>
                  <div className="text-black-1 text-sm">{i18n.BACK[lang]}</div>
                  <ChevronRight className="h-4 w-4 ml-1" />
                </div>
              </div>
              <div className="flex items-end gap-2 flex-grow mt-3 pl-[2px]">
                <Input.TextArea autoSize={true} value={feedback} onChange={(e) => setFeedback(e.target.value)} placeholder={i18n.PROVIDE_FEEDBACK[lang]} />
                <button
                  disabled={!feedback.trim()}
                  onClick={() => feedback.trim() && handleFeedback(false, feedback)}
                  className="w-[50px] h-[32px] flex items-center justify-center text-sm font-medium text-white 
                              bg-noah-theme rounded-lg
                              transition-all duration-200 text-center disabled:cursor-not-allowed"
                >
                  {i18n.SEND[lang]}
                </button>
              </div>
            </> : <div className="flex items-center gap-1.5 flex-wrap justify-between mt-2">
              {/* 10 second countdown with automatic confirmation */}
              <div className="text-xs text-black">
                {(countdown > 0 && !disable) ? `${countdown}${i18n.AUTO_ACCEPT[lang]}` : ''}
              </div>
              <div className="flex items-center gap-1.5 flex-wrap">
                <button
                  disabled={disable}
                  onClick={() => handleFeedback(true)}
                  className="w-[50px] h-[32px] flex items-center justify-center text-sm font-medium text-white 
                                bg-noah-theme rounded-lg
                                transition-all duration-200 text-center disabled:cursor-not-allowed"
                >
                  {i18n.YES[lang]}
                </button>
                <button
                  disabled={disable}
                  onClick={() => {
                    setCountdown(0);
                    setShowFeedback(true);
                    onFeedback({
                      type: 'stop-auto-run',
                      thread_id: update.thread_id,
                      planning_task: update.id.slice(0, 36),
                    });
                  }}
                  className="w-[50px] h-[32px] flex items-center justify-center text-sm font-medium text-black-1
                                bg-white border border-[#E9E9E9] rounded-lg
                                transition-all duration-200 text-center disabled:cursor-not-allowed"
                >
                  {i18n.NO[lang]}
                </button>
              </div>
            </div>
          }
        </div>
      )}

      {/* Feedback submitted confirmation */}
      {update.agentStatus === 'waiting' && feedbackSubmitted && (
        <div className="ml-4 mt-2 text-xs text-emerald-600 font-medium">
          {/* nothing shown here, the whole would be overridden from socket */}
        </div>
      )}

      {/* feedbackWaiting input */}
      {update.agentStatus === 'waitingFeedback' && !feedbackSubmitted && (
        <div className="flex items-end gap-2 flex-grow mt-3 pl-[2px]">
          <Input.TextArea autoSize={true} disabled={disable} className="!bg-white" value={feedback} onChange={(e) => setFeedback(e.target.value)} placeholder={i18n.ENTER_QUESTION[lang]} />
          <button
            disabled={!feedback.trim() || disable}
            onClick={() => feedback.trim() && handleFeedback(true, feedback)}
            className="w-[50px] h-[32px] flex items-center justify-center text-sm font-medium text-white 
                          bg-noah-theme rounded-lg
                          transition-all duration-200 text-center disabled:cursor-not-allowed"
          >
            {i18n.SEND[lang]}
          </button>
        </div>
      )}

      {/* Stopped state UI */}
      {update.agentStatus === 'stopped' && update.startedAt && update.taskStart && (
        <div className="mt-3 flex justify-start">
          <div className="inline-flex items-center gap-2 px-3 py-2 bg-noah-theme bg-opacity-5 rounded-lg">
            <span className="text-sm text-noah-theme font-medium whitespace-nowrap">
              {i18n.AGENT_STOPPED?.[lang] || 'Task completed'}
              {/* space between the text and the time */}
              {update.startedAt && update.taskStart && (
                <>
                  &nbsp;
                  {Math.floor((update.startedAt - update.taskStart) / 60)}{i18n.MINUTE[lang]} {Math.floor((update.startedAt - update.taskStart) % 60)}{i18n.SECOND[lang]}
                </>
              )}
            </span>
            
          </div>
        </div>
      )}
    </div>
  );
};

export default StatusUpdateEvent;