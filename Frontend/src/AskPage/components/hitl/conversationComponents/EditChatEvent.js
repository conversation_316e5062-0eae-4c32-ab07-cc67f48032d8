import React, { useEffect, useRef, useState } from 'react';

import { Input } from 'antd';

import { useLanguage } from '../../../../utils/context/lang';
import { i18n } from '../../../i18n/hitl';

const EditChatEvent = ({
  update, 
  message,
  focusArea = 'planning', // Default focus area for the agent
  onFeedback = () => {}, // Single callback that handles all user feedback with {confirm, message} payload
  disable = false,
}) => {  
  const [ feedbackSubmitted, setFeedbackSubmitted ] = useState(!update.editable || false);
  const [ countdown, setCountdown ] = useState(30);
  const [ isEditing, setIsEditing ] = useState(false);
  const [ tempContent, setTempContent ] = useState(message || '');
  const { lang } = useLanguage();
  const tips = i18n.EDIT_CHAT_EVENT[lang];
  const timerRef = useRef(null);
  useEffect(() => {
    if (disable) return;
    if (!feedbackSubmitted && countdown > 0) {
      timerRef.current = setInterval(() => {
        // Update countdown every second
        setCountdown(prev => {
          if (prev <= 1) {            
            clearInterval(timerRef.current);
            timerRef.current = null;
            if (!disable && !feedbackSubmitted) {
              handleFeedback(tempContent);
            }
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
    return () => clearInterval(timerRef.current);
  }, [ feedbackSubmitted, countdown, disable ]);

  useEffect(() => {    
    if ((disable || feedbackSubmitted) && timerRef.current) {
      clearInterval(timerRef.current);
      setCountdown(0);
      timerRef.current = null;
    }
  }, [ disable ]);

  useEffect(() => {    
    setTempContent(message || '');
  }, [ message ]);


  const handleFeedback = (message = null) => {
    console.log('handleFeedback called with confirm:', 'message:', message);
    setCountdown(0);
    // Call the onFeedback callback with the appropriate parameters
    onFeedback({ 
      type: 'edit',
      thread_id: update.thread_id,
      feedback: message,
      event_id: update.id,
    });
    setFeedbackSubmitted(true);
  };

  const handleEdit = () => {
    setCountdown(0);
    setIsEditing(true);
  };

  const handleCancel = () => {
    setIsEditing(false);
    setTempContent(update.message || '');
  };

  const handleFinish = () => {
    handleFeedback(tempContent);
    setIsEditing(false);
  };

  if (!update.message) {
    return null;
  }
  
  return (
    <div className="flex flex-col mb-4 group max-w-4xl mx-auto">
      {/* plan rewrite input */}
      <div className="flex flex-col flex-grow">
        <span className="text-sm font-medium mb-1 text-noah-theme whitespace-pre-wrap">
          {!feedbackSubmitted ? tips : tempContent}
        </span>
      </div>
      {
        !feedbackSubmitted && (
          <div className="mt-2">
            <div className="flex flex-col gap-2">
              <Input.TextArea
                disabled={disable}
                value={tempContent}
                onChange={(e) => setTempContent(e.target.value)}
                autoSize={true}
                onFocus={handleEdit}
                className="!bg-white"
              />
              {
                isEditing ? (
                  <div className="flex justify-end gap-2">
                    <button
                      onClick={handleCancel}
                      className="min-w-[50px] px-2 h-[32px] flex items-center justify-center text-sm font-medium text-black-1
                                      bg-white border border-[#E9E9E9] rounded-lg
                                      transition-all duration-200 text-center disabled:cursor-not-allowed"
                    >
                      {i18n.CANCEL[lang]}
                    </button>
                    <button
                      disabled={!tempContent.trim() || disable}
                      onClick={handleFinish}
                      className="min-w-[50px] px-2 h-[32px] flex items-center justify-center text-sm font-medium text-white 
                                      bg-noah-theme rounded-lg
                                      transition-all duration-200 text-center disabled:cursor-not-allowed"
                    >
                      {i18n.FINISH[lang]}
                    </button>
                  </div>
                ) : (
                  <div className='flex items-center justify-between gap-2'>
                    <div className="text-xs text-black">{(countdown && !disable) ? `${countdown}${i18n.AUTO_ACCEPT[lang]}` : ''}</div>
                    <button
                      disabled={disable}
                      onClick={() => {
                        handleFeedback(true, update.message);
                      }}
                      className="min-w-[50px] px-2 h-[32px] flex items-center justify-center text-sm font-medium text-white 
                                      bg-noah-theme rounded-lg
                                      transition-all duration-200 text-center disabled:cursor-not-allowed"
                    >
                      {i18n.ACCEPT[lang]}
                    </button>
                  </div>
                )
              }
            </div>
          </div>
        )
      }
    </div>
  );
};

export default EditChatEvent;