# Conversation Components

This folder contains modular components for the ConversationPlayer that represent different types of events in the conversation.

## Component Overview

- **AssistantResponse**: Renders the assistant's text responses with support for markdown formatting.
- **CheckpointEvent**: Displays checkpoint markers in the conversation flow.
- **ConfirmToolEvent**: Shows user confirmations/rejections for tool usage.
- **PlanStep**: Renders a step in a plan with a timeline visualization.
- **StatusUpdateEvent**: Displays status updates with different visual states.
- **ThoughtEvent**: Shows the agent's thought process with expand/collapse functionality.
- **ToolResultEvent**: Displays the output of tool executions.
- **TypingIndicator**: Animated indicator for when the assistant is typing.

## Usage

Import components individually or use the index.js barrel file:

```javascript
import { 
  AssistantResponse, 
  ThoughtEvent, 
  PlanStep 
} from './conversationComponents';
```

## Design Principles

- Components are designed to be self-contained with their own styling
- Each component handles one specific event type in the conversation
- Components use modern UI patterns (timeline view, expandable sections, cards)
- Visual styling is consistent with the application's design language 