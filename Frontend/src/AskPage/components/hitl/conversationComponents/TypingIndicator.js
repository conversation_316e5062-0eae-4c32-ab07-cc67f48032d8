import React from 'react';

// Minimal modern typing indicator using Tailwind
const TypingIndicator = () => (
  <div className="inline-flex items-center gap-1 my-1">
    <div className="h-1.5 w-1.5 rounded-full bg-neutral-400 animate-typingPulse"></div>
    <div className="h-1.5 w-1.5 rounded-full bg-neutral-400 animate-typingPulse delay-200"></div>
    <div className="h-1.5 w-1.5 rounded-full bg-neutral-400 animate-typingPulse delay-400"></div>
  </div>
);

export default TypingIndicator; 