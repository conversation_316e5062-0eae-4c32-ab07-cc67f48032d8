import React from 'react';

import DisplayFormattedText from '../../markdown';

// Component for rendering a plan step with enhanced visual styling
const PlanStep = ({ plan, isLastInSequence, onReferenceClick, isCompact = false }) => {
  // Determine the status badge color and icon
  let statusColor = 'bg-neutral-100 text-neutral-600 border-neutral-300'; // default (pending)
  let dotColor = 'bg-neutral-400';
  let lineColor = 'bg-neutral-200';
  let statusIcon = '⧗';

  if (plan.status === 'done') {
    statusColor = 'bg-emerald-50 text-emerald-700 border-emerald-200';
    dotColor = 'bg-emerald-500';
    lineColor = 'bg-emerald-200';
    statusIcon = '✓';
  } else if (plan.status === 'doing') {
    statusColor = 'bg-blue-50 text-blue-600 border-blue-200';
    dotColor = 'bg-blue-500';
    lineColor = 'bg-blue-200';
    statusIcon = '●';
  } else if (plan.status === 'error') {
    statusColor = 'bg-rose-50 text-rose-700 border-rose-200';
    dotColor = 'bg-rose-500';
    lineColor = 'bg-rose-200';
    statusIcon = '✕';
  }
  
  // Compact mode is used for horizontal display in the top panel
  if (isCompact) {
    return (
      <div className="h-full flex flex-col justify-between">
        <div className="flex items-center gap-2 mb-2">
          <div className={`h-3 w-3 rounded-full ${dotColor} ${plan.status === 'doing' ? 'animate-pulse' : ''}`}></div>
          <span className="font-semibold text-sm text-blue-800 truncate">{plan.tool || 'Step'}</span>
          <span className={`text-xs px-2 py-0.5 rounded-full border ${statusColor} font-medium flex items-center ml-auto`}>
            <span className="mr-1">{statusIcon}</span>
            {plan.status === 'done' ? 'Done' : 
              plan.status === 'doing' ? 'In Progress' : 
                plan.status === 'error' ? 'Failed' : 'Pending'}
          </span>
        </div>
        
        {plan.reason && (
          <p className="text-xs text-neutral-600 line-clamp-3">{plan.reason}</p>
        )}
        
        {plan.result && (
          <div className="mt-2 text-xs bg-white p-2 rounded-md border border-neutral-200">
            <div className="line-clamp-2">
              <DisplayFormattedText text={plan.result} onReferenceClick={onReferenceClick} />
            </div>
          </div>
        )}
      </div>
    );
  }
  
  // Regular mode (vertical display)
  return (
    <div className="relative mb-4 pl-6 max-w-5xl mx-auto">
      {/* Timeline dot with pulse animation for active steps */}
      <div className={`absolute left-0 top-[12px] h-4 w-4 rounded-full ${dotColor} z-10 
        ${plan.status === 'doing' ? 'animate-pulse shadow-lg shadow-blue-200' : ''}`}></div>
      
      {/* Connecting line with gradient fade for better visual appearance */}
      {!isLastInSequence && (
        <div className={`absolute left-[7px] top-[24px] bottom-0 w-[2px] ${lineColor}`}></div>
      )}
      
      {/* Content with enhanced styling */}
      <div className="group bg-gradient-to-r from-base-100 to-base-100/50 rounded-lg p-3 border-l-[3px] border-l-blue-400 shadow-sm hover:shadow-md transition-all duration-300">
        <div className="flex items-center flex-wrap gap-2">
          <span className="font-semibold text-sm text-blue-800">{plan.tool || 'Step'}</span>
          <span className={`text-xs px-2 py-1 rounded-full border ${statusColor} font-medium flex items-center`}>
            <span className="mr-1">{statusIcon}</span>
            {plan.status === 'done' ? 'Completed' : 
              plan.status === 'doing' ? 'In Progress' : 
                plan.status === 'error' ? 'Failed' : 'Pending'}
          </span>
          <span className="text-xs text-neutral-400 ml-auto opacity-0 group-hover:opacity-100 transition-opacity">{plan.id}</span>
        </div>
        
        {plan.reason && (
          <p className="text-sm mt-2 text-neutral-700 font-medium">{plan.reason}</p>
        )}
        
        {plan.result && (
          <div className="mt-3 text-sm bg-white p-4 rounded-md shadow-inner">
            <DisplayFormattedText text={plan.result} onReferenceClick={onReferenceClick} />
          </div>
        )}
      </div>
    </div>
  );
};

export default PlanStep; 