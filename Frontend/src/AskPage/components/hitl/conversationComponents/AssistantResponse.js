import React from 'react';

import {
  ClipboardIcon,
} from '@heroicons/react/24/outline';

import { useAlert } from '../../../../utils/context/alert';
import { useLanguage } from '../../../../utils/context/lang';
import { i18n } from '../../../i18n/common';
import DisplayFormattedText from '../../markdown';

import TypingIndicator from './TypingIndicator';

// Component for rendering a assistant response with minimal modern design
const AssistantResponse = ({ response, onReferenceClick, isRunning }) => {
  const showAlert = useAlert();
  const { lang } = useLanguage();

  const handleCopy = () => {
    const textToCopy = response.content || response.message || response.result || (response.current_tool && response.current_tool.result) || '';
    navigator.clipboard.writeText(textToCopy).then(
      () => {
        console.log('Content copied to clipboard');
        showAlert('success', i18n.SUCCESS_COPY[lang]);
      },
      (err) => {
        console.error('Could not copy text: ', err);
        showAlert('error', i18n.FAILED_COPY[lang]);
      }
    );
  };

  return (
    <div className="mb-4 max-w-full overflow-x-auto group">
      <DisplayFormattedText 
        text={response.content || response.message || response.result || (response.current_tool && response.current_tool.result) || ''}
        onReferenceClick={onReferenceClick}
      />
      {isRunning && <div className="mt-1"><TypingIndicator /></div>}
      
      <ClipboardIcon
        title="Copy"
        onClick={handleCopy}
        className="ml-3 h-4 w-4 cursor-pointer text-neutral-content hover:text-secondary-content opacity-0 group-hover:opacity-100 transition-opacity duration-200 inline-block"
        aria-hidden="true"
      />
    </div>
  );
};

export default AssistantResponse; 