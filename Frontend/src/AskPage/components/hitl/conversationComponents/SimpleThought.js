import { ReactComponent as CheckSelectIcon } from '../../../../Common/assets/check-select.svg';
import { ReactComponent as WaitingIcon } from '../../../assets/waiting.svg';

function SimpleThought({ thought, thoughtClassName }) {
  if (!thought || thought?.children.length === 0) {
    return null;
  }

  const isProcessing = thought?.children?.[thought?.children?.length - 1]?.processing_type !== 3;
  const text = thought?.children?.[thought?.children?.length - 1]?.query;

  return (
    <div className={`w-full py-3 flex items-center space-x-1 ${thoughtClassName}`}>
      {isProcessing && <WaitingIcon className="animate-spin w-5 h-5" />}
      <p className={`font-medium text-sm overflow-auto text-gray-1 ${isProcessing ? 'animate-pulse' : ''}`}>{text}</p>
    </div>
  );
}

export default SimpleThought;
