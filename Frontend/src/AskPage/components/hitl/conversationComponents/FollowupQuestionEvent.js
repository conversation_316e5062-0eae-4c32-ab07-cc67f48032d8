import React from 'react';

import { ReactComponent as ArrowLongRight } from '../../../../Common/assets/arrow-long-right.svg';
import { useLanguage } from '../../../../utils/context/lang';
import { i18n } from '../../../i18n/common';

const FollowupQuestionEvent = ({ followupQuestions, onTriggerNewQuestion, disable = false, agent }) => {
  const { lang } = useLanguage();
  
  return (
    <div className="mb-3">
      <h3 className="text-base font-semibold mb-3 text-black-1">
        {i18n.FOLLOWUP_QUESTIONS[lang]}
      </h3>
      <ul className="space-y-3">
        {followupQuestions.map((followup_question, index) => (
          <li
            key={index}
            className={`group relative flex items-center justify-between min-w-0 py-4 px-6 rounded-2xl bg-[#3333330A] gap-x-1 ${disable ? 'cursor-not-allowed' : 'cursor-pointer'}`}
            onClick={() => !disable && onTriggerNewQuestion(followup_question, [], false, agent)}
          >
            <p className="text-sm leading-5 text-black line-clamp-2 mr-1 align-middle">
              {followup_question}
            </p>
            <ArrowLongRight className="w-4 h-4 align-middle hidden group-hover:block" />
          </li>
        ))}
      </ul>
    </div>
  );
};

export default FollowupQuestionEvent; 