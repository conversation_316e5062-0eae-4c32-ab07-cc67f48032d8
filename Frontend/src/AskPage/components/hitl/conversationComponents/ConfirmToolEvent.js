import React from 'react';

import { ReactComponent as AcceptIcon } from '../../../assets/accept.svg';

// Component for rendering a tool confirmation with minimal modern design
const ConfirmToolEvent = ({ confirmation }) => {
  const isAccepted = confirmation.accept;
  const dotColor = isAccepted ? 'bg-emerald-400' : 'bg-amber-400';
  const textColor = isAccepted ? 'text-noah-theme' : 'text-amber-600';
  const borderColor = isAccepted ? 'border-emerald-200' : 'border-amber-200';
  
  return (
    <div className="mb-3 flex items-start max-w-4xl mx-auto">
      {/* <div className={`h-2 w-2 rounded-full ${dotColor} mr-2 mt-1.5 flex-shrink-0`}></div> */}
      
      <div className="flex-1">
        <div className="flex items-center">
          {
            isAccepted ? <AcceptIcon className="w-5 h-5 mr-1" /> : <svg className="mr-1" xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="#d97706" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          }
          <div className={`text-sm font-medium ${textColor}`}>
            Tool {isAccepted ? 'Accepted' : 'Rejected'}
          </div>
        </div>
        
        {confirmation.message && (
          <div className="text-xs text-gray-1 mt-0.5 italic">
            &quot;{confirmation.message}&quot;
          </div>
        )}
      </div>
    </div>
  );
};

export default ConfirmToolEvent; 