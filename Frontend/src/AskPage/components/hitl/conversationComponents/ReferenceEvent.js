import React from 'react';

import { useLanguage } from '../../../../utils/context/lang';
import { i18n } from '../../../i18n/common';

const ReferenceEvent = ({ sources, handleViewAllReferences }) => {
  const { lang } = useLanguage();
  
  return (
    <div className="relative flex text-base mx-auto mb-4 gap-3 group max-w-4xl">
      <div className="relative flex w-full flex-col text-primary-content">
        <div className="font-semibold select-none">{i18n.REFERENCE[lang]}</div>
        <div className="relative flex-col gap-1">
          <div className="relative md:grid md:grid-cols-4 gap-2 min-h-24">
            {/* Show first 3 references */}
            {sources?.slice(0, 3).map((source, index) => (
              <div
                key={index}
                className="flex relative flex-col p-4 my-2 bg-[#3333330A] rounded-xl justify-between cursor-pointer"
                onClick={
                  ()=>handleViewAllReferences(sources, index)
                  // () => window.open(source.url, '_blank')
                }
              >
                <p className="line-clamp-3 text-left text-sm text-black-1 mb-1">
                  {source.title}
                </p>
                <p className="line-clamp-1 text-left text-xs text-gray-1">
                  {source.site_name}
                </p>
              </div>
            ))}
    
            {/* Show View More card if more than 3 references */}
            {sources?.length > 3 && (
              <div
                className="flex relative flex-col p-4 my-2 bg-[#3333330A] rounded-xl justify-center  cursor-pointer"
                onClick={
                  ()=>handleViewAllReferences(sources)
                }
              >
                <p className="text-sm text-black-1 mb-1">View All</p>
                <p className="text-sm text-gray-1">
                            +{sources.length - 3} more references
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReferenceEvent; 