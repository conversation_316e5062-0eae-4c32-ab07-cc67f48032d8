/* Custom styles for the PlanStepTimeline component */
.plan-steps .ant-steps-item {
  padding-bottom: 4px;
}

.plan-steps .ant-steps-item-title {
  font-size: 12px;
  font-weight: 500;
}

.plan-steps .ant-steps-item-description {
  font-size: 11px;
}

/* Constrain width for mobile view */
@media (max-width: 768px) {
  .plan-steps {
    overflow-x: auto;
  }
}

.plan-steps-inline .ant-steps-item {
  margin-right: 12px;
}

.plan-steps-inline .ant-steps-item-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.plan-steps-inline .ant-steps-item-icon {
  margin-right: 8px;
}

.plan-steps-inline .ant-steps-item-content {
  display: flex;
  flex-direction: column;
}

.plan-steps-inline .ant-steps-item-title {
  font-size: 12px;
  font-weight: 500;
  line-height: 1.3;
}

.plan-steps-inline .ant-steps-item-description {
  font-size: 11px;
  color: rgba(0, 0, 0, 0.6);
}

/* Make the tooltip show on hover even on mobile devices */
@media (hover: hover) {
  .ant-tooltip {
    display: block !important;
  }
}

/* Constrain width for mobile view */
@media (max-width: 768px) {
  .plan-steps-inline {
    overflow-x: auto;
    padding-bottom: 8px;
  }
  
  .plan-steps-inline::-webkit-scrollbar {
    height: 4px;
  }
  
  .plan-steps-inline::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 4px;
  }
} 