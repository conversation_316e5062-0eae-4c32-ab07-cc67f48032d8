// Simplified ConversationPlayer component that handles real-time and playback modes
// for displaying conversations with collapsible plan steps

import React, { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';

import { useAlert } from '../../../utils/context/alert';
import { useLanguage } from '../../../utils/context/lang';
import { useWebSocketContext } from '../../../utils/context/main-websocket.js';
import { useUser } from '../../../utils/context/user';
import { i18n } from '../../i18n/common';
import ChatSection from '../ChatSection';
import FileThumbnail from '../fileThumbnail';

import {
  AssistantResponse,
  CheckpointEvent,
  ConfirmToolEvent,
  PlanStep,
  StatusUpdateEvent,
  ThoughtEvent,
  ToolResultEvent,
  TypingIndicator
} from './conversationComponents';

const ConversationPlayer = ({ 
  events: initialEvents = [], 
  threadId = null,
  agentType = null,
  isFollowupFetching = false,
  onFollowupComplete = () => {},
  onReferenceClick, 
  isPlaybackEnabled = true,
  onPlanUpdate = () => {} 
}) => {
  const { lang } = useLanguage();
  const { user } = useUser();
  const showAlert = useAlert();
  const { latestMessage, sendMessage } = useWebSocketContext();

  // Core state
  const [ events, setEvents ] = useState([]);
  const [ autoScroll, setAutoScroll ] = useState(true);
  const [ collapsedSteps, setCollapsedSteps ] = useState({});
  
  // Refs for tracking collapsed state without re-rendering the entire component
  const collapsedStepsRef = useRef(collapsedSteps);
  const playbackTimeout = useRef(null);
  const containerRef = useRef(null);
  const bottomRef = useRef(null);
  
  // Update ref when state changes
  useEffect(() => {
    collapsedStepsRef.current = collapsedSteps;
  }, [ collapsedSteps ]);

  // Toggle collapsed state of a plan step - optimized to minimize re-renders
  const togglePlanStep = useCallback((stepId) => {
    // Update the ref immediately for any synchronous operations
    collapsedStepsRef.current = {
      ...collapsedStepsRef.current,
      [ stepId ]: !collapsedStepsRef.current[ stepId ]
    };
    
    // Update state (will cause re-render for just the toggled step)
    setCollapsedSteps(collapsedStepsRef.current);
  }, []); // Empty dependency array - function never changes

  // Add or update an event in the events array
  const updateEvent = useCallback((newEvent) => {
    // Process plan updates
    if (newEvent.type === 'planUpdate' && newEvent.plan) {
      if (!Array.isArray(newEvent.plan)) {
        newEvent.plan = [];
      }
      
      // Auto-collapse completed steps
      const newCollapsedState = {};
      newEvent.plan.forEach(step => {
        if (step.status === 'done' && !(step.id in collapsedStepsRef.current)) {
          newCollapsedState[step.id] = true;
        }
      });
      
      if (Object.keys(newCollapsedState).length > 0) {
        const updatedState = { ...collapsedStepsRef.current, ...newCollapsedState };
        collapsedStepsRef.current = updatedState;
        setCollapsedSteps(updatedState);
      }
    }

    // Add or update the event
    setEvents(prev => {
      const exists = prev.some(e => e.id === newEvent.id);
      return exists 
        ? prev.map(e => (e.id === newEvent.id ? newEvent : e)) 
        : [ ...prev, newEvent ];
    });
  }, []); // Remove collapsedSteps dependency

  // Handle scroll events to disable auto-scroll when user scrolls
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;
    
    const handleScroll = () => {
      if (autoScroll) setAutoScroll(false);
    };
    
    container.addEventListener('wheel', handleScroll, { passive: true });
    container.addEventListener('touchmove', handleScroll, { passive: true });
    
    return () => {
      container.removeEventListener('wheel', handleScroll);
      container.removeEventListener('touchmove', handleScroll);
    };
  }, [ autoScroll ]);

  // Auto-scroll to bottom when events change
  useEffect(() => {
    if (autoScroll) {
      bottomRef.current?.scrollIntoView({ behavior: 'smooth' });
    }
  }, [ events, autoScroll ]);

  // Process playback events at a steady pace
  const startPlayback = useCallback((incomingEvents) => {
    if (!Array.isArray(incomingEvents) || incomingEvents.length === 0) return;
    
    const playbackSpeed = 700;
    let currentIndex = 0;
    
    // Clear any existing timeout
    if (playbackTimeout.current) {
      clearTimeout(playbackTimeout.current);
    }
    
    // Process events one by one with a delay
    const processNextEvent = () => {
      if (currentIndex >= incomingEvents.length) return;
      
      updateEvent(incomingEvents[currentIndex]);
      currentIndex++;
      
      if (currentIndex < incomingEvents.length) {
        playbackTimeout.current = setTimeout(processNextEvent, playbackSpeed);
      }
    };
    
    processNextEvent();
    
    return () => {
      if (playbackTimeout.current) clearTimeout(playbackTimeout.current);
    };
  }, [ updateEvent ]);

  // Process initial events
  useEffect(() => {
    if (!initialEvents || initialEvents.length === 0) return;

    // Ensure all events have valid IDs
    const processedEvents = initialEvents.map((event, index) => ({
      ...event,
      id: event.id || `event-${index}-${Date.now()}`
    }));

    // Either play back events gradually or set them all at once
    if (isPlaybackEnabled) {
      startPlayback(processedEvents);
    } else {
      setEvents(prev => {
        const incomingIds = new Set(processedEvents.map(e => e.id));
        const filteredPrev = prev.filter(e => !incomingIds.has(e.id));
        return [ ...filteredPrev, ...processedEvents ];
      });
    }
    
    return () => {
      if (playbackTimeout.current) clearTimeout(playbackTimeout.current);
    };
  }, [ initialEvents, isPlaybackEnabled, startPlayback ]);

  // Process real-time WebSocket messages
  useEffect(() => {
    if (!latestMessage || latestMessage.thread_id !== threadId) return;
    
    if (latestMessage.type === 'LIMIT') {
      showAlert('error', i18n.DAILY_LIMIT_ERROR[lang]);
      onFollowupComplete();
      return;
    }

    let event = {
      ...latestMessage,
      id: latestMessage.id || `event-${Date.now()}`,
      thread_id: threadId,
      startedAt: latestMessage.startedAt || Date.now() / 1000
    };

    if (event.type === 'CONTENT') {
      event = {
        ...event,
        type: 'chat',
        sender: 'assistant',
        message: latestMessage.content,
        result: latestMessage.content,
        status: latestMessage.status
      };
      if (event.status === 'DONE') onFollowupComplete();
    }

    updateEvent(event);
  }, [ latestMessage, threadId, lang, updateEvent, showAlert, onFollowupComplete ]);

  // Handle reference clicks
  const handleReferenceClick = useCallback((ref) => {
    if (!onReferenceClick) return;
    if (typeof ref === 'number') {
      onReferenceClick({ sources: ref.sources, referenceIndex: +ref });
    } else {
      onReferenceClick({ sources: [ { url: ref } ], referenceIndex: 0 });
    }
  }, [ onReferenceClick ]);

  // Check if assistant is currently generating a response
  const isAssistantRunning = (e) => e.type === 'chat' && e.sender === 'assistant' && e.status !== 'DONE';

  // Handle feedback submissions
  const handleFeedback = useCallback((feedback) => {
    sendMessage(feedback);
  }, [ sendMessage ]);

  // Render individual events based on their type
  const renderEvent = useCallback((event) => {
    if (!event || !event.type) return null;

    switch (event.type) {
    case 'chat':
      if (event.sender === 'system') {
        return (
          <div key={event.id} className="text-xs text-center text-base-content/50 my-2 italic">
            {event.message}
          </div>
        );
      }
      if (event.sender === 'user') {
        return (
          <ChatSection
            key={event.id}
            imageUrl={`https://placehold.co/50x50?text=${user?.username?.[0]?.toUpperCase() || 'U'}`}
            role="you"
          >
            {event.upload_files?.length > 0 && (
              <div className="flex flex-wrap mb-2">
                {event.upload_files.map((f, i) => (
                  <FileThumbnail key={f.id || i} upload={f} allowDelete={false} />
                ))}
              </div>
            )}
            <div className="whitespace-pre-wrap">{event.message}</div>
          </ChatSection>
        );
      }
      return (
        <ChatSection key={event.id} role="agent">
          <AssistantResponse
            response={{ ...event, content: event.result || event.message }}
            onReferenceClick={handleReferenceClick}
            isRunning={isFollowupFetching && isAssistantRunning(event)}
          />
        </ChatSection>
      );
    case 'planUpdate': return null; // Don't render plan updates in the main flow
    case 'plan':
      return <PlanStep key={event.id} plan={event.plan || event} isLastInSequence={event.isLastInSequence || false} onReferenceClick={handleReferenceClick} isCompact={false} />;
    case 'checkpoint':
      return <CheckpointEvent key={event.id} checkpoint={event} />;
    case 'toolResult':
      return <ToolResultEvent key={event.id} tool={event} onReferenceClick={handleReferenceClick} />;
    case 'statusUpdate':
      return <StatusUpdateEvent key={event.id} update={event} onReferenceClick={handleReferenceClick} onFeedback={handleFeedback}/>;
    case 'thought':
      return <ThoughtEvent key={event.id} thought={event} />;
    case 'confirmTool':
      return <ConfirmToolEvent key={event.id} confirmation={event} />;
    default:
      return null;
    }
  }, [ user, handleReferenceClick, isFollowupFetching, handleFeedback ]);

  // Find and expose the latest plan update 
  useEffect(() => {
    const planUpdates = events.filter(e => e.type === 'planUpdate');
    if (planUpdates.length > 0) {
      onPlanUpdate(planUpdates[planUpdates.length - 1]);
    }
  }, [ events, onPlanUpdate ]);

  // Group events by plan steps using ID pattern matching
  const groupedEvents = useMemo(() => {
    // Find the plan structure
    const planUpdate = events.find(e => e.type === 'planUpdate');
    if (!planUpdate || !planUpdate.plan || !Array.isArray(planUpdate.plan)) {
      // No plan structure - return events as standalone
      return events.map(event => ({
        type: 'standaloneEvent',
        id: event.id,
        event
      }));
    }
    
    // Extract plan steps and assign indexes
    const planSteps = planUpdate.plan.map((step, index) => ({
      ...step,
      index: index + 1,
      events: []
    }));
    
    // Create a map for quick lookup
    const planStepsMap = Object.fromEntries(
      planSteps.map(step => [ step.index, step ])
    );
    
    // Separate standalone events
    const standaloneEvents = [];
    
    // Associate events with plan steps based on ID pattern
    events.forEach(event => {
      // Skip plan updates
      if (event.type === 'planUpdate') return;
      
      // Check if event ID matches pattern with step number
      const match = event.id && typeof event.id === 'string' ? 
        event.id.match(/^([0-9a-f-]{36})-(\d+)/) : null;
      
      if (match && planStepsMap[match[2]]) {
        // Check if this event ID already exists in the step's events
        const existingEventIndex = planStepsMap[match[2]].events.findIndex(e => e.id === event.id);
        
        if (existingEventIndex >= 0) {
          // Update existing event instead of adding a new one
          planStepsMap[match[2]].events[existingEventIndex] = event;
        } else {
          // Add new event
          planStepsMap[match[2]].events.push(event);
        }
      } else {
        standaloneEvents.push(event);
      }
    });
    
    // Combine plan steps and standalone events
    const result = [
      // Include plan steps with events or non-todo status
      ...planSteps
        .filter(step => step.events.length > 0 || step.status !== 'todo')
        .map(step => ({
          type: 'planStepContainer',
          id: step.id,
          planStep: step,
          events: step.events
        })),
      // Include standalone events
      ...standaloneEvents.map(event => ({
        type: 'standaloneEvent',
        id: event.id,
        event
      }))
    ];
    
    // Sort by step index for plan steps, then by timestamp
    return result.sort((a, b) => {
      if (a.type === 'planStepContainer' && b.type === 'planStepContainer') {
        return a.planStep.index - b.planStep.index;
      }
      
      const aTime = a.type === 'planStepContainer' ? a.planStep.startedAt || 0 : a.event.startedAt || 0;
      const bTime = b.type === 'planStepContainer' ? b.planStep.startedAt || 0 : b.event.startedAt || 0;
      
      return aTime - bTime;
    });
  }, [ events ]);

  // Render plan step containers and standalone events
  const renderItem = useCallback((item) => {
    if (item.type === 'planStepContainer') {
      const { planStep, events, id } = item;
      const isCollapsed = collapsedStepsRef.current[id];
      
      return (
        <CollapsiblePlanStep
          key={id}
          planStep={planStep}
          events={events}
          id={id}
          isCollapsed={isCollapsed}
          togglePlanStep={togglePlanStep}
          renderEvent={renderEvent}
        />
      );
    } else if (item.type === 'standaloneEvent') {
      return renderEvent(item.event);
    }
    
    return null;
  }, [ renderEvent, togglePlanStep ]);

  return (
    <div className="flex flex-col h-full relative">
      <div className="flex-1 overflow-y-auto overflow-x-hidden h-full" ref={containerRef}>
        {/* Empty state */}
        {events.length === 0 && !isFollowupFetching && (
          <div className="text-center py-12 text-base-content/60">
            {initialEvents.length === 0 ? 'No conversation history' : 'Loading conversation...'}
          </div>
        )}

        {/* Render events organized by plan steps */}
        {groupedEvents.map(renderItem)}

        {/* Typing indicator */}
        {isFollowupFetching && (!events.some(Boolean) || !events.some(isAssistantRunning)) && (
          <ChatSection role="agent">
            <TypingIndicator />
          </ChatSection>
        )}
        
        {/* Bottom spacing + scroll anchor */}
        <div ref={bottomRef} className="h-36"></div>
      </div>
    </div>
  );
};

// Create a memoized collapsible container component to prevent re-rendering non-toggled steps
const CollapsiblePlanStep = memo(({ 
  planStep, 
  events, 
  id, 
  isCollapsed, 
  togglePlanStep, 
  renderEvent 
}) => {
  // Determine styling based on status
  let statusConfig = {
    colorClass: 'bg-neutral-100 text-neutral-500 border-neutral-300',
    indicatorClass: 'bg-neutral-400',
    icon: '⧗',
    text: 'Pending'
  };
  
  if (planStep.status === 'done') {
    statusConfig = {
      colorClass: 'bg-emerald-50 text-emerald-600 border-emerald-200',
      indicatorClass: 'bg-emerald-500',
      icon: '✓',
      text: 'Completed'
    };
  } else if (planStep.status === 'doing') {
    statusConfig = {
      colorClass: 'bg-blue-50 text-blue-600 border-blue-200',
      indicatorClass: 'bg-blue-500',
      icon: '●',
      text: 'In Progress'
    };
  } else if (planStep.status === 'error') {
    statusConfig = {
      colorClass: 'bg-rose-50 text-rose-600 border-rose-200',
      indicatorClass: 'bg-rose-500',
      icon: '✕',
      text: 'Failed'
    };
  }
  
  return (
    <div className="mb-6 relative max-w-5xl mx-auto">
      {/* Step number indicator */}
      {/* <div className={`absolute left-[-36px] top-[14px] ${statusConfig.indicatorClass} text-white w-7 h-7 flex items-center justify-center rounded-full text-sm font-bold shadow-sm z-10 ${planStep.status === 'doing' ? 'animate-pulse' : ''}`}>
        {planStep.index}
      </div> */}
      
      {/* Plan step container */}
      <div className="transition-all duration-200 ease-in-out">
        {/* Header (always visible) */}
        <div 
          className="cursor-pointer rounded-t-lg bg-white hover:bg-neutral-50"
          onClick={() => togglePlanStep(id)}
        >
          <div className="p-3 flex items-center">
            <div className="flex-1">
              <div className="font-medium text-neutral-800 flex items-center gap-2">
                <span className={`flex items-center justify-center w-5 h-5 rounded-full ${statusConfig.indicatorClass} ${planStep.status === 'doing' ? 'animate-pulse' : ''}`}>
                  {planStep.status === 'done' ? (
                    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round">
                      <polyline points="20 6 9 17 4 12"></polyline>
                    </svg>
                  ) : planStep.status === 'doing' ? (
                    <span className="w-3 h-3 bg-white rounded-full"></span>
                  ) : planStep.status === 'error' ? (
                    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round">
                      <line x1="18" y1="6" x2="6" y2="18"></line>
                      <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                  ) : (
                    <span className="w-2 h-2 bg-white rounded-full"></span>
                  )}
                </span>
                <span className="text-blue-600">
                  {planStep.tool || `Step ${planStep.index}`}
                </span>
              </div>
            </div>
            
            {/* Toggle indicator */}
            <div className="ml-4 text-neutral-500 transition-transform duration-200" style={{ transform: isCollapsed ? 'rotate(0deg)' : 'rotate(180deg)' }}>
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <polyline points="6 9 12 15 18 9"></polyline>
              </svg>
            </div>
          </div>
          
          {/* Description (truncated) */}
          {planStep.reason && (
            <div className="px-3 pb-3">
              <p className="text-sm text-neutral-600 line-clamp-2">{planStep.reason}</p>
            </div>
          )}
        </div>
        
        {/* Content section (collapsible) */}
        <div className={`overflow-hidden transition-all duration-300 rounded-b-lg bg-neutral-50 grid ${
          isCollapsed 
            ? 'grid-rows-[0fr] opacity-0 border-t-0' 
            : 'grid-rows-[1fr] opacity-100'
        }`}>
          <div className="min-h-0">
            {events.length > 0 ? (
              <div className="p-3 pl-5 ml-4">
                {events.map(renderEvent)}
              </div>
            ) : (
              <div className="p-3 text-center text-neutral-500 text-sm italic">
                No events yet
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}, (prevProps, nextProps) => {
  // Only re-render if these specific props change
  return (
    prevProps.isCollapsed === nextProps.isCollapsed &&
    prevProps.planStep.status === nextProps.planStep.status &&
    prevProps.events.length === nextProps.events.length
  );
});

// Add displayName
CollapsiblePlanStep.displayName = 'CollapsiblePlanStep';

export default ConversationPlayer;
