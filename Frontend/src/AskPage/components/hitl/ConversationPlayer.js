// Simplified ConversationPlayer component that handles real-time and playback modes
// for displaying conversations with collapsible plan steps

import React, { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';

import throttle from 'lodash.throttle';
import { useNavigate } from 'react-router-dom';

import { createSharedId } from '../../../api.js';
import { ReactComponent as ChevronDown } from '../../../assets/arrow-down.svg';
import ThoughtNode from '../../../Common/thought.js';
import { useAlert } from '../../../utils/context/alert';
import { useLanguage } from '../../../utils/context/lang';
import { useWebSocketContext } from '../../../utils/context/main-websocket.js';
import { useModal } from '../../../utils/context/modal';
import { useUser } from '../../../utils/context/user';
import { ReactComponent as DoneIcon } from '../../assets/done.svg';
import { ReactComponent as RejectIcon } from '../../assets/reject.svg';
import { ReactComponent as ShareIcon } from '../../assets/share.svg';
import { ReactComponent as WaitingIcon } from '../../assets/waiting.svg';
import { i18n } from '../../i18n/hitl.js';
import ChatSection from '../ChatSection';
import FileThumbnail from '../fileThumbnail';

import {
  AssistantResponse,
  CheckpointEvent,
  ConfirmToolEvent,
  EditChatEvent,
  FollowupQuestionEvent,
  PlanStep,
  ReferenceEvent,
  SimpleThought,
  StatusUpdateEvent,
  ThoughtEvent,
  ToolResultEvent,
  TypingIndicator,
} from './conversationComponents';

const PLAYBACK_SPEED = 50;

const ConversationPlayer = ({ 
  events: initialEvents = [], 
  threadId = null,
  agentType = null,
  isFollowupFetching = false,
  onFollowupComplete = () => {},
  onReferenceClick, 
  isShare = false,
  onSubmit = () => {},
  focusArea = 'planning',
  cancelCountdown = false,
  isSending = false,
}) => {
  const { lang } = useLanguage();
  const { user } = useUser();
  const showAlert = useAlert();
  const { showModal } = useModal();
  const navigate = useNavigate();
  const { latestMessage, sendMessage } = useWebSocketContext();

  // Core state
  const [ events, setEvents ] = useState([]);
  const [ autoScroll, setAutoScroll ] = useState(true);
  const [ collapsedSteps, setCollapsedSteps ] = useState({});
  
  // Playback control state (only used in share mode)
  const [ isPlaying, setIsPlaying ] = useState(false);
  const [ playbackComplete, setPlaybackComplete ] = useState(false);
  const [ allEvents, setAllEvents ] = useState([]);
  
  // Refs for tracking collapsed state without re-rendering the entire component
  const collapsedStepsRef = useRef(collapsedSteps);
  const playbackTimeout = useRef(null);
  const containerRef = useRef(null);
  const bottomRef = useRef(null);
  
  // Throttled scroll function to prevent excessive scrolling
  const throttledScrollToBottom = useRef(
    throttle(() => {
      bottomRef.current?.scrollIntoView({ behavior: 'smooth' });
    }, 200)
  ).current;
  
  // Update ref when state changes
  useEffect(() => {
    collapsedStepsRef.current = collapsedSteps;
  }, [ collapsedSteps ]);

  // Toggle collapsed state of a plan step - optimized to minimize re-renders
  const togglePlanStep = useCallback((stepId) => {
    // Update the ref immediately for any synchronous operations
    collapsedStepsRef.current = {
      ...collapsedStepsRef.current,
      [ stepId ]: !collapsedStepsRef.current[ stepId ]
    };
    
    // Update state (will cause re-render for just the toggled step)
    setCollapsedSteps(collapsedStepsRef.current);
  }, []); // Empty dependency array - function never changes

  // Add or update an event in the events array
  const updateEvent = useCallback((newEvent) => {
    // Process plan updates
    if (newEvent.type === 'planUpdate' && newEvent.plan) {
      if (!Array.isArray(newEvent.plan)) {
        newEvent.plan = [];
      }
      
      // Auto-collapse completed steps (except summary-related tools)
      const newCollapsedState = {};
      newEvent.plan.forEach(step => {
        if (step.status === 'done' && 
            !(step.id in collapsedStepsRef.current) &&
            ![ 'Generate-Summary', 'General-Inference' ].includes(step.tool)) {
          newCollapsedState[step.id] = true;
        }
      });
      
      if (Object.keys(newCollapsedState).length > 0) {
        const updatedState = { ...collapsedStepsRef.current, ...newCollapsedState };
        collapsedStepsRef.current = updatedState;
        setCollapsedSteps(updatedState);
      }
    }

    // Add or update the event
    setEvents(prev => {
      if (newEvent.type === 'planUpdate') {
        // For plan updates, replace any existing plan update instead of accumulating them
        const withoutPlanUpdates = prev.filter(e => e.type !== 'planUpdate');
        return [ ...withoutPlanUpdates, newEvent ];
      } else {
        // For other events, update if exists, otherwise add
        const exists = prev.some(e => e.id === newEvent.id);
        return exists 
          ? prev.map(e => (e.id === newEvent.id ? newEvent : e)) 
          : [ ...prev, newEvent ];
      }
    });
  }, []);

  // Shared utility: Deduplicate events and handle collapsed states
  const processEventsBundle = useCallback((incomingEvents) => {
    if (!Array.isArray(incomingEvents) || incomingEvents.length === 0) return [];
    
    // Deduplicate events: keep only the latest version of each event ID
    const eventMap = new Map();
    
    incomingEvents.forEach(event => {
      if (event.type === 'planUpdate') {
        // For plan updates, always use the latest one (overwrite any previous)
        eventMap.set('planUpdate', event);
      } else {
        // For other events, keep the latest version of each ID
        eventMap.set(event.id, event);
      }
    });
    
    const finalEvents = Array.from(eventMap.values());
    
    // Process the latest plan update for collapsed states
    const latestPlanUpdate = eventMap.get('planUpdate');
    if (latestPlanUpdate && latestPlanUpdate.plan && Array.isArray(latestPlanUpdate.plan)) {
      const newCollapsedState = {};
      latestPlanUpdate.plan.forEach(step => {
        if (step.status === 'done' && 
            !(step.id in collapsedStepsRef.current) &&
            ![ 'Generate-Summary', 'General-Inference' ].includes(step.tool)) {
          newCollapsedState[step.id] = true;
        }
      });
      
      if (Object.keys(newCollapsedState).length > 0) {
        const updatedState = { ...collapsedStepsRef.current, ...newCollapsedState };
        collapsedStepsRef.current = updatedState;
        setCollapsedSteps(updatedState);
      }
    }
    
    return finalEvents;
  }, []);

  // Shared utility: Process events one by one
  const processEventsSequentially = useCallback((incomingEvents, delay = 0) => {
    if (!Array.isArray(incomingEvents) || incomingEvents.length === 0) return;
    
    let currentIndex = 0;
    
    const processNext = () => {
      if (currentIndex >= incomingEvents.length) {
        if (delay > 0) {
          setIsPlaying(false);
          setPlaybackComplete(true);
        }
        return;
      }
      
      updateEvent(incomingEvents[currentIndex]);
      currentIndex++;
      
      if (currentIndex < incomingEvents.length) {
        if (delay > 0) {
          playbackTimeout.current = setTimeout(processNext, delay);
        } else {
          processNext(); // No delay for real-time
        }
      } else if (delay > 0) {
        setIsPlaying(false);
        setPlaybackComplete(true);
      }
    };
    
    if (delay > 0) {
      // Clear any existing timeout for playback
      if (playbackTimeout.current) {
        clearTimeout(playbackTimeout.current);
      }
      setIsPlaying(true);
      setPlaybackComplete(false);
    }
    
    processNext();
  }, [ updateEvent ]);

  // Process all events immediately (for non-share mode and jumpToEnd)
  const processAllEventsImmediately = useCallback((incomingEvents) => {
    const finalEvents = processEventsBundle(incomingEvents);
    finalEvents.forEach(event => updateEvent(event));
    setPlaybackComplete(true);
  }, [ processEventsBundle, updateEvent ]);

  // Process playback events at a steady pace (for share mode only)
  const startPlayback = useCallback((incomingEvents) => {
    processEventsSequentially(incomingEvents, PLAYBACK_SPEED);
  }, [ processEventsSequentially ]);

  const showNotification = useCallback(() => {
    if (!('Notification' in window)) {
      console.log('该浏览器不支持通知功能');
      return;
    }

    if (!window.isSecureContext) {
      console.log('需要在 HTTPS 环境下运行');
      return;
    }

    try {
      if (Notification.permission === 'granted') {
        const notification = new Notification(i18n.NOTIFICATION_TITLE[lang], {
          body: i18n.NOTIFICATION_MESSAGE[lang],
          icon: '/favicon.ico',
          tag: 'planning-complete', // 防止重复通知
          // requireInteraction: true, // 通知会一直显示直到用户交互
          silent: false // 允许声音提醒
        });

        notification.onclick = () => {
          window.focus();
          notification.close();
        };
      } else if (Notification.permission !== 'denied') {
        Notification.requestPermission().then(permission => {
          if (permission === 'granted') {
            showNotification();
          }
        });
      }
    } catch (error) {
      console.log('通知创建失败:', error);
    }
  }, []);

  // Jump to end state - show all events immediately
  const jumpToEnd = useCallback(() => {
    if (allEvents.length === 0) return;
    
    // Clear any ongoing playback
    if (playbackTimeout.current) {
      clearTimeout(playbackTimeout.current);
      playbackTimeout.current = null;
    }
    
    setIsPlaying(false);
    setPlaybackComplete(true);
    
    // Use shared logic to process all events at once
    const finalEvents = processEventsBundle(allEvents);
    setEvents(finalEvents);

    // Enable auto-scroll to show the end
    setAutoScroll(true);
  }, [ allEvents, processEventsBundle ]);

  // Replay from beginning (share mode only)
  const replay = useCallback(() => {
    if (allEvents.length === 0) return;
    
    // Clear any ongoing playback
    if (playbackTimeout.current) {
      clearTimeout(playbackTimeout.current);
      playbackTimeout.current = null;
    }
    
    // Reset state
    setEvents([]);
    setCollapsedSteps({});
    collapsedStepsRef.current = {};
    setPlaybackComplete(false);
    setIsPlaying(true);
    setAutoScroll(true);
    
    // Start playback from beginning
    startPlayback(allEvents);
  }, [ allEvents, startPlayback ]);

  const handleTryClick = (e) => {
    e.preventDefault(); // Prevent default link behavior immediately
      
    // if user is not logged in, nor already invited, show a modal to invite user to login
    if (!user || !user.invited) {
      location.href = '/signin?redirect=' + encodeURIComponent(window.location.href);
      return;
    } else {
      navigate('/hitl');
    }
  };

  // Handle scroll events to disable auto-scroll when user scrolls
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;
    
    const handleScroll = () => {
      if (autoScroll) setAutoScroll(false);
    };
    
    container.addEventListener('wheel', handleScroll, { passive: true });
    container.addEventListener('touchmove', handleScroll, { passive: true });
    
    return () => {
      container.removeEventListener('wheel', handleScroll);
      container.removeEventListener('touchmove', handleScroll);
    };
  }, [ autoScroll ]);

  // Auto-scroll to bottom when events change
  useEffect(() => {
    if (autoScroll) {
      throttledScrollToBottom();
    }
  }, [ events, autoScroll, throttledScrollToBottom ]);

  // Process initial events
  useEffect(() => {
    if (!initialEvents || initialEvents.length === 0) return;

    // Ensure all events have valid IDs
    const processedEvents = initialEvents.map((event, index) => ({
      ...event,
      id: event.id || `event-${index}-${Date.now()}`
    }));

    // Store all events for playback control
    setAllEvents(processedEvents);

    // Handle different modes
    if (isShare) {
      // Share mode with playback enabled: use gradual playback
      startPlayback(processedEvents);
    } else {
      // Non-share mode or playback disabled: process immediately

      processAllEventsImmediately(processedEvents);

    }
    
    return () => {
      if (playbackTimeout.current) clearTimeout(playbackTimeout.current);
    };
  }, [ initialEvents, isShare, startPlayback ]);

  // Process real-time WebSocket messages
  useEffect(() => {
    if (!latestMessage || latestMessage.thread_id !== threadId) return;
    
    if ((latestMessage.type === 'statusUpdate' && latestMessage.agentStatus === 'stopped') || latestMessage.status === 'DONE') {
      onFollowupComplete();
    }

    if (latestMessage.agent === 'planning' && latestMessage.type === 'statusUpdate' && latestMessage.agentStatus === 'stopped') {
      showNotification();
    }
    
    if (latestMessage.type === 'LIMIT') {
      showAlert('error', i18n.DAILY_LIMIT_ERROR[lang]);
      onFollowupComplete();
      return;
    }

    let event = {
      ...latestMessage,
      id: latestMessage.id || `event-${Date.now()}`,
      thread_id: threadId,
      startedAt: latestMessage.startedAt || Date.now() / 1000
    };

    if (event.type === 'CONTENT') {
      event = {
        ...event,
        type: 'chat',
        sender: 'assistant',
        message: latestMessage.content,
        result: latestMessage.content,
        status: latestMessage.status
      };
    }

    // Update both current events and all events for playback control
    updateEvent(event);
    setAllEvents(prev => {
      const exists = prev.some(e => e.id === event.id);
      return exists 
        ? prev.map(e => (e.id === event.id ? event : e)) 
        : [ ...prev, event ];
    });
  }, [ latestMessage, threadId, lang, updateEvent, showAlert, onFollowupComplete, showNotification ]);

  // Group events by plan steps using ID pattern matching
  const groupedEvents = useMemo(() => {
    // Find the latest plan structure (not just the first one)
    const planUpdates = events.filter(e => e.type === 'planUpdate');
    const planUpdate = planUpdates.length > 0 ? planUpdates[planUpdates.length - 1] : null;
    
    if (!planUpdate || !planUpdate.plan || !Array.isArray(planUpdate.plan)) {
      // No plan structure - return events as standalone
      return events.map(event => ({
        type: 'standaloneEvent',
        id: event.id,
        event
      }));
    }
    
    // Extract plan steps and assign indexes
    const planSteps = planUpdate.plan.map((step, index) => ({
      ...step,
      index: index + 1,
      events: []
    }));
    
    // Create a map for quick lookup
    const planStepsMap = Object.fromEntries(
      planSteps.map(step => [ step.index, step ])
    );
    
    // Separate standalone events
    const standaloneEvents = [];
    
    // Associate events with plan steps based on ID pattern
    events.forEach(event => {
      // Skip plan updates
      if (event.type === 'planUpdate') return;
      
      // Check if event ID matches pattern with step number
      const match = event.id && typeof event.id === 'string' ? 
        event.id.match(/^([0-9a-f-]{36})-(\d+)/) : null;
      
      if (match && planStepsMap[match[2]]) {
        // Check if this event ID already exists in the step's events
        const existingEventIndex = planStepsMap[match[2]].events.findIndex(e => e.id === event.id);
        
        if (existingEventIndex >= 0) {
          // Update existing event instead of adding a new one
          planStepsMap[match[2]].events[existingEventIndex] = event;
        } else {
          // Add new event
          planStepsMap[match[2]].events.push(event);
        }
      } else {
        standaloneEvents.push(event);
      }
    });
    
    // Combine plan steps and standalone events
    const result = [
      // Include plan steps with events or non-todo status
      ...planSteps
        .filter(step => step.events.length > 0 || step.status !== 'todo')
        .map(step => ({
          type: 'planStepContainer',
          id: step.id,
          planStep: step,
          events: step.events
        })),
      // Include standalone events
      ...standaloneEvents.map(event => ({
        type: 'standaloneEvent',
        id: event.id,
        event
      }))
    ];
    
    // return result;
    // Sort by step index for plan steps, then by timestamp
    // no need to sort, because the step index is already sorted
    return result.sort((a, b) => {
      if (a.type === 'planStepContainer' && b.type === 'planStepContainer') {
        return a.planStep.index - b.planStep.index;
      }
      
      const aTime = a.type === 'planStepContainer' ? a.planStep.startedAt || 0 : a.event.startedAt || 0;
      const bTime = b.type === 'planStepContainer' ? b.planStep.startedAt || 0 : b.event.startedAt || 0;
      
      return aTime - bTime;
    });
  }, [ events ]);

  const lastChatId = useMemo(() => {
    if (groupedEvents.length === 0) return null;
    const lastEvent = groupedEvents[groupedEvents.length - 1];
    let match = null;
    let id = '';
    if (lastEvent?.type === 'planStepContainer') {
      id = lastEvent.events?.[0]?.id || '';
    } else {
      id = lastEvent?.id || '';
    }
    match = id?.match(/^([0-9a-f-]{36})-(\d+)/) || [];     
    return match?.[1] || null;
  }, [ groupedEvents ])

  // Handle reference clicks
  const handleReferenceClick = useCallback((ref, id) => {
    if (!onReferenceClick) return;
    if (typeof ref === 'number') {
      console.log(`Reference index clicked ref: ${ref}`, id, groupedEvents);
      let message = null;
      if (id.startsWith('step')) {
        message = groupedEvents.find((e) => e.id === id)?.events?.find((e) => e.type === 'reference')?.message;      
      } else {
        const match = id.match(/^([0-9a-f-]{36})-(\d+)/);        
        message = groupedEvents.find((e) => e.event?.type === 'reference' && e.event?.id?.startsWith(match?.[0]))?.event?.message;
      }
      if (!message) return;
      const sources = JSON.parse(message);
      onReferenceClick({ sources, referenceIndex: +ref });
    } else {
      onReferenceClick({ sources: [ { url: ref } ], referenceIndex: 0 });
    }
  }, [ onReferenceClick, groupedEvents ]);

  // Check if assistant is currently generating a response
  const isAssistantRunning = (e) => e.type === 'chat' && e.sender === 'assistant' && e.status !== 'DONE';

  // Handle feedback submissions
  const handleFeedback = useCallback((feedback) => {
    sendMessage(feedback);
  }, [ sendMessage ]);

  const handleViewAllReferences = useCallback((source, referenceIndex) => {
    onReferenceClick({
      sources: source,
      referenceIndex: referenceIndex,
    });
  }, [ onReferenceClick ]);

  // Render individual events based on their type
  const renderEvent = useCallback((event, stepId) => {
    if (!event || !event.type) return null;

    const disable = isShare || cancelCountdown || event.id?.match(/^([0-9a-f-]{36})-(\d+)/)[1] !== lastChatId;    

    switch (event.type) {
    case 'chat':
      if ('editable' in event) {
        return <EditChatEvent key={event.id} update={event} message={event?.message || ''} focusArea={focusArea} disable={disable} onFeedback={handleFeedback} />;
      }
      if (event.sender === 'system') {
        return (
          <div key={event.id} className="max-w-4xl mx-auto text-xs text-center text-base-content/50 my-2 italic">
            {event.message}
          </div>
        );
      }
      if (event.sender === 'user') {
        return (
          <ChatSection
            key={event.id}
            imageUrl={`https://placehold.co/50x50?text=${user?.username?.[0]?.toUpperCase() || 'U'}`}
            role="you"
          >
            {event.upload_files?.length > 0 && (
              <div className="flex flex-wrap mb-2">
                {event.upload_files.map((f, i) => (
                  <FileThumbnail key={f.id || i} upload={f} allowDelete={false} />
                ))}
              </div>
            )}
            <div className="text-black text-base font-medium whitespace-pre-wrap">{event.message.startsWith('/SG') ? i18n.SG_MESSAGE[lang] : event.message}</div>
          </ChatSection>
        );
      }
      return (
        <ChatSection key={event.id} role="agent">
          <AssistantResponse
            response={{ ...event, content: event.result || event.message }}
            onReferenceClick={(ref) => handleReferenceClick(ref, stepId || event.id)}
            isRunning={isFollowupFetching && isAssistantRunning(event)}
          />
        </ChatSection>
      );
    case 'planUpdate': return null; // Don't render plan updates in the main flow
    case 'toolResult':
      return <ToolResultEvent key={event.id} tool={event} onReferenceClick={handleReferenceClick} />;
    case 'statusUpdate':
      return <StatusUpdateEvent key={event.id} update={event} disable={disable} onReferenceClick={handleReferenceClick} onFeedback={handleFeedback} />;
    case 'thought':
      return <ThoughtNode key={''+event.id+'thought'} thought={JSON.parse(event.message)?.search_graph} thoughtClassName="pt-0 pm-3" />;
    case 'simpleThought':
      return <SimpleThought key={''+event.id+'thought'} thought={JSON.parse(event.message)?.search_graph} thoughtClassName="pt-0 pm-3" />;
    case 'confirmTool':
      return <ConfirmToolEvent key={event.id} confirmation={event} />;
    case 'reference':
      return <ReferenceEvent key={event.id} sources={JSON.parse(event.message)} handleViewAllReferences={handleViewAllReferences} />;
    case 'followupQuestion':
      return <FollowupQuestionEvent key={event.id} followupQuestions={JSON.parse(event.message)} onTriggerNewQuestion={onSubmit} disable={isShare || isSending} agent={event.agent} />;
    default:
      return null;
    }
  }, [ user, handleReferenceClick, handleViewAllReferences, isFollowupFetching, handleFeedback, isShare, onSubmit, cancelCountdown, lastChatId, isSending ]);

  // Render plan step containers and standalone events
  const renderItem = useCallback((item) => {
    if (item.type === 'planStepContainer') {
      const { planStep, events, id } = item;
      const isCollapsed = collapsedStepsRef.current[id];
      
      return (
        <CollapsiblePlanStep
          key={id}
          planStep={planStep}
          events={events}
          id={id}
          isCollapsed={isCollapsed}
          togglePlanStep={togglePlanStep}
          renderEvent={renderEvent}
        />
      );
    } else if (item.type === 'standaloneEvent') {
      return <div className="max-w-4xl mx-auto">
        {renderEvent(item.event)}
      </div>;
    }
    
    return null;
  }, [ renderEvent, togglePlanStep ]);

  return (
    <div className="flex flex-col h-full relative">
      <div className="flex-1 overflow-y-auto overflow-x-hidden h-full" ref={containerRef}>
        {/* Empty state */}
        {(events.length === 0 && !isFollowupFetching) && (
          <div className="text-center py-12 text-base-content/60">
            {initialEvents.length === 0 ? '' : i18n.LOADING_CONVERSATION[lang]}
          </div>
        )}

        {/* Render events organized by plan steps */}
        {groupedEvents.map(renderItem)}

        {/* Typing indicator */}
        {isFollowupFetching && (!events.some(Boolean) || !events.some(isAssistantRunning)) && (
          <ChatSection role="agent">
            <TypingIndicator />
          </ChatSection>
        )}
        
        {/* Bottom spacing + scroll anchor */}
        <div ref={bottomRef} className="h-16"></div>
      </div>
      
      {/* Play controls - only show in share mode when there are events */}
      {isShare && allEvents.length > 0 && (
        <div className="fixed bottom-4 left-1/2 transform -translate-x-1/2 z-10">
          <div className="bg-white rounded-full shadow-lg border border-gray-200 px-4 py-2 flex items-center gap-3">
            {!playbackComplete ? (
              <button
                onClick={jumpToEnd}
                className="flex items-center gap-2 px-3 py-1 bg-blue-500 text-white rounded-full hover:bg-blue-600 transition-colors"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <polygon points="5,4 15,12 5,20"></polygon>
                  <line x1="19" y1="5" x2="19" y2="19"></line>
                </svg>
                <span className="text-sm font-medium">Jump to End</span>
              </button>
            ) : (
              <button
                onClick={replay}
                className="flex items-center gap-2 px-3 py-1 bg-green-500 text-white rounded-full hover:bg-green-600 transition-colors"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <polyline points="1,4 1,10 7,10"></polyline>
                  <path d="M3.51,15a9,9,0,0,0,2.13,3.09,9,9,0,0,0,13.37,0,9,9,0,0,0,0-12.72,9,9,0,0,0-9.51-1.59"></path>
                </svg>
                <span className="text-sm font-medium">Replay</span>
              </button>
            )}
            
            {isPlaying ? (
              <div className="flex items-center gap-2 text-gray-600">
                <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                <span className="text-sm">Playing...</span>
              </div>
            ) : (
              <div
                onClick={handleTryClick}
                className="cursor-pointer font-medium flex text-sm items-center gap-2 px-3 py-1 bg-blue-500 text-white rounded-full hover:bg-blue-600 transition-colors"
              >
                Try it yourself
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

// Create a memoized collapsible container component to prevent re-rendering non-toggled steps
const CollapsiblePlanStep = memo(({ 
  planStep, 
  events, 
  id, 
  isCollapsed, 
  togglePlanStep, 
  renderEvent,
}) => {  
  // Determine styling based on status
  let statusConfig = {
    colorClass: 'bg-neutral-100 text-neutral-500 border-neutral-300',
    indicatorClass: 'bg-neutral-400',
    icon: '⧗',
    text: 'Pending'
  };
  
  if (planStep.status === 'done') {
    statusConfig = {
      colorClass: 'bg-emerald-50 text-emerald-600 border-emerald-200',
      indicatorClass: '',
      icon: '✓',
      text: 'Completed'
    };
  } else if (planStep.status === 'doing') {
    statusConfig = {
      colorClass: 'bg-blue-50 text-blue-600 border-blue-200',
      indicatorClass: '',
      icon: '●',
      text: 'In Progress'
    };
  } else if (planStep.status === 'error') {
    statusConfig = {
      colorClass: 'bg-rose-50 text-rose-600 border-rose-200',
      indicatorClass: 'bg-rose-500',
      icon: '✕',
      text: 'Failed'
    };
  }
  
  return (
    <div className="mb-4 relative max-w-4xl mx-auto">
      {/* Step number indicator */}
      {/* <div className={`absolute left-[-36px] top-[14px] ${statusConfig.indicatorClass} text-white w-7 h-7 flex items-center justify-center rounded-full text-sm font-bold shadow-sm z-10 ${planStep.status === 'doing' ? 'animate-pulse' : ''}`}>
        {planStep.index}
      </div> */}
      
      {/* Plan step container */}
      <div className="transition-all duration-200 ease-in-out">
        {/* Header (always visible) */}
        <div className="rounded-[20px] bg-white p-5 pb-2">
          {/* The part that will not be folded */}
          <div className="cursor-pointer" onClick={() => togglePlanStep(id)}>
            <div className="flex items-center mb-3">
              <div className="flex-1">
                <div className="font-medium flex items-center gap-1">
                  <span className={`flex items-center justify-center w-5 h-5 rounded-full ${planStep.status === 'doing' ? 'animate-spin' : ''}`}>
                    {planStep.status === 'done' ? (
                      <DoneIcon />
                    ) : planStep.status === 'doing' ? (
                      <WaitingIcon />
                    ) : planStep.status === 'error' ? (
                      <RejectIcon />
                    ) : (
                      <span className="w-3 h-3 bg-white rounded-full"></span>
                    )}
                  </span>
                  <span className="text-black-1 font-medium text-base">
                    {planStep.tool || `Step ${planStep.index}`}
                  </span>
                </div>
              </div>
              
              {/* Toggle indicator */}
              <ChevronDown className="h-4 w-4 ml-4 transition-transform duration-200" style={{ transform: isCollapsed ? 'rotate(0deg)' : 'rotate(180deg)' }} />
            </div>
            
            {/* Description (truncated) */}
            {planStep.reason && (
              <div className="mb-3">
                <p className="text-sm text-black-1 line-clamp-2">{planStep.reason}</p>
              </div>
            )}
          </div>

          {/* Content section (collapsible) */}
          <div className={`overflow-hidden transition-all duration-300 rounded-b-[20px] bg-white grid ${
            isCollapsed 
              ? 'grid-rows-[0fr] opacity-0 border-t-0' 
              : 'grid-rows-[1fr] opacity-100'
          }`}>
            <div className="min-h-0">
              {events.length > 0 ? (
                <div className="">
                  {events.map((event) => renderEvent(event, planStep.id))}
                </div>
              ) : (
                <div className="p-3 text-center text-neutral-500 text-sm italic">
                  No events yet
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}, (prevProps, nextProps) => {
  if (prevProps.renderEvent !== nextProps.renderEvent) {
    return false;
  }
  
  if (prevProps.events.length !== nextProps.events.length) {
    return false;
  }
  
  // Check if isCollapsed or planStep.status has changed
  if (prevProps.isCollapsed !== nextProps.isCollapsed || 
      prevProps.planStep.status !== nextProps.planStep.status) {
    return false;
  }
  
  // Check if any event has changed by comparing each event
  for (let i = 0; i < prevProps.events.length; i++) {
    const prevEvent = prevProps.events[i];
    const nextEvent = nextProps.events[i];
    
    // Simple check for updates: compare status, message/result content if present
    if (prevEvent.status !== nextEvent.status ||
        (prevEvent.message !== nextEvent.message) ||
        (prevEvent.result !== nextEvent.result)) {
      return false;
    }
  }
  
  return true;
});

// Add displayName
CollapsiblePlanStep.displayName = 'CollapsiblePlanStep';

export default ConversationPlayer;
