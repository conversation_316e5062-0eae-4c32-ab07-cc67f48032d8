import { Fragment, useEffect, useState } from 'react';

import {
  Popover,
  PopoverButton,
  PopoverPanel,
  Switch,
  Transition,
} from '@headlessui/react';
import { ChevronDownIcon } from '@heroicons/react/20/solid';
import {
  ArrowPathIcon,
  ArrowRightIcon,
  ChartPieIcon,
  CheckCircleIcon,
  CursorArrowRaysIcon,
  DocumentChartBarIcon,
  FingerPrintIcon,
  LanguageIcon,
  SquaresPlusIcon,
} from '@heroicons/react/24/outline';
import {
  Brain,
  Globe,
  MagnifyingGlass,
} from '@phosphor-icons/react';

import Tooltip from '../../Common/Components/Tooltip';
import TransparentSelect from '../../Common/Components/TransparentSelect.js';
import { useAlert } from '../../utils/context/alert.js'; // Adjust path as needed
import { useLanguage } from '../../utils/context/lang.js';
import { usePro } from '../../utils/context/pro.js';
import { useUser } from '../../utils/context/user.js';
import { i18n as agentDict, languages, placeholder, agents as solutions } from '../const/agent.js';
import { i18n } from '../i18n/common.js';

import { AgentButton, SearchButton, SearchSelect } from './ChatInputComponents.js';
import DeepSeekButton from './DeepSeekButton.js';
import FileThumbnail from './fileThumbnail.js'; // Ensure correct import path
import SendButton from './SentButton.js';
import UploadButton from './uploadBtn.js';

// Add this mapping near your solutions constant or where appropriate
const iconMap = {
  mindsearchofficialsite: Brain,
  mindsearchpubmed: MagnifyingGlass,
  mindsearch: Globe,
  // Add more mappings as needed
};

export default function textInput({
  className,
  onSubmit,
  enableUpload = false,
  disabled = false,
  onFileCountChange,
}) {
  const { lang } = useLanguage();
  const showAlert = useAlert();
  const { isProOn, switchPro, switchAnswerLang, answerLang } = usePro();
  // const { user } = useUser();
  const [ textInput, setTextInput ] = useState('');
  const [ uploadInfos, setUploadInfos ] = useState([]);
  const [ focusArea, setFocusArea ] = useState('mindsearchofficialsite');

  // useEffect(() => {
  //   if (forceSwitchValue !== undefined) {
  //     setSwitchValue(forceSwitchValue);
  //   } else {
  //     setSwitchValue(user ? user.price_plan === 'pro' : false);
  //   }
  // }, [ forceSwitchValue, user ]);

  // useEffect(()=>{
  //   onSwitchChange(switchValue);
  // }, [ switchValue ]);

  useEffect(() => {
    onFileCountChange(uploadInfos.length);
  }, [ uploadInfos ]);

  const submitQuestion = () => {
    console.log('submit question ', {
      textInput,
      uploadInfos,
      isProOn,
      focusArea,
    });
    onSubmit({ textInput, uploadInfos, isProOn, focusArea });
    setUploadInfos([]);
    setTextInput('');
    const textarea = document.querySelector('.resize-none'); // Use a more specific selector if needed
    const container = textarea.parentNode;

    // textarea.style.height = 'auto';  // Reset the height of textarea
    container.style.height = '60px'; // Reset the height of the container to the initial height
  };

  useEffect(() => {
    const handleKeyPress = (e) => {
      if (e.key === 'Enter' && !e.isComposing && !disabled) {
        if (textInput.trim().length === 0 || e.shiftKey) {
          // If there's no content (trimmed) or Shift is pressed, just add a new line
          return;
        } else {
          // Otherwise, submit the question
          e.preventDefault(); // Prevent form submission
          submitQuestion();
        }
      }
    };
    document.addEventListener('keydown', handleKeyPress);
    return () => {
      document.removeEventListener('keydown', handleKeyPress);
    };
  }, [ textInput, disabled ]); // Include `disabled` to ensure the effect updates if the disabled state changes

  const handleSelectFile = (file) => {
    console.log('File selected:', file.name);
    setUploadInfos((prev) => [ ...prev, { name: file.name } ]);
  };
  const handleTextChange = (e) => {
    const textarea = e.target;
    const container = textarea.parentNode; // Assuming the parent node is the container div
    container.style.height = 'auto';
    // Calculate effective height considering padding
    const computedStyle = window.getComputedStyle(textarea);
    const paddingTop = parseInt(computedStyle.paddingTop, 10);
    const paddingBottom = parseInt(computedStyle.paddingBottom, 10);
    const borderHeight =
      parseInt(computedStyle.borderTopWidth, 10) +
      parseInt(computedStyle.borderBottomWidth, 10);

    // Calculate the height to be the greater of scrollHeight (less padding and border) or the minimum height
    const desiredHeight = Math.max(
      textarea.scrollHeight - paddingTop - paddingBottom - borderHeight,
      53 // Increased minimum height from 60px to 53px
    );

    container.style.height = `${desiredHeight}px`;
    setTextInput(textarea.value); // Update the state with new input
  };
  const handleFileUploaded = (file, uploadData) => {
    console.log('selectedFile', file);
    console.log('File uploaded:', uploadData);
    setUploadInfos((prev) =>
      prev.map((info) =>
        info.name === file.name
          ? {
            ...info,
            upload: uploadData.upload,
            id: uploadData.id,
            name: file.name,
          }
          : info
      )
    );
  };

  const deleteUpload = (fileId) => {
    setUploadInfos((prev) => prev.filter((info) => info.id !== fileId));
  };

  // const onModelChange = (newVal) => {
  //   // if (newVal) {
  //   //   if (user?.price_plan === 'pro') {
  //   //     setSwitchValue(newVal);
  //   //   } else {
  //   //     // optimize this part
  //   //     showAlert('error', i18n.UPGRADE_HINT[lang]);
  //   //   }
  //   // } else {
  //   setSwitchValue(newVal);

  //   // }
  // };

  return (
    <>
      <div
        className={`form-control border border-[#EDEDED] p-5 rounded-3xl bg-base-100 ${className}`}
      >
        <div className="flex overflow-x-scroll mx-2 scrollbar-none">
          {uploadInfos.map((upload, index) => (
            <FileThumbnail
              key={upload.id || upload.name}
              upload={upload}
              onDelete={() => deleteUpload(upload.id)}
            />
          ))}
        </div>
        <div className="relative input-group flex items-center justify-center overflow-hidden h-[53px] max-h-[160px]">
          <textarea
            id="textarea"
            placeholder={placeholder[focusArea][lang]}
            rows={1}
            className="p-0 relative scrollbar-none h-full min-h-[53px] max-h-[160px] resize-none leading-6 input font-medium text-black-1 placeholder-gray-2 w-[calc(100%-1rem)] border-none focus:outline-none focus:shadow-none focus:ring-0 overflow-y-scroll"
            value={textInput}
            onChange={handleTextChange}
          />
        </div>
        <div className="relative flex items-center justify-between flex-wrap space-y-2">
          {/* <div className="relative flex items-center">
            {enableUpload && (
            <UploadButton
              className=""
              onSelectFile={handleSelectFile}
              onFileUploaded={handleFileUploaded}
              onUploadError={(error) => showAlert('error', 'File upload error')}
              upload={uploadInfos}
              isMini={false}
            />
          )}
          </div> */}
          <div className="relative flex items-center space-x-2">
            <AgentButton />
            <SearchButton />
          </div>
          <div className="relative flex items-center space-x-2">
            <SearchSelect
              value={focusArea}
              onChange={(e) => {
                setFocusArea(e.value);
              }}
            />
            <div className="relative group/tooltip flex items-center">
              <TransparentSelect
                className="w-auto"
                dropdownStyle={{
                  minWidth: '100px',
                }}
                value={answerLang}
                optionLabelProp="label"
                onChange={(e) => switchAnswerLang(e.value)}
                labelInValue
                needBlur={true}
                labelRender={(label) => (
                  <div className="flex items-center justify-center flex-1 font-medium text-sm !text-black-1">
                    {label.title}
                  </div>
                )}
                items={languages.map(item => {
                  return { 
                    value: item.code, 
                    label: item.name, 
                    current: item.code === answerLang 
                  }
                })}
              />
              <Tooltip
                tipClassName="left-1/2 -top-8 -translate-x-1/2"
                text="Change answer language"
              />
            </div>
            {/* <DeepSeekButton /> */}
            {/* <>
            <Switch
              checked={isProOn}
              onChange={switchPro}
              className={`${
                isProOn ? 'bg-blue-500' : 'bg-gray-200'
              } relative mr-1 ml-1 inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none`}
            >
              <span className="sr-only">Use setting</span>
              <span
                aria-hidden="true"
                className={`${
                  isProOn ? 'translate-x-5' : 'translate-x-0'
                } pointer-events-none inline-block h-5 w-5 rounded-full bg-white shadow ring-0 transition ease-in-out duration-200`}
              />
            </Switch>
            <span>Pro</span>
          </> */}
            <SendButton
              onClick={submitQuestion}
              disabled={textInput.length === 0 || disabled}
              isSending={false}
            />
          </div>
        </div>
      </div>
    </>
  );
}
