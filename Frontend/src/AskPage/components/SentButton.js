import CustomButton from '../../Common/Components/CustomButton.js';
import Tooltip from '../../Common/Components/Tooltip';
import { useLanguage } from '../../utils/context/lang';
import { ReactComponent as Pause } from '../assets/pause.svg';
import { ReactComponent as SendDisable } from '../assets/send-disable.svg';
import { ReactComponent as SendEnable } from '../assets/send-enable.svg';
import { ReactComponent as Sending } from '../assets/sending.svg';
import { i18n } from '../i18n/common';

export default function SendButton({ onClick, disabled, isSending }) {
  const { lang } = useLanguage();
  
  const DisabledIcon = <SendDisable className="w-5 h-5 translate-x-[1px]" />;
  const EnadledIcon = <SendEnable className="w-5 h-5 translate-x-[1px]" />;
  const SendingIcon = <Sending className="w-3 h-3 animate-pulse" />;
  const PauseIcon = <Pause className="w-3 h-3" />;
  
  return (
    <div className="group/tooltip relative ml-4">
      <CustomButton
        btnClassName={`disabled:bg-[#F7F7F7] ${isSending ? 'cursor-pointer' : disabled ? 'cursor-not-allowed' : 'cursor-pointer'}`}
        shape="circle"
        onBtnClick={() => (isSending || !disabled) && onClick()}
        icon={isSending ? (
          <>
            <div className="group-hover/tooltip:hidden">{SendingIcon}</div>
            <div className="hidden group-hover/tooltip:block">{PauseIcon}</div>
          </>
        ) : disabled ? DisabledIcon : EnadledIcon} />
      <Tooltip
        tipClassName="left-1/2 -top-8 -translate-x-1/2"
        text={isSending ? i18n.STOP_RUNNING[lang] : i18n.ENTER_QUESTION[lang]}
        show={disabled || isSending}
      />
    </div>
  );
}
