/*
  This example requires some changes to your config:
  
  ```
  // tailwind.config.js
  module.exports = {
    // ...
    plugins: [
      // ...
      require('@tailwindcss/forms'),
    ],
  }
  ```
*/
import { Fragment, useEffect, useState } from 'react';

import { Listbox, Transition } from '@headlessui/react';
import {
  ArrowRightIcon,
} from '@heroicons/react/20/solid';

import UploadButton from './uploadBtn';

function classNames(...classes) {
  return classes.filter(Boolean).join(' ');
}

export default function TextInput({ className, passedText, onSubmit }) {
  const [ textInput, setTextInput ] = useState(passedText);
  const [ uploadInfo, setUploadInfo ] = useState(null);

  useEffect(() => {
    setTextInput(passedText);
  }, [ passedText ]);

  const handleSelectFile = (file) => {
    console.log('File selected:', file.name);
    // Reset upload info on new file selection
    setUploadInfo(null);
  };

  const handleFileUploaded = (file, uploadData) => {
    console.log('selectedFile', file);
    console.log('File uploaded:', uploadData);
    setUploadInfo({
      upload: uploadData.upload, // Adjust according to your API response
      name: file.name,
      id: uploadData.id,
    });
  };
  return (
    <form action="#" className={`relative ${className}`}>
      <div className="overflow-hidden rounded-lg border border-gray-300 shadow-sm focus-within:border-primary focus-within:ring-1 focus-within:ring-primary">
        {/* <input
          type="text"
          name="title"
          id="title"
          className="block w-full border-0 pt-2.5 text-lg font-medium placeholder:text-neutral-content focus:ring-0"
          placeholder="Title"
        /> */}
        <label htmlFor="description" className="sr-only">
          Description
        </label>
        <textarea
          rows={2}
          name="description"
          id="description"
          className="block pt-2.5 w-full resize-none border-0 py-0 text-secondary-content placeholder:text-neutral-content focus:ring-0 sm:text-sm sm:leading-6"
          placeholder="Start exploring ..."
          defaultValue={''}
          value={textInput}
          onChange={(e) => {
            setTextInput(e.target.value);
          }}
        />

        {/* Spacer element to match the height of the toolbar */}
        <div aria-hidden="true">
          <div className="py-2">
            <div className="h-9" />
          </div>
          <div className="h-px" />
          <div className="py-2">
            <div className="py-px">
              <div className="h-9" />
            </div>
          </div>
        </div>
      </div>

      <div className="absolute inset-x-px bottom-0">
        {/* Actions: These are just examples to demonstrate the concept, replace/wire these up however makes sense for your project. */}
        <div className="flex items-center justify-between space-x-3 border-t border-gray-200 px-2 py-2 sm:px-3">
          <div className="flex">
            {/* <UploadButton onUploadUpdate={handleAttachment} /> */}
            <UploadButton
              onSelectFile={handleSelectFile}
              onFileUploaded={handleFileUploaded}
              upload={uploadInfo}
              isMini={false}
            />
          </div>
          <div className="flex-shrink-0">
            <button
              type="submit"
              className="inline-flex items-center rounded-md bg-primary px-3 py-2 text-sm font-semibold text-primary-content shadow-sm hover:bg-primary focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary"
              onClick={(e) => {
                e.preventDefault();
                onSubmit(textInput, uploadInfo);
              }}
            >
              <ArrowRightIcon className="h-5 w-5" aria-hidden="true" />
            </button>
          </div>
        </div>
      </div>
    </form>
  );
}
