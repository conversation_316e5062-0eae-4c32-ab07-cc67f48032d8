/*
  This example requires some changes to your config:
  
  ```
  // tailwind.config.js
  module.exports = {
    // ...
    plugins: [
      // ...
      require('@tailwindcss/forms'),
    ],
  }
  ```
*/
import { useState } from 'react';

import { ChatBubbleLeftIcon } from '@heroicons/react/24/outline';
import Cookies from 'js-cookie';
import { redirect, useLoaderData, useNavigate } from 'react-router-dom';

import { getMyInfo } from '../api.js';
import { useLanguage } from '../utils/context/lang.js';

import TextInput from './components/textInput.js';
import { agents } from './const/agent.js';
import { i18n } from './i18n/chat.js';

const people = [
  {
    name: 'What\'s chrono virus?',
    email: '<EMAIL>',
    role: 'Co-Founder / CEO',
    imageUrl:
      'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
  },
  {
    name: 'What\'s chrono virus?',
    email: '<EMAIL>',
    role: 'Co-Founder / CEO',
    imageUrl:
      'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
  },
  {
    name: 'What\'s chrono virus?',
    email: '<EMAIL>',
    role: 'Co-Founder / CEO',
    imageUrl:
      'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
  },
  {
    name: 'What\'s chrono virus?',
    email: '<EMAIL>',
    role: 'Co-Founder / CEO',
    imageUrl:
      'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
  },
]

function classNames(...classes) {
  return classes.filter(Boolean).join(' ');
}

export function AskLanding() {
  const { lang } = useLanguage();
  const myInfo = useLoaderData();
  console.log('in landing page', myInfo);

  return (
    <div className="relative flex py-10 flex-col h-screen w-full items-center justify-center">
      <h2 className="mb-8 text-3xl font-bold tracking-tight text-secondary-content sm:text-4xl">
        {i18n.MEET_AGENT[lang]}
      </h2>

      <TextInput         
        className="md:w-3/4 lg:w-2/3 w-full mx-auto left-0 right-0"
        onSubmit={()=>{}}
        enableUpload={true}
        disabled={false}
        areas={agents}
        onFileCountChange={(count) => {
          console.log(count)
        }}
      />
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:w-3/4 lg:w-2/3 w-full mx-auto left-0 right-0 mt-4">
        {people.map((person) => (
          <div
            key={person.email}
            className="relative flex items-center space-x-3 rounded-lg border border-gray-300 bg-white px-6 py-5 shadow-sm focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-offset-2 hover:border-gray-400"
          >
            <div className="flex-shrink-0">
              <span>😈</span>
            </div>
            <div className="min-w-0 flex-1">
              <a href="#" className="focus:outline-none">
                <span aria-hidden="true" className="absolute inset-0" />
                <p className="text-sm font-medium text-gray-900">{person.name}</p>
                {/* <p className="truncate text-sm text-gray-500">{person.role}</p> */}
              </a>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

export async function askLandingLoader() {
  console.log('in landing loader')
  const userName = Cookies.get('noahUser');
  if (userName) {
    console.log('有username!', userName)
    try {
      const result = await getMyInfo(userName);
      console.log(result)
      return result?.data
    } catch (error) {
      console.log('error', error)
      return null
      // return location.replace('/login');
    }
  } else {
    return location.replace('/signin');
  }
}
