const MODIFY_THIS_STEP = {
  en: 'You can modify this step through Feedback',
  zh: '您可以通过反馈修改此步骤',
};

const BACK = {
  en: 'Back',
  zh: '返回',
};

const YES = {
  en: 'Yes',
  zh: '确认',
};

const NO = {
  en: 'No',
  zh: '拒绝',
};

const SEND = {
  en: 'Send',
  zh: '发送',
};

const PROVIDE_FEEDBACK = {
  en: 'Provide feedback...',
  zh: '提供反馈...',
};

const DAILY_LIMIT_ERROR = {
  en: 'You have reached your usage limit',
  zh: '您已达到使用限制',
}

const NO_HISTORY = {
  en: 'No conversation history',
  zh: '无对话历史记录',
}

const LOADING_CONVERSATION = {
  en: 'Loading conversation...',
  zh: '正在加载对话...',
}

const NO_EVENT_YET = {
  en: 'No events yet',
  zh: '还没有事件',
}

const ENTER_QUESTION = {
  en: 'Please enter your question...',
  zh: '请输入你的问题...',
};

const COPY_SUCCESS = {
  en: 'Sharing successful, the sharing link has been copied to the clipboard',
  zh: '分享成功，分享链接已复制到剪切板',
}

const COPY_FAILED = {
  en: 'Sharing failed, please try again later',
  zh: '分享失败，请稍后再试',
}

const SHARE = {
  en: 'Share',
  zh: '分享',
}

const AUTO_ACCEPT = {
  en: 's auto accept',
  zh: '后自动接受',
}

const PREVIEW_DATA = {
  en: 'Preview Data',
  zh: '预览数据',
}

const TOOL = {
  en: 'Tool',
  zh: '工具',
}

const AGENT_STOPPED = {
  en: 'Task completed, time taken',
  zh: '任务完成, 用时',
}

const MINUTE = {
  en: 'm',
  zh: '分',
}

const SECOND = {
  en: 's',
  zh: '秒',
}
const CANCEL = {
  en: 'Cancel',
  zh: '取消',
}

const FINISH = {
  en: 'Confirm',
  zh: '确认',
}

const EDIT = {
  en: 'Edit',
  zh: '编辑',
}

const ACCEPT = {
  en: 'Accept',
  zh: '接受',
};

const EDIT_CHAT_EVENT = {
  en: 'Please confirm the current step, if adjustments are needed, you can edit the content directly.',
  zh: '请确认当前步骤，如需调整可直接编辑内容。',
};

const SHARE_DESCRIPTION = {
  en: 'Click copy to share the link with others',
  zh: '点击复制，分享链接给其他人',
}

const INVITED_ONLY_DESCRIPTION = {
  en: 'You need to log in and be invited to continue. Please follow @NoahAITech to get invited.',
  zh: '您需要登录并被邀请才能继续。请关注@NoahAITech获取邀请。',  
}

const CONTINUE = {
  en: 'Continue',
  zh: '继续',
}

const SG_MESSAGE = {
  en: 'Generating a synopsis based on the provided information...',
  zh: '根据提供的信息生成摘要...',
}

const NOTIFICATION_MESSAGE = {
  en: 'Noah has completed the current task',
  zh: 'Noah已完成当前任务',
}

const NOTIFICATION_TITLE = {
  en: 'Task Completed',
  zh: '任务完成',
}

export const i18n = {
  AGENT_STOPPED,
  TOOL,
  PREVIEW_DATA,
  MODIFY_THIS_STEP,
  BACK,
  YES,
  NO,
  SEND,
  PROVIDE_FEEDBACK,
  DAILY_LIMIT_ERROR,
  NO_HISTORY,
  LOADING_CONVERSATION,
  NO_EVENT_YET,
  ENTER_QUESTION,
  COPY_SUCCESS,
  COPY_FAILED,
  SHARE,
  AUTO_ACCEPT,
  MINUTE,
  SECOND,
  CANCEL,
  FINISH,
  EDIT,
  ACCEPT,
  EDIT_CHAT_EVENT,
  SHARE_DESCRIPTION,
  INVITED_ONLY_DESCRIPTION,
  CONTINUE,
  SG_MESSAGE,
  NOTIFICATION_MESSAGE,
  NOTIFICATION_TITLE,
}
