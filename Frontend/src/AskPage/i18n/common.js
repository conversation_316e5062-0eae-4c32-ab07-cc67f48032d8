const SUCCESS_COPY = {
  en: 'Content has been successfully copied to the clipboard.',
  zh: '内容已成功复制到剪贴板。',
};

const COPY = {
  en: 'Copy',
  zh: '复制',
}

const FAILED_COPY = {
  en: 'Failed to copy content to clipboard.',
  zh: '无法复制内容到剪贴板。',
}

const THOUGHT = {
  en: 'Thought',
  zh: '思考过程',
}

const UPGRADE_HINT = {
  en: 'Upgrade to Pro to unlock more features',
  zh: '升级到专业版以解锁更多功能',
}

const ANALYZING_UPLOAD = {
  en: 'Analyzing upload...',
  zh: '正在分析上传...',
}

const FILE_SIZE_EXCEEDED = {
  en: 'File size exceeded, 10MB limit.',
  zh: '文件大小超出限制, 10MB限制。',
}

const ANSWER = {
  en: 'Answer',
  zh: '回答',
}

const REFERENCE = {
  en: 'Reference',
  zh: '引用',
}

const FOLLOWUP_QUESTIONS = {
  en: 'Follow-up Questions',
  zh: '推荐问题',
}

const REPLY_LANGUAGE = {
  en: 'Reply Language',
  zh: '回复语言',
}

const ENTER_QUESTION= {
  en: 'Please enter your question',
  zh: '请输入你的问题',
}

const STOP_RUNNING = {
  en: 'Stop running',
  zh: '停止运行',
}

const AGENT_SEGMENTED = {
  en: 'Noah analyzes drug and clinical databases to provide detailed insights within 5-15 minutes, with interactive guidance.',
  zh: 'Noah 分析药物和临床数据库，在5-15分钟内提供详细见解，支持交互式引导。',
}

const SEARCH_SEGMENTED = {
  en: 'Noah quickly analyzes web resources to deliver accurate answers to your queries.',
  zh: 'Noah 快速分析网络资源，为您的问题提供准确答案。',
}

export const i18n = {
  SUCCESS_COPY,
  FAILED_COPY,
  THOUGHT,
  UPGRADE_HINT,
  ANALYZING_UPLOAD,
  FILE_SIZE_EXCEEDED,
  ANSWER,
  REFERENCE,
  FOLLOWUP_QUESTIONS,
  REPLY_LANGUAGE,
  COPY,
  ENTER_QUESTION,
  AGENT_SEGMENTED,
  SEARCH_SEGMENTED,
  STOP_RUNNING,
}
