import { useEffect, useState } from 'react';

import {
  ChatBubbleLeftIcon,
  CheckCircleIcon,
} from '@heroicons/react/24/outline';
import { Link, redirect, useLoaderData } from 'react-router-dom';

import { getHistoryList } from '../api.js';
import ReportDownloadBtn from '../Common/Components/ReportDownloadBtn.js';
import { useLanguage } from '../utils/context/lang.js';
import { timeAgo } from '../utils/date.js';
import { setPageTitle } from '../utils/helpers';
import useIntersectionObserver from '../utils/hooks/useIntersectionObserver.js';

import { ReactComponent as Add } from './assets/add.svg';
import { agents } from './const/agent.js';
import { i18n } from './i18n/history.js';

const typeUrlMap = {
  thread: '/hitl',
  report: '/report',
  hitl: '/hitl',
};

const formattedHistories = (histories) => {
  const currentYear = new Date().getFullYear();
  const thisYear = [];
  const lastYears = [];

  histories.forEach((history) => {
    const date = new Date(history.time_updated);
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();

    const formattedHistory = {
      ...history,
      formattedTime: year === currentYear
        ? `${month}月${day}日`
        : `${year}年${month}月${day}日`
    };

    if (year === currentYear) {
      thisYear.push(formattedHistory);
    } else {
      lastYears.push(formattedHistory);
    }
  });

  return {
    thisYear,
    lastYears
  };
};

export function AskHistory() {
  const { lang } = useLanguage();
  const [ data, setData ] = useState([]);
  const [ thisYearData, setThisYearData ] = useState([]);
  const [ yearAgoData, setYearAgoData ] = useState([]);
  const [ page, setPage ] = useState(2); // first page comes from loader
  const [ loading, setLoading ] = useState(false); // Lock state
  const [ hasMore, setHasMore ] = useState(true); // Termination condition state
  const histories = useLoaderData();

  useEffect(() => {
    setPageTitle(i18n.HISTORY[lang]);
  }, [ lang ]);

  useEffect(() => {
    const formattedData = formattedHistories([ ...histories, ...data ]);
    setThisYearData(formattedData.thisYear);
    setYearAgoData(formattedData.lastYears);
  }, [ histories, data ]);

  const loadMore = async () => {
    // If currently loading or no more data to fetch, return early.
    if (loading || !hasMore) return;

    setLoading(true);

    try {
      const newData = await getHistoryList(page);

      // Check if there's no more data to load (based on server response).
      // This logic might vary depending on the structure of your server's response.
      if (newData.length === 0) {
        setHasMore(false);
      } else {
        setData((prevData) => [ ...prevData, ...newData ]);
        setPage((prevPage) => prevPage + 1);
      }
    } catch (error) {
      console.error('Failed to fetch new data: ', error);
      // Handle errors as needed
    } finally {
      setLoading(false);
    }
  };

  const [ ref, isIntersecting ] = useIntersectionObserver({
    root: null,
    rootMargin: '0px',
    threshold: 0.6, // when the sentinel is fully visible
  });

  // Trigger 'loadMore' when the sentinel becomes visible
  useEffect(() => {
    if (isIntersecting && !loading && hasMore) {
      loadMore();
    }
  }, [ isIntersecting, loadMore ]);

  const renderItem = (items) => {
    return items.map((history) => {
      // depending on the type of history, render different components
      if (history.type === 'report')
        return (
          <div
            key={history.id}
            className="flex flex-wrap items-center justify-between gap-x-4 px-3 sm:flex-nowrap rounded-lg hover:bg-gray-3 transition-colors duration-200"
          >
            <a className="text-sm text-black flex-1 py-4 max-w-full [overflow-wrap:anywhere]" href={`${typeUrlMap[history.type]}/${history.id}`}>{history.name}</a>
            <ReportDownloadBtn status={history.status} attachmentKey={history.attachments_key} threadId={history.id} />
            <p className="text-xs text-gray-1 shrink-0">{history.formattedTime}</p>
          </div>
        );
      else 
        return (
          <div
            key={history.id}
            className="flex flex-wrap items-center justify-between gap-x-4 px-3 sm:flex-nowrap rounded-lg hover:bg-gray-3 transition-colors duration-200"
          >
            <a className="text-sm text-black flex-1 py-4 max-w-full [overflow-wrap:anywhere]" href={`${typeUrlMap[history.type]}/${history.id}`}>{history.name}</a>
            <p className="text-xs text-gray-1 shrink-0">{history.formattedTime}</p>
          </div>
        );
    })
  };

  if (!histories || histories.length === 0) {
    return (
      <div className="flex items-center justify-center h-screen">
        <h2 className="text-lg font-semibold text-secondary-content">
          No data available
        </h2>
      </div>
    );
  }

  return (
    <div className="mx-auto max-w-4xl p-6">
      <div className="border border-[#F1F1F1] rounded-2xl p-5 bg-white relative">
        <button
          type="button"
          className="text-sm font-semibold text-black
                    bg-gray-3 rounded-lg
                    transition-all duration-200 flex items-center gap-0.5 absolute right-5 top-5"
        >
          <Link
            className="flex items-center gap-0.5 px-3 py-2.5 "
            to="/hitl/" // TODO:liyang update link, delete?
          >
            <Add className="h-4 w-4" />
            <div>{i18n.NEW_CHAT[lang]}</div>
          </Link>
        </button>
        {
          thisYearData.length > 0 && (
            <>
              <div className="text-xl font-semibold text-black h-10 leading-10 mb-2">{i18n.THIS_YEAR[lang]}</div>
              {renderItem(thisYearData)}
            </>
          )
        }
        {yearAgoData.length > 0 && (
          <>
            <div className="text-xl font-semibold text-black h-10 leading-10 mb-2">{i18n.LAST_YEARS[lang]}</div>
            {renderItem(yearAgoData)}
          </>
        )}
        {hasMore && (
          <div className="text-center text-sm text-black" ref={ref}>
            {i18n.LOADING_MORE[lang]}
          </div>
        )}
      </div>
    </div>
  );
}

export async function askHistoryLoader() {
  return await getHistoryList();
}
