import React, { Suspense, useCallback, useEffect, useMemo, useRef, useState } from 'react';

import ClinicalTrialForm from './components/ClinicalTrialForm';
import ClinicalTrialFormPICOT from './components/ClinicalTrialFormPICOT';

export function PicoSynopsisDetail() {
  const containerRef = useRef(null);

  return (
    <div ref={containerRef} className="relative max-w-4xl mx-auto pb-10 bg-cream">
      {/* <ClinicalTrialForm /> */}
      <ClinicalTrialFormPICOT />
    </div>
  );
}

export async function picoSynopsisLoader(location) {
  return { };
}
