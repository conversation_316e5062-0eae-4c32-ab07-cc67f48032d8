/* 移除最外层边框 */
.ag-root-wrapper {
    border: none !important;
}

/* 设置表头样式 */
.ag-header {
    background-color: #ffffff !important;
    border-bottom: 2px solid #22222214 !important;
}

/* 设置表头单元格样式 */
.ag-header-cell {
    color: #22222299 !important;
    font-size: 14px !important;
    font-weight: 600 !important;
}

.ag-cell {
    color: #222222 !important;
    font-size: 14px !important;
    font-weight: 400 !important;
}

.ag-cell-focus {
    border: none !important;
}

/* 去掉行的横向边框 */
.ag-row {
    border-width: 0 !important;
}

/* 去掉单元格的边框 */
.ag-cell {
    border-right: none !important;
}

/* 自定义选择框样式 */
.ag-checkbox-input-wrapper {
    width: 16px !important;
    height: 16px !important;
    border-radius: 3px !important;
    border: 1px solid #b0b1b2 !important;
    background-color: white !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    cursor: pointer !important;
    transition: all 0.2s !important;
    position: relative !important;
}

.ag-checkbox-input-wrapper.ag-checked {
    background-color: #01a98f !important;
    border-color: #01a98f !important;
}

/* 修改默认的勾选图标 */
.ag-checkbox-input-wrapper::after {
    content: '' !important;
    width: 12px !important;  /* 稍微减小图标尺寸 */
    height: 12px !important;
    background-image: url("data:image/svg+xml,%3Csvg width='12' height='12' viewBox='0 0 12 12' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M10 3L4.5 8.5L2 6' stroke='white' stroke-width='1.6666' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") !important;
    background-repeat: no-repeat !important;
    background-position: center !important;
    background-size: contain !important; /* 确保图标适应容器 */
    opacity: 0 !important;
    transition: opacity 0.2s !important;
    position: absolute !important; /* 使用绝对定位 */
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important; /* 精确居中 */
}

.ag-checkbox-input-wrapper.ag-checked::after {
    opacity: 1 !important;
}

/* 悬停效果 */
.ag-checkbox-input-wrapper:hover {
    border-color: #01a98f !important;
}

/* 隐藏原始的checkbox input */
.ag-checkbox-input-wrapper input {
    display: none !important;
}

/* 调整选择列的宽度和对齐方式 */
.ag-selection-checkbox {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    padding: 0 !important;
    margin: 0 !important;
}

/* 修改行的边框样式 */
.ag-row {
    border-bottom: 1px solid #22222214 !important;
    border-width: 0 0 1px 0 !important;  /* 只保留下边框 */
}

/* 最后一行去掉边框 */
/* .ag-row:last-child {
    border-bottom: none !important;
} */

