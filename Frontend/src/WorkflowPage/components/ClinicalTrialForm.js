import React, { Suspense, useEffect, useState } from 'react';

import { 
  CloudArrowUpIcon,
  DocumentTextIcon,
} from '@heroicons/react/24/outline';
import {
  Button,
  Card,
  Checkbox,
  Col,
  Form,
  Input,
  message,
  Radio,
  Row,
  Select,
  Space,
  Upload,
} from 'antd';
import { saveAs } from 'file-saver';
import { marked } from 'marked';
import { useNavigate } from 'react-router-dom';

import { languages } from '../../AskPage/const/agent';
import { ReactComponent as ChevronDown } from '../../assets/arrow-down.svg';
import TransparentSelect from '../../Common/Components/TransparentSelect';
import { usePro } from '../../utils/context/pro';

const { TextArea } = Input;
const { Option } = Select;

const ClinicalTrialForm = () => {
  const navigate = useNavigate();
  const { switchAnswerLang, answerLang } = usePro();
  // 状态定义
  const [ form ] = Form.useForm();
  const [ uploadedDocs, setUploadedDocs ] = useState([]);
  const [ showResult, setShowResult ] = useState(false);
  const [ endpointType, setEndpointType ] = useState('byEndpointType');
  const [ formData, setFormData ] = useState(null);
  const [ previewMarkdown, setPreviewMarkdown ] = useState('');
  const [ sampleSizeDisabled, setSampleSizeDisabled ] = useState(false);
  const [ durationDisabled, setDurationDisabled ] = useState(false);
  const [ selectedDocType, setSelectedDocType ] = useState(null);
  const [ isOptionalExpanded, setIsOptionalExpanded ] = useState(true);
  const [ isFileExpanded, setIsFileExpanded ] = useState(true);

  // 添加文档类型选项
  const documentTypes = [
    { label: '临床前研究结果', value: 'preclinical' },
    { label: 'I、II临床研究方案', value: 'phase1-2-protocol' },
    { label: 'I、II临床研究结果', value: 'phase1-2-results' },
    { label: 'II、III期临床研究方案', value: 'phase2-3-protocol' },
    { label: 'II、III期临床研究结果', value: 'phase2-3-results' }
  ];


  const handlingFormDependencies = (name, value) => {
    const hasSampleSize = name === 'alpha' && value;
    const hasDuration = name === 'durationRange' && value;
    
    // 当填写了样本量时
    if (hasSampleSize) {
      // 重置并禁用研究终点和研究类型字段
      form.setFieldsValue({
        endpoints: undefined,
        studyType: undefined
      });
      setSampleSizeDisabled(true);
    } else {
      setSampleSizeDisabled(false);
    }

    // 当填写了持续时间时
    if (hasDuration) {
      // 重置并禁用研究终点、研究类型、样本量和site数量字段
      form.setFieldsValue({
        endpoints: undefined,
        studyType: undefined,
        sampleSize: undefined,
        siteCount: undefined
      });
      setDurationDisabled(true);
    } else {
      setDurationDisabled(false);
    }
  }

  // 处理表单提交
  const onFinish = (formData) => {
    const processedData = collectFormData(formData);
    console.log('收集到的表单数据:', processedData, formData);
    
    // object to string
    let stringData = '/SG ' + Object.entries(formData).reduce((acc, [ key, value ]) => {
      if (!value) return acc;
      if (Array.isArray(value)) {
        acc += `${key}=${value.join(',')}&`;
      } else {
        acc += `${key}=${value}&`;
      }
      return acc;
    }, '');

    // remove last & if  there is &
    if (stringData.endsWith('&')) {
      stringData = stringData.slice(0, -1);
    }
    // alert(stringData);
    window.open('/hitl?q=' + encodeURIComponent(stringData) + '&focusArea=mindsearchofficialsite');
    
    // const markdown = generateSynopsisMarkdown(processedData);
    // setPreviewMarkdown(markdown);
    // setShowResult(true);
  };

  const onFinishFailed = () => {
    message.error('请检查表单填写是否完整');
  };

  // 处理文件上传
  const handleUpload = (info) => {
    const { file } = info;
    message.success(`${file.name} 上传成功`);
    setUploadedDocs([ ...uploadedDocs, {
      type: selectedDocType,
      typeText: documentTypes.find(t => t.value === selectedDocType)?.label,
      fileName: file.name,
      fileSize: file.size,
      file: file
    } ]);
    // if (status === 'done') {
    //   message.success(`${info.file.name} 上传成功`);
    //   setUploadedDocs([ ...uploadedDocs, info.file ]);
    // } else if (status === 'error') {
    //   message.error(`${info.file.name} 上传失败`);
    // }
  };

  // 收集表单数据
  const collectFormData = (values) => {
    return {
      required: {
        indication: values.indication,
        phase: values.phase,
        treatment_line: values.treatment_line,
        health_condition: values.health_condition,
        sex: values.sex,
        age: values.age,
        intervention_model: values.intervention_model,
        masking: values.masking,
        outcome: values.outcome,
        location: values.location,
        drug: values.drug,
        route_of_administration: values.route_of_administration,
        drug_type: values.drug_type
      },
      optional: {
        endpoints: endpointType === 'byEndpointType' ? {
          primary: values.primary_endpoint,
          secondary: values.secondary_endpoint,
          exploratory: values.exploratory_endpoint
        } : {
          efficacy: values.efficacy_endpoint,
          safety: values.safety_endpoint,
          pkpd: values.pkpd_endpoint
        },
        stratification_factors: values.stratification_factors,
        sample_size: values.sample_size,
        duration: values.duration,
        site_count: values.site_count,
        interim_analysis: values.interim_analysis,
        regions: values.regions,
        other_requirements: values.other_requirements
      },
      documents: uploadedDocs,
      endpointType
    };
  };

  // 生成摘要的 Markdown
  const generateSynopsisMarkdown = (data) => {
    const { required, optional } = data;
    let markdown = `# ${required.indication} ${required.treatment_line ? required.treatment_line + '线' : ''} 临床试验方案摘要\n\n`;
    
    // 添加基本信息
    markdown += '## 基本信息\n\n';
    markdown += `- 适应症：${required.indication}\n`;
    markdown += `- 治疗线数：${
      required.treatment_line === 'firstLine' ? '初治/一线治疗' : 
        required.treatment_line === 'secondLine' ? '二线治疗' :
          required.treatment_line === 'laterLine' ? '后线治疗' :
            required.treatment_line === 'rr' ? '复发/难治' : required.treatment_line
    }\n`;
    
    // 格式化人群特征
    const ageMap = {
      'CHILD': '儿童',
      'ADULT': '成人',
      'OLDER_ADULT': '老年人'
    };
    const sexMap = {
      'Male': '男性',
      'Female': '女性',
      'Both': '不限'
    };
    const healthMap = {
      'Healthy Volunteers': '健康志愿者',
      'HIV-positive': 'HIV阳性',
      'Diabetic': '糖尿病',
      'Hypertensive': '高血压',
      'Obese': '肥胖',
      'Hepatic/Renal Impairment': '肝/肾功能损害',
      'Liver/Kidney Dysfunction': '肝/肾功能障碍'
    };

    markdown += '- 人群特征：\n';
    if (required.age) markdown += `  - 年龄组：${ageMap[required.age] || required.age}\n`;
    if (required.sex) markdown += `  - 性别：${sexMap[required.sex] || required.sex}\n`;
    if (required.health_condition) {
      markdown += `  - 健康状况：${Array.isArray(required.health_condition) ? 
        required.health_condition.map(h => healthMap[h] || h).join('、') : 
        (healthMap[required.health_condition] || required.health_condition)}\n`;
    }
    markdown += `- 临床试验阶段：Phase ${required.phase}\n\n`;

    // 添加研究药物信息
    markdown += '## 研究药物\n\n';
    markdown += `- 药物名称：${required.drug}\n`;
    markdown += `- 给药方式：${required.route_of_administration}\n`;
    markdown += `- 药物类型：${required.drug_type}\n\n`;

    // 添加研究设计信息
    if (required.intervention_model || required.masking) {
      markdown += '## 研究设计\n\n';
      if (required.intervention_model) {
        const modelMap = {
          'singleArm': '单臂',
          'controlled': '对照'
        };
        markdown += `- 研究臂：${modelMap[required.intervention_model] || required.intervention_model}\n`;
      }
      if (required.masking) {
        const maskingMap = {
          'openLabel': '开放',
          'singleBlind': '单盲',
          'doubleBlind': '双盲',
          'tripleBlind': '三盲'
        };
        markdown += `- 盲法：${maskingMap[required.masking] || required.masking}\n`;
      }
      markdown += '\n';
    }

    // 添加终点信息
    markdown += '## 研究终点\n\n';
    if (endpointType === 'byEndpointType') {
      if (optional.endpoints.primary) {
        markdown += `### 主要终点\n${optional.endpoints.primary}\n\n`;
      }
      if (optional.endpoints.secondary) {
        markdown += `### 次要终点\n${optional.endpoints.secondary}\n\n`;
      }
      if (optional.endpoints.exploratory) {
        markdown += `### 探索性终点\n${optional.endpoints.exploratory}\n\n`;
      }
    } else {
      if (optional.endpoints.efficacy) {
        markdown += `### 有效性终点\n${optional.endpoints.efficacy}\n\n`;
      }
      if (optional.endpoints.safety) {
        markdown += `### 安全性终点\n${optional.endpoints.safety}\n\n`;
      }
      if (optional.endpoints.pkpd) {
        markdown += `### 药代动力学/药效学终点\n${optional.endpoints.pkpd}\n\n`;
      }
    }

    // 添加地区信息
    if (required.location) {
      markdown += '## 研究地区\n\n';
      const locationMap = {
        'CN': '中国',
        'US': '美国',
        'EU': '欧盟',
        'JP': '日本',
        'KR': '韩国',
        'AU': '澳大利亚'
      };
      const locations = Array.isArray(required.location) ?
        required.location.map(l => locationMap[l] || l).join('、') :
        (locationMap[required.location] || required.location);
      markdown += `- 参与国家/地区：${locations}\n\n`;
    }

    return markdown;
  };

  // 下载 Markdown 文件
  const handleDownloadMarkdown = () => {
    const blob = new Blob([ previewMarkdown ], { type: 'text/markdown' });
    saveAs(blob, `clinical_trial_synopsis_${Date.now()}.md`);
  };

  // 下载 Word 文档
  const handleDownloadWord = () => {
    // ...之前的 Word 文档生成逻辑...
  };

  const handleAddDocument = (values) => {
    const { docType, file } = values.document || {};
    if (!docType || !file) {
      message.error('请选择文档类型并上传文件');
      return;
    }

    const newDoc = {
      uid: Date.now(),
      name: file.name,
      type: docType,
      status: 'done',
      url: URL.createObjectURL(file),
      originalFile: file
    };

    setUploadedDocs(prev => [ ...prev, newDoc ]);
    form.setFieldsValue({ document: undefined });
  };

  const handleDocumentRemove = (uid) => {
    setUploadedDocs(prev => prev.filter(doc => doc.file.uid !== uid));
  };

  const handleDocTypeChange = (value) => {
    setSelectedDocType(value);
  };

  const renderUploadDocumentSection = () => (
    <Card
      title={<button
        type="button"
        onClick={() => {
          setIsFileExpanded(!isFileExpanded);
        }}
        className="w-full flex items-center gap-3 text-sm transition-all duration-200 ease-in-out"
      >
        <span className="text-xl font-medium text-noah-theme">上传文档</span>

        <div className="flex-1" />
        <div className={`p-1 rounded-full transition-all duration-200 ${isFileExpanded ? 'rotate-180' : ''}`}>
          <ChevronDown className="w-5 h-5 text-gray-600" aria-hidden="true" />
        </div>
      </button>}
      className="shadow-md rounded-lg"
      bodyStyle={{
        padding: isFileExpanded ? '24px 24px 24px 24px' : '0 24px 0',
        maxHeight: isFileExpanded ? '2000px' : '0',
        opacity: isFileExpanded ? 1 : 0,
        overflow: isFileExpanded ? 'auto' : 'hidden',
        transition: 'all 0.3s ease-in-out',
      }}
    >
      <Form.Item name={[ 'document', 'docType' ]} label="文档类型" className="mb-0">
        <Select 
          placeholder="请选择文档类型" 
          options={documentTypes}
          className="mb-4"
          onChange={handleDocTypeChange}
        />
      </Form.Item>

      <Form.Item 
        name={[ 'document', 'file' ]}
        label="上传文件"
        valuePropName="fileList"
        getValueFromEvent={e => Array.isArray(e) ? e : e?.fileList}
      >
        <Upload.Dragger
          beforeUpload={(file) => {
            // 检查文件类型
            const isValid = /\.(pdf|doc|docx|xls|xlsx|ppt|pptx)$/i.test(file.name);
            if (!isValid) {
              message.error('只支持 PDF、Word、Excel、PowerPoint 格式');
              return false;
            }
            return false; // 阻止自动上传
          }}
          maxCount={1}
          className="p-6"
          accept=".pdf, .doc, .docx, .xls, .xlsx, .ppt, .pptx"
          onChange={handleUpload}
          disabled={!selectedDocType}
          showUploadList={false}
        >
          <p className="text-4xl text-gray-400 mb-4">
            <CloudArrowUpIcon className="w-12 h-12 mx-auto" />
          </p>
          <p className="text-base mb-2">
            {!selectedDocType ? '请先选择文档类型' : '点击或拖拽文件到此处上传'}
          </p>
          <p className="text-sm text-gray-500">支持 PDF、Word、Excel、PowerPoint 格式</p>
        </Upload.Dragger>
      </Form.Item>

      {/* <Button 
        type="dashed" 
        onClick={() => form.validateFields([ 'document' ]).then(handleAddDocument)}
        className="mb-4"
      >
        添加文档
      </Button> */}

      {/* 已上传文档列表 */}
      {uploadedDocs.length > 0 && (
        <div className="mt-6">
          <div className="text-lg font-medium mb-4">已上传文档</div>
          <div className="space-y-2">
            {uploadedDocs.map(doc => (
              <div 
                key={doc.file.uid} 
                className="flex justify-between items-center p-3 bg-gray-50 rounded-md"
              >
                <div>
                  <span className="font-medium">
                    {doc.typeText}
                  </span>
                  <span className="ml-2 text-gray-500">{doc.fileName}</span>
                </div>
                <Button 
                  type="text" 
                  danger 
                  onClick={() => handleDocumentRemove(doc.file.uid)}
                  className="flex items-center"
                >
                  删除
                </Button>
              </div>
            ))}
          </div>
        </div>
      )}
    </Card>
  );

  const renderEndpointSection = (type) => (
    <Row gutter={16}>
      {type === 'byEndpointType' ? (
        <>
          <Col span={8}>
            <Form.Item 
              name="primary_endpoint"
              label="主要终点"
              className="mb-4"
            >
              <TextArea 
                rows={4} 
                disabled={sampleSizeDisabled || durationDisabled}
                placeholder="例如：6个月PFS率、ORR、CR率等" 
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item 
              name="secondary_endpoint"
              label="次要终点"
              className="mb-4"
            >
              <TextArea 
                rows={4} 
                disabled={sampleSizeDisabled || durationDisabled}
                placeholder="例如：OS、DCR、DOR、安全性等" 
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item 
              name="exploratory_endpoint"
              label="探索性终点"
              className="mb-4"
            >
              <TextArea 
                rows={4} 
                disabled={sampleSizeDisabled || durationDisabled}
                placeholder="例如：生物标志物、QoL等" 
              />
            </Form.Item>
          </Col>
        </>
      ) : (
        <>
          <Col span={8}>
            <Form.Item 
              name="efficacy_endpoint"
              label="有效性终点"
              className="mb-4"
            >
              <TextArea 
                rows={4} 
                disabled={sampleSizeDisabled || durationDisabled}
                placeholder="例如：PFS、OS、ORR等" 
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item 
              name="safety_endpoint"
              label="安全性终点"
              className="mb-4"
            >
              <TextArea 
                rows={4} 
                disabled={sampleSizeDisabled || durationDisabled}
                placeholder="例如：AE、SAE、TEAE等" 
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item 
              name="pkpd_endpoint"
              label="药代动力学/药效学终点"
              className="mb-4"
            >
              <TextArea 
                rows={4} 
                disabled={sampleSizeDisabled || durationDisabled}
                placeholder="例如：血药浓度、药时曲线、生物标志物等" 
              />
            </Form.Item>
          </Col>
        </>
      )}
    </Row>
  );

  return (
    <div className="max-w-5xl mx-auto p-8 bg-gray-50 min-h-screen">
      <div className="mb-8 text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">临床试验方案生成器</h1>
        <p className="text-lg text-gray-600">填写以下信息，自动生成专业的临床试验方案摘要</p>
      </div>
      <Form
        form={form}
        layout="vertical"
        onFinish={onFinish}
        onFinishFailed={onFinishFailed}
        className="space-y-8"
      >
        {/* 必填内容卡片 */}
        <Card 
          title={<span className="text-2xl font-bold text-noah-theme">必填内容</span>}
          className="shadow-lg rounded-lg border-0"
        >
          <div className="text-xl font-semibold text-gray-800 mb-6 pb-3 border-b border-gray-200">适应症</div>
          <Row gutter={[ 24, 16 ]}>
            <Col span={8}>
              <Form.Item
                label={<span className="text-sm font-semibold text-gray-700">疾病 *</span>}
                name="indication"
                rules={[ { required: true, message: '请输入疾病' } ]}
                className="mb-6"
              >
                <Input placeholder="如：非小细胞肺癌" className="rounded-md " />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label={<span className="text-sm font-semibold text-gray-700">治疗线数</span>}
                name="treatment_line"
                rules={[ { required: false, message: '请选择治疗线数' } ]}
                className="mb-6"
              >
                <Select placeholder="请选择治疗线数" className="">
                  <Option value="first-line">初治/一线治疗</Option>
                  <Option value="second-line">二线治疗</Option>
                  <Option value="later-line">后线治疗</Option>
                  <Option value="rr">复发/难治</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label={<span className="text-sm font-semibold text-gray-700">年龄组</span>}
                name={ [ 'age' ] }
                rules={[ { required: false, message: '请选择年龄组' } ]}
                className="mb-6"
              >
                <Select placeholder="请选择年龄组" className="">
                  <Option value="CHILD">儿童</Option>
                  <Option value="ADULT">成人</Option>
                  <Option value="OLDER_ADULT">老年人</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={[ 24, 16 ]}>
            <Col span={8}>
              <Form.Item
                label={<span className="text-sm font-semibold text-gray-700">性别</span>}
                name={ [ 'sex' ] }
                rules={[ { required: false, message: '请选择性别' } ]}
                className="mb-6"
              >
                <Select placeholder="请选择性别" className="">
                  <Option value="Male">男性</Option>
                  <Option value="Female">女性</Option>
                  <Option value="All">不限</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={16}>
              <Form.Item
                label={<span className="text-sm font-semibold text-gray-700">健康状况</span>}
                name={ [ 'health_condition' ] }
                rules={[ { required: false, message: '请选择健康状况' } ]}
                className="mb-6"
              >
                <Select 
                  placeholder="请选择健康状况"
                  mode="multiple"
                  className=""
                >
                  <Option value="Healthy Volunteers">健康志愿者</Option>
                  <Option value="HIV-positive">HIV阳性</Option>
                  <Option value="Diabetic">糖尿病</Option>
                  <Option value="Hypertensive">高血压</Option>
                  <Option value="Obese">肥胖</Option>
                  <Option value="Hepatic/Renal Impairment">肝/肾功能损害</Option>
                  <Option value="Liver/Kidney Dysfunction">肝/肾功能障碍</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          {/* 临床试验阶段 */}
          <div className="text-xl font-semibold text-gray-800 mb-6 pb-3 border-b border-gray-200 mt-8">临床试验阶段</div>
          <Form.Item
            label={<span className="text-sm font-semibold text-gray-700">试验阶段 *</span>}
            name="phase"
            rules={[ { required: true, message: '请选择临床试验阶段' } ]}
            className="mb-6"
          >
            <Select placeholder="请选择临床试验阶段" className="">
              <Option value="1">Phase 1</Option>
              <Option value="1/2">Phase 1/2</Option>
              <Option value="2">Phase 2</Option>
              <Option value="2/3">Phase 2/3</Option>
              <Option value="3">Phase 3</Option>
              <Option value="4">Phase 4</Option>
            </Select>
          </Form.Item>

          {/* 研究药物 */}
          <div className="text-xl font-semibold text-gray-800 mb-6 pb-3 border-b border-gray-200 mt-8">研究药物</div>
          <Row gutter={[ 24, 16 ]}>
            <Col span={8}>
              <Form.Item
                label={<span className="text-sm font-semibold text-gray-700">药物名称 *</span>}
                name="drug"
                rules={[ { required: true, message: '请输入药物名称' } ]}
                className="mb-6"
              >
                <Input placeholder="如：Pembrolizumab" className="" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label={<span className="text-sm font-semibold text-gray-700">给药方式 *</span>}
                name="route_of_administration"
                rules={[ { required: true, message: '请选择给药方式' } ]}
                className="mb-6"
              >
                <Select placeholder="请选择给药方式" className="">
                  <Option value="oral">口服</Option>
                  <Option value="iv">静脉注射</Option>
                  <Option value="sc">皮下注射</Option>
                  <Option value="im">肌肉注射</Option>
                  <Option value="inhale">吸入</Option>
                  <Option value="topical">局部用药</Option>
                  <Option value="other">其他</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label={<span className="text-sm font-semibold text-gray-700">药物类型 *</span>}
                name="drug_type"
                rules={[ { required: true, message: '请选择药物类型' } ]}
                className="mb-6"
              >
                <Select placeholder="请选择药物类型" className="">
                  <Option value="smallMolecule">小分子</Option>
                  <Option value="antibody">单抗</Option>
                  <Option value="adc">ADC</Option>
                  <Option value="cellTherapy">细胞疗法</Option>
                  <Option value="geneTherapy">基因疗法</Option>
                  <Option value="vaccine">疫苗</Option>
                  <Option value="other">其他</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* 选填内容卡片 */}
        <Card 
          title={<button
            type="button"
            onClick={() => {
              setIsOptionalExpanded(!isOptionalExpanded);
            }}
            className="w-full flex items-center gap-3 text-sm transition-all duration-200 ease-in-out"
          >
            <span className="text-2xl font-bold text-noah-theme">选填内容</span>

            <div className="flex-1" />
            <div className={`p-1 rounded-full transition-all duration-200 ${isOptionalExpanded ? 'rotate-180' : ''}`}>
              <ChevronDown className="w-5 h-5 text-gray-600" aria-hidden="true" />
            </div>
          </button>}
          className="shadow-lg rounded-lg border-0"
          bodyStyle={{
            padding: isOptionalExpanded ? '24px 24px 24px 24px' : '0 24px 0',
            maxHeight: isOptionalExpanded ? '2000px' : '0',
            opacity: isOptionalExpanded ? 1 : 0,
            overflow: isOptionalExpanded ? 'auto' : 'hidden',
            transition: 'all 0.3s ease-in-out',
          }}
        >
          {/* 终点部分 */}
          <div>
            <div className="text-xl font-semibold text-gray-800 mb-6 pb-3 border-b border-gray-200">终点</div>
            <div className="bg-aqua rounded-md p-4 border-l-2 border-noah-theme mb-4">
              <div className="text-base font-medium text-base-content">选择终点分类方式</div>
              <Form.Item className="mt-2 mb-0">
                <Radio.Group
                  value={endpointType}
                  onChange={(e) => setEndpointType(e.target.value)}
                  className="space-x-4"
                >
                  <Radio value="byEndpointType" disabled={sampleSizeDisabled || durationDisabled}>按终点类型分类</Radio>
                  <Radio value="byCategory" disabled={sampleSizeDisabled || durationDisabled}>按类别分类</Radio>
                </Radio.Group>
              </Form.Item>
            </div>
            {/* 终点输入部分 */}
            <div className="space-y-2">
              {renderEndpointSection(endpointType)}
            </div>
          </div>

          {/* 研究类型 */}
          <div className="space-y-4 mt-8">
            <div className="text-xl font-semibold text-gray-800 mb-6 pb-3 border-b border-gray-200">研究类型</div>
            <Row gutter={[ 24, 16 ]}>
              <Col span={12}>
                <Form.Item 
                  name="intervention_model" 
                  label={<span className="text-sm font-semibold text-gray-700">研究臂</span>}
                  rules={[ { required: false } ]}
                  className="mb-6"
                >
                  <Select 
                    placeholder="请选择"
                    disabled={sampleSizeDisabled || durationDisabled}
                    className=""
                  >
                    <Option value="single_group">单臂</Option>
                    <Option value="parallel">平行</Option>
                    <Option value="crossover">交叉</Option>
                    <Option value="factorial">因子</Option>
                    <Option value="sequential">序贯</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item 
                  name="masking" 
                  label={<span className="text-sm font-semibold text-gray-700">盲法</span>}
                  rules={[ { required: false } ]}
                  className="mb-6"
                >
                  <Select
                    placeholder="请选择"
                    disabled={sampleSizeDisabled || durationDisabled}
                    className=""
                  >
                    <Option value="none">开放</Option>
                    <Option value="single">单盲</Option>
                    <Option value="double">双盲</Option>
                    <Option value="triple">三盲</Option>
                    <Option value="quadruple">四盲</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>
          </div>

          {/* 分层因素部分 */}
          <div className="space-y-4 mt-8">
            <div className="text-xl font-semibold text-gray-800 mb-6 pb-3 border-b border-gray-200">分层因素</div>
            <Form.Item name="stratification_factors">
              <Checkbox.Group className="space-x-4 mb-4">
                <Checkbox value="ageStratification">年龄</Checkbox>
                <Checkbox value="mutationStratification">突变</Checkbox>
                <Checkbox value="priorTreatmentStratification">前线治疗</Checkbox>
              </Checkbox.Group>
            </Form.Item>
          </div>

          {/* 样本量部分 */}
          <div className="space-y-4 mt-8">
            <div className="text-xl font-semibold text-gray-800 mb-6 pb-3 border-b border-gray-200">样本量</div>
            <Row gutter={[ 24, 16 ]}>
              <Col span={12}>
                <Form.Item 
                  name="control_value" 
                  label={<span className="text-sm font-semibold text-gray-700">标准治疗预测值</span>}
                  className="mb-6"
                >
                  <Input 
                    addonAfter="%" 
                    placeholder="文献数据"
                    disabled={durationDisabled}
                    className=""
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item 
                  name="experimental_value" 
                  label={<span className="text-sm font-semibold text-gray-700">实验组预测值</span>}
                  className="mb-6"
                >
                  <Input 
                    addonAfter="%" 
                    placeholder="竞品/自填"
                    disabled={durationDisabled}
                    className=""
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item 
                  name="p_value" 
                  label={<span className="text-sm font-semibold text-gray-700">P值</span>}
                  className="mb-6"
                >
                  <Input 
                    placeholder="如: 0.05"
                    disabled={durationDisabled}
                    className=""
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item 
                  name="sample_size_range" 
                  label={<span className="text-sm font-semibold text-gray-700">预估样本范围</span>}
                  className="mb-2"
                >
                  <Input 
                    placeholder="如: 100-150"
                    disabled={durationDisabled}
                    className=""
                    onChange={(e) => {
                      const value = e.target.value;
                      if (value) {
                        const [ min, max ] = value.split('-').map(Number);
                        if (min && max && min > max) {
                          message.error('最小值不能大于最大值');
                        }
                      }
                      handlingFormDependencies('alpha', value);
                    }}
                  />
                </Form.Item>
                <div className="text-amber-600 text-sm font-medium bg-amber-50 p-2 rounded">
                  ⚠️ 注意：填写此项后，研究终点和研究类型将无法填写
                </div>
              </Col>
            </Row>
          </div>

          {/* 其他选填内容 */}
          <div className="space-y-4 mt-8">
            <div className="text-xl font-semibold text-gray-800 mb-6 pb-3 border-b border-gray-200">持续时间</div>
            {/* 持续时间部分 */}
            <Form.Item>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item name={[ 'enrollmentSpeed' ]} label="预测入组速度/site">
                    <Input 
                      addonAfter="患者/月/site" 
                      placeholder="如: 2"
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name={[ 'durationRange' ]} label="预估持续时间范围" className="mb-2">
                    <Input 
                      addonAfter="月" 
                      placeholder="如: 24-36"
                      onChange={(e) => {
                        const value = e.target.value;
                        if (value) {
                          const [ min, max ] = value.split('-').map(Number);
                          if (min && max && min > max) {
                            message.error('最小值不能大于最大值');
                          }
                        }
                        handlingFormDependencies('durationRange', value);
                      }}
                    />
                  </Form.Item>
                  <div className="text-warning">注意：填写此项后，研究终点、研究类型、样本量、site数量均不能填写</div>
                </Col>
              </Row>
            </Form.Item>
          </div>

          {/* Site数量 */}
          <div className="space-y-4 mt-2">
            <div className="text-lg font-medium text-base-content mb-2">Site数量</div>
            <Form.Item name="site_count">
              <Input 
                placeholder="如: 20" 
                type="number"
                min={0}
                disabled={durationDisabled}
              />
            </Form.Item>
          </div>

          {/* 中期分析 */}
          <div className="mt-4">
            <div className="text-lg font-medium text-base-content mb-2">中期分析</div>
            <Form.Item name="interim_analysis">
              <Radio.Group>
                <Radio value="interimYes">是</Radio>
                <Radio value="interimNo">否</Radio>
              </Radio.Group>
            </Form.Item>
          </div>

          {/* 研究地区 */}
          <div className="mt-4">
            <div className="text-lg font-medium text-base-content mb-2">研究地区</div>
            <Form.Item>
              <Row gutter={[ 16, 16 ]}>
                <Col span={24}>
                  <Form.Item name="location_type">
                    <Radio.Group>
                      <Radio value="domestic">国内研究</Radio>
                      <Radio value="global">全球多中心</Radio>
                    </Radio.Group>
                  </Form.Item>
                </Col>
                <Col span={24}>
                  <Form.Item name="location" label="参与国家/地区">
                    <Select
                      mode="multiple"
                      placeholder="请选择参与国家/地区"
                      options={[
                        { label: '中国', value: 'CN' },
                        { label: '美国', value: 'US' },
                        { label: '欧盟', value: 'EU' },
                        { label: '日本', value: 'JP' },
                        { label: '韩国', value: 'KR' },
                        { label: '澳大利亚', value: 'AU' },
                        { label: '其他', value: 'OTHER' }
                      ]}
                    />
                  </Form.Item>
                </Col>
              </Row>
            </Form.Item>
          </div>
          {/* 其他要求 */}
          <div>
            <div className="text-lg font-medium text-base-content mb-2">其他要求</div>
            <Form.Item name="additionalRequirements">
              <TextArea rows={4} placeholder="请输入其他特殊要求或说明" />
            </Form.Item>
          </div>

          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600">回答语言:</span>
            <TransparentSelect
              className="w-auto"
              dropdownStyle={{
                minWidth: '100px',
              }}
              value={answerLang}
              optionLabelProp="label"
              onChange={(e) => switchAnswerLang(e.value)}
              labelInValue
              needBlur={true}
              labelRender={(label) => (
                <div className="flex items-center justify-center flex-1 font-medium text-sm !text-black-1">
                  {label.title}
                </div>
              )}
              items={languages.map(item => {
                return { 
                  value: item.code, 
                  label: item.name, 
                  current: item.code === answerLang 
                }
              })}
            />
          </div>
        </Card>

        {/* 上传文档部分 */}
        {/* {renderUploadDocumentSection()} */}

        {/* 操作按钮 */}
        <div className="flex justify-end space-x-6 pt-8 border-t border-gray-200">
          <button
            type="button"
            onClick={() => form.resetFields()}
            className="px-6 py-3 text-base font-semibold text-gray-700
                    bg-gray-100 hover:bg-gray-200 rounded-lg
                    transition-all duration-200 text-center border border-gray-300
                    focus:outline-none focus:ring-2 focus:ring-gray-400"
          >
            重置表单
          </button>
          <button
            type="submit"
            className="px-6 py-3 text-base font-semibold text-white 
                    bg-noah-theme rounded-lg
                    transition-all duration-200 text-center shadow-lg hover:bg-noah-theme/90"
          >
            生成方案摘要
          </button>
        </div>
      </Form>

      {/* 结果展示 */}
      {showResult && (
        <Card
          title={<span className="text-2xl font-bold text-noah-theme">生成结果</span>}
          extra={
            <Space className="space-x-4">
              <button 
                onClick={handleDownloadMarkdown}
                className="px-4 py-2 text-sm font-semibold text-white 
                    bg-noah-theme hover:bg-blue-700 rounded-lg
                    transition-all duration-200 text-center flex items-center space-x-2
                    focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <DocumentTextIcon className="w-5 h-5" />
                <div>下载 Markdown</div>
              </button>
              <button
                onClick={handleDownloadWord}
                className="px-4 py-2 text-sm font-semibold text-white 
                    bg-noah-theme hover:bg-blue-700 rounded-lg
                    transition-all duration-200 text-center flex items-center space-x-2
                    focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <DocumentTextIcon className="w-5 h-5" />
                <div>下载 Word</div>
              </button>
            </Space>
          }
          className="mt-8 shadow-lg rounded-lg border-0"
        >
          <div 
            className="prose max-w-none"
            dangerouslySetInnerHTML={{ __html: marked(previewMarkdown) }}
          />
        </Card>
      )}
    </div>
  );
};

export default ClinicalTrialForm;
