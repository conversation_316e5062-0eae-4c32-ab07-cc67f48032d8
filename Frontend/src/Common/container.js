import { Children, Fragment, useEffect, useRef, useState } from 'react';

import {
  Dialog,
  Popover,
  Transition,
} from '@headlessui/react';
import {
  Bars3Icon,
  XMarkIcon,
} from '@heroicons/react/24/outline';
import { DiscordLogoIcon } from '@phosphor-icons/react';
import Cookies from 'js-cookie';
import {
  Link,
  Outlet,
  useLocation,
  useNavigate,
} from 'react-router-dom';

import { getHistoryList, getUserList } from '../api';
import noah_logo_black from '../assets/logo-black.png';
import noah_logo_white from '../assets/logo-white.png';
import { ReactComponent as UserIcon } from '../assets/user-icon.svg';
import { useHistory } from '../utils/context/history';
import { useLanguage } from '../utils/context/lang';
import { useLayout } from '../utils/context/layout';
import { useLayoutWidth } from '../utils/context/layout-width';
import { useWebSocketContext } from '../utils/context/main-websocket';
import { usePro } from '../utils/context/pro';
import { useTheme } from '../utils/context/theme-system';
import { useUser } from '../utils/context/user';

import { ReactComponent as chat_icon } from './assets/chat-icon.svg';
import { ReactComponent as chat_select } from './assets/chat-select-icon.svg';
import { ReactComponent as ChevronDownSelect } from './assets/chevron-down-select.svg';
import { ReactComponent as ChevronDown } from './assets/chevron-down.svg';
import { ReactComponent as conference_icon } from './assets/conference-icon.svg';
import { ReactComponent as conference_select } from './assets/conference-select-icon.svg';
import { ReactComponent as FeedbackDarkIcon } from './assets/feedback-icon-dark.svg';
import { ReactComponent as FeedbackWhiteIcon } from './assets/feedback-icon-white.svg';
import { ReactComponent as FeedbackIcon } from './assets/feedback-icon.svg';
import { ReactComponent as history_icon } from './assets/history-icon.svg';
import { ReactComponent as history_select } from './assets/history-select-icon.svg';
import { ReactComponent as home_icon } from './assets/home-icon.svg';
import { ReactComponent as home_select } from './assets/home-select-icon.svg';
import { ReactComponent as LogoutDarkIcon } from './assets/logout-icon-dark.svg';
import { ReactComponent as LogoutIcon } from './assets/logout-icon.svg';
import { ReactComponent as news_icon } from './assets/news-icon.svg';
import { ReactComponent as news_select } from './assets/news-select-icon.svg';
import { ReactComponent as search_icon } from './assets/search-icon.svg';
import { ReactComponent as search_select } from './assets/search-select-icon.svg';
import chevron_right from './assets/semi-icons-chevron_right.png';
import { ReactComponent as SetDarkIcon } from './assets/set-icon-dark.svg';
import { ReactComponent as SetIcon } from './assets/set-icon.svg';
import toggle_icon from './assets/toggle-icon.png';
import SettingsBar from './Components/SettingsBar';
import Tooltip from './Components/Tooltip';
import TransparentSelect from './Components/TransparentSelect';
import { i18n } from './i18n/menu';

const langMap = {
  en: 'EN',
  zh: 'CN',
  jp: 'JP',
};

const teams = [
  { id: 1, name: 'Heroicons', href: '#', initial: 'H', current: false },
  { id: 2, name: 'Tailwind Labs', href: '#', initial: 'T', current: false },
  { id: 3, name: 'Workcation', href: '#', initial: 'W', current: false },
];

function classNames(...classes) {
  return classes.filter(Boolean).join(' ');
}

// TODO MILD: ziwen. add ErrorBoundary
export function MainContainer({ children }) {
  // const isProd = process.env.NODE_ENV === 'production';
  const [ sidebarOpen, setSidebarOpen ] = useState(false);
  const [ expandedNavBarList, setExpandedNavBarList ] = useState([]);
  const [ isSettingsPath, setIsSettingsPath ] = useState(false);
  const [ renderSidebar, setRenderSidebar ] = useState(false);
  const { isSidebarCollapsed, setIsSidebarCollapsed } = useLayout();
  const { user, logout, showModal, hideModal } = useUser();
  const { latestMessage, usedCredits: wsUsedCredits, totalCredits: wsTotalCredits } = useWebSocketContext();
  const { lang, toggleLanguage, selectLanguage } = useLanguage();
  const { historyResult } = useHistory();
  const { switchHistoryMenu, historyMenuOpen, switchAnswerLang } = usePro();
  const { pathname } = useLocation();
  
  // Check if current URL is a share page
  const isSharePage = pathname.startsWith('/share/');

  // Get credits from WebSocket context with user context fallback
  const currentUsedCredits = wsUsedCredits || user?.usedCredits || 0;
  const currentTotalCredits = wsTotalCredits || user?.totalCredits || 100;

  const [ navigation, setNavigation ] = useState([
    // {
    //   name: i18n.HOME[lang],
    //   href: '/home',
    //   icon: home_icon,
    //   selectIcon: home_select,
    //   current: false,
    // },
    {
      name: i18n.HITL[lang],
      href: '/hitl/',
      icon: chat_icon,
      selectIcon: chat_select,
      current: false,
    },
    {
      name: i18n.DISCOVER[lang],
      href: '/discover/',
      icon: news_icon,
      selectIcon: news_select,
      current: false,
    },
    {
      children: [
        {
          name: i18n.CLINICAL_TRIAL_RESULT[lang],
          href: '/tool/clinical-result/',
          current: false,
        },
        {
          name: i18n.DRUG_COMPETE[lang],
          href: '/tool/drug-compete?search_param=%7B%22location%22%3A%5B%22USA%22%2C%22China%22%5D%7D',
          current: false,
        },
        {
          name: i18n.CATALYST[lang],
          href: '/tool/catalyst/',
          current: false,
        },
        {
          name: i18n.GUIDANCE[lang],
          href: '/tool/clinical-guidelines/',
          current: false,
        },
      ],
      href: '/tool/',
      current: false,
      name: i18n.SEARCH[lang],
      icon: search_icon,
      selectIcon: search_select,
    },
    {
      name: i18n.CONFERENCE[lang],
      href: '/conference/',
      icon: conference_icon,
      selectIcon: conference_select,
      current: false,
    },
    {
      name: i18n.HISTORY[lang],
      href: '/ask/history',
      icon: history_icon,
      selectIcon: history_select,
      current: false,
    },
  ]);
  const [ languageSelectOpen, setLanguageSelectOpen ] = useState(false);
  const [ navSelectOpen, setNavSelectOpen ] = useState('');
  const [ isNavHovered, setIsNavHovered ] = useState(false);
  const { setMainWidth } = useLayoutWidth();
  const mainRef = useRef(null);
  const closePopoverRef = useRef(null);
  const navigate = useNavigate();

  // Add resize observer
  useEffect(() => {
    if (!mainRef.current) return;
    
    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        setMainWidth(entry.contentRect.width);
      }
    });

    resizeObserver.observe(mainRef.current);
    return () => resizeObserver.disconnect();
  }, []);
  
  // useEffect(() => {
  //   console.log('historyResult', historyResult);
  //   if (historyResult && historyResult.length) {
  //     setNavigation((prev) => {
  //       return prev.map((item) => {
  //         if (item.name === i18n.HISTORY[lang]) {
  //           return {
  //             ...item,
  //             children: historyResult.map((item) =>
  //               item.type === 'thread'
  //                 ? {
  //                   name: item.name,
  //                   href: `/ask/${item.id}`,
  //                   id: item.id,
  //                 }
  //                 : {
  //                   name: item.name,
  //                   href: `/tool/${item.id}`,
  //                   id: item.id,
  //                 }
  //             ),
  //           };
  //         }
  //         return item;
  //       });
  //     });
  //   }
  // }, [ historyResult ]);

  useEffect(() => {
    if (pathname) {
      setNavigation((prev) => {
        return prev.map((item) => {
          if (item.href === '/ask/' && pathname.startsWith('/ask/history')) {
            item.current = false;
          } else {
            let href = item.href.split('?')[0];
            href.endsWith('/') ? href = href.slice(0, -1) : href;
            item.current = pathname.startsWith(href);
          }
          if (item.children?.length) {
            item.children.forEach((subItem) => {
              let href = subItem.href.split('?')[0];
              href.endsWith('/') ? href = href.slice(0, -1) : href;
              subItem.current = pathname.startsWith(href);
            });
          }
          return item;
        });
      });

      setIsSettingsPath(pathname.startsWith('/settings'));
      setRenderSidebar(true);
    }
  }, [ pathname ]);

  const { theme } = useTheme();

  const navigationClick = (href) => {    
    if (!isSidebarCollapsed && href !== '/ask/') {
      setIsSidebarCollapsed(true)
    }
    setNavigation((prev) => {
      return prev.map((item) => {
        if (item.children?.length) {
          item.children.forEach((subItem) => {
            subItem.current = subItem.href === href;
          });
          item.current = item.children.some((subItem) => subItem.current);
        } else {
          item.current = item.href === href;
        }
        return item;
      });
    });
  }

  const UserSection = (isCollapsed, isMobile) => {
    return (
      <div className="shrink-0">
        <>
          {!user && (
            <div
              className={`relative text-sm font-semibold leading-6 text-secondary-content ${isMobile ? '' : 'py-2 border-t border-gray-200'}`}
            >
              <div
                className={`flex items-center w-full ${isCollapsed ? 'justify-center' : 'justify-between'}`}
              >
                {
                  !isCollapsed ?
                    <>
                      <div
                        className="flex flex-1 items-center cursor-pointer"
                        onClick={() => showModal()}
                      >
                        <UserIcon className="h-8 w-8 shrink-0" />
                        <span
                          className="truncate max-w-[148px] flex-1 ml-1"
                        >
                          {i18n.LOGIN_NOW[lang]}
                        </span>
                      </div>
                      <Link
                        className="group h-6 w-6 cursor-pointer flex items-center justify-center rounded-lg hover:bg-gray-3"
                        to="/settings/account"
                      >
                        <div className="w-5 h-5"> 
                          <SetIcon className="block group-hover:hidden w-full h-full" />
                          <SetDarkIcon className="hidden group-hover:block w-full h-full" /> 
                        </div>
                      </Link>
                    </> : <UserIcon className="h-8 w-8 shrink-0 cursor-pointer" onClick={() => showModal()} />
                }
              </div>
            </div>
          )}
          {user?.username && (
            <div className={`text-sm font-semibold leading-6 text-[#222726] cursor-pointer w-full ${isMobile ? '' : 'py-4 border-t border-gray-200'}`}>
              <Popover className="relative w-full h-8">
                {({ close }) => (
                  <>
                    <div ref={(node) => {
                      closePopoverRef.current = close;
                    }}>
                      <Popover.Button className="relative w-full focus:outline-none">
                        <div
                          className={`flex items-center w-full ${isCollapsed ? 'justify-center' : 'justify-between'}`}
                        >
                          {
                            !isCollapsed ?
                              <>
                                <div className="flex flex-1 items-center">
                                  <UserIcon className="h-8 w-8 shrink-0" />
                                  <span
                                    className="truncate max-w-[148px] flex-1 ml-1"
                                  >
                                    {user?.username}
                                  </span>
                                </div>
                                <img
                                  className="h-4 w-4"
                                  src={chevron_right}
                                />
                              </> : <UserIcon className="h-8 w-8 shrink-0" />
                          }
                        </div>
                      </Popover.Button>
                      <Popover.Panel
                        className={`min-w-[204px] bg-base-100 w-auto p-[10px] overflow-hidden rounded-xl focus:outline-none z-[60] ${isMobile ? 'absolute right-0' : isCollapsed ? 'fixed bottom-[4rem] left-[1rem]' : 'fixed bottom-[7rem] left-[1rem]'}`}
                        style={{ boxShadow: '0px 4px 16px 0px #00000014' }}
                      >
                        <div
                          className="flex items-center py-1 w-full justify-start"
                        >
                          <UserIcon className="h-8 w-8 shrink-0" />
                          <span
                            className="truncate w-auto max-w-[150px] ml-1"
                          >
                            {user?.username}
                          </span>
                        </div>
                        <div className="w-full bg-[#********] h-[1px] my-1" />
                        {
                          isMobile && (
                            <>
                              <Link
                                className="group relative flex cursor-pointer items-center py-2 p-[10px] text-gray-1 hover:text-black-1"
                                to="https://discord.gg/Fg3gWKX8nS"
                                target="_blank"
                              >
                                <div className="w-5 h-5 mr-[10px]"> 
                                  <FeedbackIcon className="block group-hover:hidden w-full h-full" />
                                  <FeedbackDarkIcon className="hidden group-hover:block w-full h-full" /> 
                                </div>
                                <p>{i18n.FEEDBACK[lang]}</p>
                              </Link>
                              <div className="w-full bg-[#********] h-[1px] my-1" />
                            </>
                          )
                        }
                        <Link
                          className="group relative flex cursor-pointer items-center py-2 p-[10px] text-gray-1 hover:text-black-1"
                          to="/settings/account"
                          onClick={() => {
                            closePopoverRef.current?.();
                          }}
                        >
                          <div className="w-5 h-5 mr-[10px]"> 
                            <SetIcon className="block group-hover:hidden w-full h-full" />
                            <SetDarkIcon className="hidden group-hover:block w-full h-full" /> 
                          </div>
                          <p>{i18n.SETTINGS[lang]}</p>
                        </Link>
                        <div className="w-full bg-[#********] h-[1px] my-1" />
                        <Link
                          className="group relative flex items-center py-2 p-[10px] text-gray-1 hover:text-black-1"
                          to="#"
                          onClick={() => (window.location.href = '/logout')}
                        >
                          <div className="w-5 h-5 mr-[10px]"> 
                            <LogoutIcon className="block group-hover:hidden w-full h-full" />
                            <LogoutDarkIcon className="hidden group-hover:block w-full h-full" /> 
                          </div>
                          <p>{i18n.LOGOUT[lang]}</p>
                        </Link>
                      </Popover.Panel>
                    </div>
                  </>
                )}
              </Popover>
            </div>
          )}
        </>
        {
          (!isCollapsed && !isMobile) && (
            <Link
              className="flex items-center justify-center px-3 py-2 text-xs font-medium
                  bg-gray-3 rounded-lg hover:bg-noah-theme
                  text-gray-1 hover:text-white w-full mb-4"
              to="https://discord.gg/Fg3gWKX8nS"
              target="_blank"
            >
              <DiscordLogoIcon className="w-4 h-4 mr-2" />
              <span>{i18n.JOIN_DISCORD[lang]}</span>
            </Link>
          )
        }
      </div>
    )
  }

  const CreditBar = (isCollapsed) => {
    return null;
    // const percentage = currentTotalCredits > 0 ? (currentUsedCredits / currentTotalCredits) * 100 : 0;
    // const remainingCredits = currentTotalCredits - currentUsedCredits;
    
    // if (isCollapsed) {
    //   return (
    //     <div className="group/tooltip relative shrink-0 px-2 py-3">
    //       <div className="flex flex-col items-center">
    //         <div className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center mb-2">
    //           <span className="text-xs font-medium text-gray-600">
    //             {Math.round(percentage)}%
    //           </span>
    //         </div>
    //         <div className="w-full bg-gray-200 rounded-full h-1">
    //           <div 
    //             className="bg-noah-theme h-1 rounded-full transition-all duration-300"
    //             style={{ width: `${Math.min(percentage, 100)}%` }}
    //           />
    //         </div>
    //       </div>
    //       <Tooltip
    //         text={`${remainingCredits} credits remaining`}
    //       />
    //     </div>
    //   );
    // }

    // return (
    //   <div className="shrink-0 px-3 py-3">
    //     <div className="flex items-center justify-between mb-2">
    //       <span className="text-xs font-medium text-gray-600">AI Credits</span>
    //       <span className="text-xs text-gray-500">
    //         {currentUsedCredits} / {currentTotalCredits}
    //       </span>
    //     </div>
    //     <div className="w-full bg-gray-200 rounded-full h-2">
    //       <div 
    //         className="bg-noah-theme h-2 rounded-full transition-all duration-300"
    //         style={{ width: `${Math.min(percentage, 100)}%` }}
    //       />
    //     </div>
    //     <div className="mt-1 text-xs text-gray-500">
    //       {remainingCredits} credits remaining
    //     </div>
    //   </div>
    // );
  };

  console.log('rendering ask container');
  return (
    <>
      {/*
        This example requires updating your template:

        ```
        <html class="h-full bg-gray-50">
        <body class="h-full">
        ```
      */}
      <div className="font-sans">
        {
          renderSidebar && (
            <>
              {/* settings sidebar */}
              <SettingsBar sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} showSettingsSidebar={isSettingsPath} />
              {/* Mobile left nav bar */}
              {(!isSharePage && !isSettingsPath) ? (
                <Transition.Root show={sidebarOpen} as={Fragment}>
                  <Dialog
                    as="div"
                    className="relative z-50 lg:hidden"
                    onClose={setSidebarOpen}
                  >
                    <Transition.Child
                      as={Fragment}
                      enter="transition-opacity ease-linear duration-300"
                      enterFrom="opacity-0"
                      enterTo="opacity-100"
                      leave="transition-opacity ease-linear duration-300"
                      leaveFrom="opacity-100"
                      leaveTo="opacity-0"
                    >
                      <div className="fixed inset-0 bg-gray-900/80" />
                    </Transition.Child>

                    <div className="fixed inset-0 flex">
                      <Transition.Child
                        as={Fragment}
                        enter="transition ease-in-out duration-300 transform"
                        enterFrom="-translate-x-full"
                        enterTo="translate-x-0"
                        leave="transition ease-in-out duration-300 transform"
                        leaveFrom="-translate-x-0"
                        leaveTo="-translate-x-full"
                      >
                        <Dialog.Panel className="relative mr-16 flex w-full max-w-xs flex-1">
                          <Transition.Child
                            as={Fragment}
                            enter="ease-in-out duration-300"
                            enterFrom="opacity-0"
                            enterTo="opacity-100"
                            leave="ease-in-out duration-300"
                            leaveFrom="opacity-100"
                            leaveTo="opacity-0"
                          >
                            <div className="absolute left-full top-0 flex w-16 justify-center pt-5">
                              <button
                                type="button"
                                className="-m-2.5 p-2.5"
                                onClick={() => setSidebarOpen(false)}
                              >
                                <span className="sr-only">Close sidebar</span>
                                <XMarkIcon
                                  className="h-6 w-6 text-primary-content"
                                  aria-hidden="true"
                                />
                              </button>
                            </div>
                          </Transition.Child>
                          {/* Sidebar component, swap this element with another sidebar if you like */}
                          <div className="flex grow flex-col gap-y-2 overflow-y-auto bg-base-100 px-6 pb-2 scrollbar-none">
                            <nav className="flex-1 overflow-y-auto scrollbar-none transition-all duration-200 mt-5">
                              <ul role="list" 
                                className="flex flex-col gap-y-2">
                                {navigation.map((item, index) => (
                                  <li key={item.name} className='relative'>
                                    {
                                      item.children?.length ? (
                                        <div>
                                          <div
                                            className={classNames(
                                              item.current
                                                ? 'bg-noah-theme text-white'
                                                : 'text-gray-1 hover:bg-gray-3',
                                              'group cursor-pointer select-none flex items-center justify-between rounded-xl font-medium p-[10px] text-sm leading-6 overflow-hidden relative',
                                            )}
                                            onClick={() => {
                                              setExpandedNavBarList((prev) =>
                                                prev.includes(item.name)
                                                  ? prev.filter((name) => name !== item.name)
                                                  : [ ...prev, item.name ]
                                              );
                                            }}
                                          >
                                            <div className="flex items-center">
                                              <div className="relative">
                                                {
                                                  item.current ? <item.selectIcon className="h-5 w-5 shrink-0 mr-3" /> : <item.icon className="h-5 w-5 shrink-0 mr-3" />
                                                }
                                              </div>
                                              <span
                                                className={classNames(
                                                  item.current ? 'text-white' : '',
                                                  'overflow-hidden whitespace-nowrap select-none'
                                                )}
                                              >
                                                {item.name}
                                              </span>
                                            </div>
                                            {
                                              item.current ? <ChevronDownSelect className={classNames(
                                                expandedNavBarList.includes(item.name) ? 'rotate-180' : '',
                                                'h-3 w-3 cursor-pointer transition-transform'
                                              )} /> : <ChevronDown className={classNames(
                                                expandedNavBarList.includes(item.name) ? 'rotate-180' : '',
                                                'h-3 w-3 cursor-pointer transition-transform'
                                              )} />
                                            }
                                          </div>
                                          {
                                            expandedNavBarList.includes(item.name) && item.children.map((subItem) => (
                                              <Link
                                                key={subItem.name}
                                                to={subItem.href}
                                                onClick={() => navigationClick(subItem.href)}
                                                className={classNames(
                                                  subItem.current
                                                    ? 'text-noah-theme'
                                                    : 'text-gray-1 hover:text-[#222726CC]',
                                                  'group cursor-pointer flex items-center justify-between font-medium p-[10px] pl-11 text-sm leading-6 overflow-hidden relative',
                                                )}
                                              >
                                                <div className="flex items-center">
                                                  <span
                                                    className={classNames(
                                                      subItem.current ? 'text-noah-theme' : '',
                                                      'overflow-hidden whitespace-nowrap select-none'
                                                    )}
                                                  >
                                                    {subItem.name}
                                                  </span>
                                                </div>
                                              </Link>
                                            ))
                                          }
                                        </div>
                                      ) : (
                                        <Link
                                          to={item.href}
                                          onClick={() => navigationClick(item.href)}
                                          className={classNames(
                                            item.current
                                              ? 'bg-noah-theme text-white'
                                              : 'text-gray-1 hover:bg-gray-3',
                                            'group flex items-center justify-between rounded-xl font-medium p-[10px] text-sm leading-6 overflow-hidden relative',
                                          )}
                                        >
                                          <div className="flex items-center">
                                            <div className="relative">
                                              {
                                                item.current ? <item.selectIcon className="h-5 w-5 shrink-0 mr-3" /> : <item.icon className="h-5 w-5 shrink-0 mr-3" />
                                              }
                                            </div>
                                            <span
                                              className={classNames(
                                                item.current ? 'text-white' : '',
                                                'overflow-hidden whitespace-nowrap select-none'
                                              )}
                                            >
                                              {item.name}
                                            </span>
                                          </div>
                                        </Link>
                                      )
                                    }
                                  </li>
                                ))}  
                              </ul>
                            </nav>
                            {/* Credit bar */}
                            {CreditBar(false)}
                          </div>
                        </Dialog.Panel>
                      </Transition.Child>
                    </div>
                  </Dialog>
                </Transition.Root>
              ) : null}

              {/* Static sidebar for desktop */}
              {!isSharePage && (
                <div
                  className={`hidden w-[14.75rem] lg:flex lg:fixed lg:inset-y-0 lg:z-50 lg:flex-col transition-all duration-300 ease-in-out ${(isSidebarCollapsed || isSettingsPath) ? 'opacity-0 -translate-x-full' : 'opacity-100 translate-x-0'}`}
                >
                  {/* Sidebar component */}
                  <div className="group/sidebar flex h-screen flex-col gap-y-2 bg-white px-5 scrollbar-none">
                    {/* Header with logo */}
                    <div className="flex h-6 my-8 shrink-0 items-center justify-center">
                      <div className="flex items-center w-full justify-between">
                        <Link to='/hitl/'>
                          <img
                            className="h-6 w-auto object-cover"
                            src={theme == 'light' ? noah_logo_black : noah_logo_black}
                            alt="Your Company"
                          />
                        </Link>
                        <div className="relative group/tooltip h-6 w-6 cursor-pointer flex items-center justify-center rounded-lg hover:bg-gray-3">
                          <img
                            onClick={() => setIsSidebarCollapsed(!isSidebarCollapsed)}
                            className="h-5 w-5 cursor-pointer"
                            src={toggle_icon}
                          />
                          <Tooltip
                            tipClassName="left-1/2 top-6 -translate-x-1/2"
                            text={i18n.CLOSE[lang]}
                          />
                        </div>
                      </div>
                    </div>

                    {/* Navigation */}
                    <nav className={classNames(
                      'flex-1 overflow-y-auto scrollbar-none transition-all duration-200',
                      isNavHovered ? 'pr-96 -mr-96' : ''
                    )}>
                      <ul role="list" 
                        className="flex flex-col gap-y-2">
                        {navigation.map((item, index) => (
                          <li key={item.name} className='relative'>
                            {
                              item.children?.length ? (
                                <div>
                                  <div
                                    className={classNames(
                                      item.current
                                        ? 'bg-noah-theme text-white'
                                        : 'text-gray-1 hover:bg-gray-3',
                                      'group cursor-pointer flex items-center justify-between rounded-xl font-medium p-[10px] text-sm leading-6 overflow-hidden relative',
                                    )}
                                    onClick={() => {
                                      setExpandedNavBarList((prev) =>
                                        prev.includes(item.name)
                                          ? prev.filter((name) => name !== item.name)
                                          : [ ...prev, item.name ]
                                      );
                                    }}
                                  >
                                    <div className="flex items-center">
                                      <div className="relative">
                                        {
                                          item.current ? <item.selectIcon className="h-5 w-5 shrink-0 mr-3" /> : <item.icon className="h-5 w-5 shrink-0 mr-3" />
                                        }
                                      </div>
                                      <span
                                        className={classNames(
                                          item.current ? 'text-white' : '',
                                          'overflow-hidden whitespace-nowrap select-none'
                                        )}
                                      >
                                        {item.name}
                                      </span>
                                    </div>
                                    {
                                      item.current ? <ChevronDownSelect className={classNames(
                                        expandedNavBarList.includes(item.name) ? 'rotate-180' : '',
                                        'h-3 w-3 cursor-pointer transition-transform'
                                      )} /> : <ChevronDown className={classNames(
                                        expandedNavBarList.includes(item.name) ? 'rotate-180' : '',
                                        'h-3 w-3 cursor-pointer transition-transform'
                                      )} />
                                    }
                                  </div>
                                  {
                                    expandedNavBarList.includes(item.name) && item.children.map((subItem) => (
                                      <Link
                                        key={subItem.name}
                                        to={subItem.href}
                                        onClick={() => navigationClick(subItem.href)}
                                        className={classNames(
                                          subItem.current
                                            ? 'text-noah-theme'
                                            : 'text-gray-1 hover:text-[#222726CC]',
                                          'group cursor-pointer flex items-center justify-between font-medium p-[10px] pl-11 text-sm leading-6 overflow-hidden relative',
                                        )}
                                      >
                                        <div className="flex items-center">
                                          <span
                                            className={classNames(
                                              subItem.current ? 'text-noah-theme' : '',
                                              'overflow-hidden whitespace-nowrap select-none'
                                            )}
                                          >
                                            {subItem.name}
                                          </span>
                                        </div>
                                      </Link>
                                    ))
                                  }
                                </div>
                              ) : (
                                <Link
                                  to={item.href}
                                  onClick={() => navigationClick(item.href)}
                                  className={classNames(
                                    item.current
                                      ? 'bg-noah-theme text-white'
                                      : 'text-gray-1 hover:bg-gray-3',
                                    'group flex items-center justify-between rounded-xl font-medium p-[10px] text-sm leading-6 overflow-hidden relative',
                                  )}
                                >
                                  <div className="flex items-center">
                                    <div className="relative">
                                      {
                                        item.current ? <item.selectIcon className="h-5 w-5 shrink-0 mr-3" /> : <item.icon className="h-5 w-5 shrink-0 mr-3" />
                                      }
                                    </div>
                                    <span
                                      className={classNames(
                                        item.current ? 'text-white' : '',
                                        'overflow-hidden whitespace-nowrap select-none'
                                      )}
                                    >
                                      {item.name}
                                    </span>
                                  </div>
                                </Link>
                              )
                            }
                          </li>
                        ))}  
                      </ul>
                    </nav>
                    {/* Credit bar */}
                    {CreditBar(false)}
                    {/* User section at bottom */}
                    {UserSection(false, false)}
                  </div>
                </div>
              )}
              {/* Collapsed static sidebar for desktop */}
              {!isSharePage && (
                <div
                  className={`hidden w-[5rem] lg:flex lg:fixed lg:inset-y-0 lg:z-50 lg:flex-col ${
                    (isSidebarCollapsed && !isSettingsPath)
                      ? 'opacity-100 translate-x-0 transition-opacity duration-300 ease-in-out delay-300' 
                      : 'opacity-0 -translate-x-full transition-none'
                  }`}
                >
                  {/* Sidebar component */}
                  <div className="group/sidebar flex h-screen flex-col gap-y-2 bg-white px-5 scrollbar-none">
                    <div className="flex h-6 my-8 shrink-0 items-center justify-center">
                      <div className="relative group/tooltip h-6 w-6 cursor-pointer flex items-center justify-center rounded-lg hover:bg-gray-3">
                        <img
                          onClick={() => setIsSidebarCollapsed(!isSidebarCollapsed)}
                          className="h-5 w-5 cursor-pointer"
                          src={toggle_icon}
                        />
                        <Tooltip
                          text={i18n.OPEN[lang]}
                        />
                      </div>
                    </div>

                    {/* Navigation */}
                    <nav className={classNames(
                      'flex-1 overflow-y-auto scrollbar-none transition-all duration-200',
                      isNavHovered ? 'pr-96 -mr-96' : ''
                    )}>
                      <ul role="list" 
                        className="flex flex-col gap-y-2"
                        onMouseEnter={() => isSidebarCollapsed && setIsNavHovered(true)}
                        onMouseLeave={() => isSidebarCollapsed && setIsNavHovered(false)}>
                        {navigation.map((item, index) => (
                          <li key={item.name} className='group/tooltip relative'>
                            {
                              item.children?.length ? (
                                <div
                                  className={`group cursor-pointer flex items-center justify-between rounded-xl font-medium p-[10px] text-sm leading-6 overflow-hidden relative ${navSelectOpen === item.name && !item.current ? 'bg-gray-3' : ''} ${item.current
                                    ? 'bg-noah-theme'
                                    : 'hover:bg-gray-3'}`}
                                  onClick={() => setNavSelectOpen(item.name)}
                                >
                                  {
                                    item.current ? <item.selectIcon className="h-5 w-5 shrink-0" /> : <item.icon className="h-5 w-5 shrink-0" />
                                  }
                                  <TransparentSelect
                                    open={navSelectOpen === item.name}
                                    className="absolute cursor-pointer inset-0 opacity-100 -translate-y-[36px]"
                                    dropdownStyle={{ 
                                      width: 'fit-content',
                                      left: '4.2rem'
                                    }}
                                    optionLabelProp="label"
                                    onChange={(e) => {
                                      setNavSelectOpen('');
                                      navigationClick(e);
                                      navigate(e);
                                    }}
                                    onDropdownVisibleChange={(visible) => setNavSelectOpen(visible ? item.name : '')}
                                    items={item.children.map(subItem => {
                                      return { 
                                        value: subItem.href, 
                                        label: subItem.name, 
                                        current: subItem.current 
                                      }
                                    })}
                                  />
                                </div>
                              ) : (
                                <Link
                                  to={item.href}
                                  onClick={() => navigationClick(item.href)}
                                  className={classNames(
                                    item.current
                                      ? 'bg-noah-theme text-white'
                                      : 'text-gray-1 hover:bg-gray-3',
                                    'group flex items-center justify-between rounded-xl font-medium p-[10px] text-sm leading-6 overflow-hidden relative',
                                  )}
                                >
                                  <div className="flex items-center">
                                    <div className="relative">
                                      {
                                        item.current ? <item.selectIcon className="h-5 w-5 shrink-0" /> : <item.icon className="h-5 w-5 shrink-0" />
                                      }
                                    </div>
                                  </div>
                                </Link>
                              )
                            }
                            {/* navigation tooltip */}
                            <Tooltip 
                              show={isSidebarCollapsed}
                              text={item.name}
                            />
                          </li>
                        ))}  
                      </ul>
                    </nav>
                    {/* Credit bar */}
                    {CreditBar(true)}
                    {/* User section at bottom */}
                    {UserSection(true, false)}
                  </div>
                </div>
              )}
            </>
          )
        }

        {/* Mobile top nav bar */}
        {!isSharePage && (
          <div className="sticky top-0 z-40 flex items-center gap-x-6 bg-base-100 px-4 py-4 shadow-sm sm:px-6 lg:hidden">
            <button
              type="button"
              className="-m-2.5 p-2.5 text-secondary-content lg:hidden"
              onClick={() => setSidebarOpen(true)}
            >
              <span className="sr-only">Open sidebar</span>
              <Bars3Icon className="h-6 w-6" aria-hidden="true" />
            </button>
            <div className="flex-1 text-sm font-semibold leading-6 text-secondary-content">
              Dashboard
            </div>
            <div>
              {UserSection(false, true)}
            </div>
          </div>
        )}

        <main ref={mainRef} className={`transition-all duration-300 ${!isSharePage ? ((isSidebarCollapsed && !isSettingsPath) ? 'lg:pl-[5rem]' : 'lg:pl-[14.75rem]') : ''}`}>
          <Outlet />
          {children}
        </main>
      </div>
    </>
  );
}

export async function mainContainerLoader() {
  // const a = await getUserList();
  // TODO MILD: follow https://github.com/remix-run/react-router/discussions/9856
  // currently in loader, we cannot use context or hooks

  //  get user info from localstorage
  console.log('in ask container loader');
  const userName = Cookies.get('userName') || Cookies.get('noahUser'); //noahUser is the same as email. as required by Backend.
  if (userName) {
    console.log('有username!', userName);
    // const data = await getHistoryList();
    // console.log('jbjbjbjb', data)
    // return data||{};
    return userName;
  } else {
    console.log('no username, need login');
    // console.log('登录啊')
    location.replace('/signin');
    return null;
  }

}