export default function CustomButton({ btnClassName, onBtnClick, children, icon, selectedIcon, text, isSelected, disabled = false, shape = 'default' }) {  
  return (
    <button
      className={`group btn btn-sm rounded-full gap-1 border-none flex items-center
        ${isSelected ? 'bg-aqua hover:bg-aqua' : 'bg-[#F7F7F7] hover:bg-[#F7F7F7]'}
        ${shape === 'circle' ? 'btn-circle' : ''}
        ${btnClassName}`}
      onClick={() => onBtnClick && onBtnClick()}
      disabled={disabled}
    >
      {children ? children : (
        <>
          {
            isSelected 
              ? (selectedIcon 
                ? selectedIcon
                : (icon ? icon : null)
              )
              : (icon ? icon : null)
          }
          {
            text && <span className={`flex items-center justify-center flex-1 font-medium text-sm ${isSelected ? 'text-noah-theme' : 'text-black-1'}`}>{text}</span>
          }
        </>
      )}
    </button>
  );
}
