import { useEffect, useRef, useState } from 'react';

import { Select } from 'antd';

import { ReactComponent as ChevronDown } from '../assets/chevron-down.svg';

import Tooltip from './Tooltip';

import '../styles/select.css';

export default function TransparentSelect({ children, needBlur, ...props }) {
  const selectRef = useRef(null);

  const tagRender = (props) => {
    return (
      <span
        {...props}
        style={{
          background: '#f6ffed',
          borderColor: '#b7eb8f',
          color: '#52c41a',
        }}
      >
        {props.label}
      </span>
    );
  };

  return (
    <Select
      {...props}
      ref={selectRef}
      popupClassName="transparent-select-dropdown"
      listHeight={null}
      className={`transparent-select overflow-visible [&_.ant-select-selector]:!bg-transparent [&_.ant-select-selector]:!rounded-full [&_.ant-select-selector]:!h-8 [&_.ant-select-selector]:!border-none [&_.ant-select-selection-item]:!leading-8 ${props.className || ''}`}
      suffixIcon={<ChevronDown />}
      dropdownRender={(menu) => (
        <div className="transparent-select">
          {props.dropdownRender ? props.dropdownRender(menu) : menu}
        </div>
      )}
      onSelect={() => {
        // 手动触发blur来移除焦点状态
        needBlur && selectRef.current?.blur();
      }}
      onDropdownVisibleChange={(visible) => {
        !visible && setTimeout(() => {
          selectRef.current?.blur();
        }
        , 100);
        props.onDropdownVisibleChange && props.onDropdownVisibleChange(visible);
      }}
      options={props.items?.length ? props.items?.map((subItem, index) => ({
        value: subItem.value,
        title: subItem.label,
        label: (
          <div className={`relative group/tooltip group flex items-center space-x-1 w-full p-[10px] font-medium ${index === props.items.length - 1 ? '' : 'border-b border-[#22272614]'}
            ${subItem.current ? 'text-black-1 hover:text-black-1' : 'text-gray-1 hover:text-black-1'}`}
          >
            {subItem.icon && <subItem.icon className={`${subItem.current ? 'opacity-100' : 'opacity-60'} group-hover:opacity-100`} />}
            <span className="whitespace-nowrap">
              {subItem.label}
            </span>
            {subItem.tip && (
              <Tooltip
                tipClassName="left-1/2 -top-1 -translate-y-full -translate-x-1/2"
                text={subItem.tip}
                width="300px"
              />
            )}
          </div>
        ),
      })) : (props.options || [])}
    >
      {children}
    </Select>
  );
}
