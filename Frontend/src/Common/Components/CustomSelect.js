import { useEffect, useRef, useState } from 'react';

import { Select } from 'antd';

import { ReactComponent as ChevronDown } from '../../assets/arrow-down.svg';

import '../styles/select.css';

export default function CustomSelect({ children, ...props }) {
  return (
    <Select
      {...props}
      className={`custom-select [&_.ant-select-selector]:!px-3 [&_.ant-select-selector]:!py-[5px] ${props.className || ''}`}
      size="large"
      suffixIcon={
        <ChevronDown className="h-4 w-4" />
      }
    >
      {children}
    </Select>
  );
}
