import React, { createContext, useContext, useEffect, useState } from 'react';


import { CheckCircleIcon } from '@heroicons/react/20/solid';
import { LanguageIcon } from '@heroicons/react/24/outline';
import { hide } from '@popperjs/core';
import { Button, Checkbox, Form, Input, message, Modal, Select, Tabs } from 'antd';
import { Link } from 'react-router-dom';

import { useLanguage } from '../../utils/context/lang';
import { usePro } from '../../utils/context/pro';

const langMap = {
  en: 'EN',
  zh: 'CN',
  jp: 'JP',
};

const i18n = {
  TITLE: {
    en: 'Settings',
    zh: '设置'
  },
  GENERAL: {
    en: 'General',
    zh: '通用设置'
  },
  OTNER: {
    en: 'Other',
    zh: '其他'
  },
  FEEDBACK: {
    en: 'Feedback',
    zh: '反馈',
  }
};

export default function SettingsModal({ hideModal }) {
  const [ languageSelectOpen, setLanguageSelectOpen ] = useState(false);
  const { lang, toggleLanguage, selectLanguage } = useLanguage();
  const { switchHistoryMenu, historyMenuOpen, switchAnswerLang } = usePro();
  
  return (
    <>
      <Modal
        open={true}
        onCancel={hideModal}
        footer={null}
        title={i18n.TITLE[lang]}
        width={525}
        style={{ 
          top: '30%',
        }}
      >
        <Tabs
          defaultActiveKey={'general'}
          centered
          items={[
            {
              key: 'general',
              label: i18n.GENERAL[lang],
              children: (
                <div
                  className={`flex items-center justify-between text-sm leading-6 font-semibold overflow-hidden relative text-black-1 ${languageSelectOpen ? 'text-secondary-content' : ''}`}
                >
                  <div
                    className="flex items-center py-2 p-[10px] "
                  >
                    <div className="relative">
                      <LanguageIcon
                        className='h-5 w-5 shrink-0 mr-3'
                        aria-hidden="true"
                      />
                    </div>
                    <span
                      className='overflow-hidden whitespace-nowrap'
                    >
                      {lang === 'en' ? 'English' : '中文'}
                    </span>
                  </div>
                  <Select
                    className="cursor-pointer w-24 h-full mr-0.5"
                    defaultValue={lang}
                    // dropdownStyle={{ width: '6rem', left: '5rem' }}
                    onChange={(e) => {
                      switchAnswerLang(langMap[e]); // switch explore answer as well.
                      selectLanguage(e);
                    }}
                    onDropdownVisibleChange={(visible) => setLanguageSelectOpen(visible)}
                    options={[
                      { value: 'en', label: 'English' },
                      { value: 'zh', label: '中文' },
                    ]}
                  />
                </div>
              ),
            },
            {
              key: 'other',
              label: i18n.OTNER[lang],
              children: (
                <div>
                  <Link
                    className="text-sm leading-6 font-semibold py-2 p-[10px] block text-secondary-content"
                    to="/contact"
                    target="_blank"
                  >
                    <p>{i18n.FEEDBACK[lang]}</p>
                  </Link>
                </div>
              ),
            }
          ]}
        />
      </Modal>
    </>
  )
}