export default function Tooltip({ show = true, text, width, tipClassName = 'left-12 top-1/2 -translate-y-1/2' }) {
  if (!show) return null;
  
  return (
    <div
      className={`absolute pointer-events-none w-max z-[1050] ${tipClassName}
                [transition:opacity_200ms_ease-out,transform_200ms_ease-out_200ms]
                group-hover/tooltip:[transition:opacity_200ms_ease-out,transform_0ms_ease-out]
                opacity-0 scale-0 origin-left
                group-hover/tooltip:opacity-100 group-hover/tooltip:scale-100`}
    >
      <div 
        className="bg-black/80 backdrop-blur-sm px-[10px] py-1 
                    rounded-[10px] text-sm font-medium whitespace-normal break-words
                    flex items-center gap-2 text-white"
        style={width ? { width } : undefined}
      >
        {text}
      </div>
    </div>
  );
}
