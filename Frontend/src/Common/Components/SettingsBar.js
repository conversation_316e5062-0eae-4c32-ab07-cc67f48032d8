import { Children, Fragment, useEffect, useRef, useState } from 'react';

import {
  Dialog,
  Transition,
} from '@headlessui/react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import Cookies from 'js-cookie';
import { Link, useLocation } from 'react-router-dom';

import { useLanguage } from '../../utils/context/lang';
import { ReactComponent as Account } from '../assets/account.svg';
import { ReactComponent as ArrowLeft } from '../assets/arrow-left.svg';
import { ReactComponent as Billing } from '../assets/billing.svg';
import { ReactComponent as LanguageIcon } from '../assets/language.svg';
import { ReactComponent as Subscription } from '../assets/subscription.svg';
// import { useHistory } from '../utils/context/history';
// import { useLayout } from '../utils/context/layout';
// import { useLayoutWidth } from '../utils/context/layout-width';
// import { useWebSocketContext } from '../utils/context/main-websocket';
// import { usePro } from '../utils/context/pro';
// import { useTheme } from '../utils/context/theme-system';
import { i18n } from '../i18n/menu';


function classNames(...classes) {
  return classes.filter(Boolean).join(' ');
}

export default function SettingsBar({
  sidebarOpen,
  setSidebarOpen,
  showSettingsSidebar = false,
}) {
  const { lang } = useLanguage();
  const { pathname } = useLocation();

  const [ navigation, setNavigation ] = useState([
    {
      name: i18n.ACCOUNT[lang],
      href: '/settings/account',
      icon: Account,
      current: false,
    },
    {
      name: i18n.SUBSCRIPTION[lang],
      href: '/settings/subscription',
      icon: Subscription,
      current: false,
    },
    // {
    //   name: i18n.BILLING[lang],
    //   href: '/settings/billing',
    //   icon: Billing,
    //   current: false,
    // },
    {
      name: i18n.LANGUAGE[lang],
      href: '/settings/language',
      icon: LanguageIcon,
      current: false,
    },
  ]);

  const navigationClick = (href) => {
    setNavigation((prev) => {
      return prev.map((item) => {
        item.current = item.href === href;
        return item;
      });
    });
  }

  useEffect(() => {
    if (pathname) {
      setNavigation((prev) => {
        return prev.map((item) => {
          let href = item.href.split('?')[0];
          href.endsWith('/') ? href = href.slice(0, -1) : href;
          item.current = pathname.startsWith(href);
          return item;
        });
      });

    }
  }, [ pathname ]);

  return (
    <>
      {/* Static settings sidebar for mobile */}
      <Transition.Root show={sidebarOpen} as={Fragment}>
        <Dialog
          as="div"
          className="relative z-50 lg:hidden"
          onClose={setSidebarOpen}
        >
          <Transition.Child
            as={Fragment}
            enter="transition-opacity ease-linear duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="transition-opacity ease-linear duration-300"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-gray-900/80" />
          </Transition.Child>

          <div className="fixed inset-0 flex">
            <Transition.Child
              as={Fragment}
              enter="transition ease-in-out duration-300 transform"
              enterFrom="-translate-x-full"
              enterTo="translate-x-0"
              leave="transition ease-in-out duration-300 transform"
              leaveFrom="-translate-x-0"
              leaveTo="-translate-x-full"
            >
              <Dialog.Panel className="relative mr-16 flex w-full max-w-xs flex-1">
                <Transition.Child
                  as={Fragment}
                  enter="ease-in-out duration-300"
                  enterFrom="opacity-0"
                  enterTo="opacity-100"
                  leave="ease-in-out duration-300"
                  leaveFrom="opacity-100"
                  leaveTo="opacity-0"
                >
                  <div className="absolute left-full top-0 flex w-16 justify-center pt-5">
                    <button
                      type="button"
                      className="-m-2.5 p-2.5"
                      onClick={() => setSidebarOpen(false)}
                    >
                      <span className="sr-only">Close sidebar</span>
                      <XMarkIcon
                        className="h-6 w-6 text-primary-content"
                        aria-hidden="true"
                      />
                    </button>
                  </div>
                </Transition.Child>
                {/* Sidebar component, swap this element with another sidebar if you like */}
                <div className="flex grow flex-col gap-y-2 overflow-y-auto bg-base-100 px-6 pb-2 scrollbar-none">
                  <Link
                    className="flex my-8 shrink-0 items-center space-x-2"
                    to="/hitl"
                  >
                    <ArrowLeft className="w-4 h-4" />
                    <div className="font-medium text-gray-1 text-sm leading-6">{i18n.BACK[lang]}</div>
                  </Link>
                  <nav className="flex-1 overflow-y-auto scrollbar-none transition-all duration-200 mt-5">
                    <ul role="list"
                      className="flex flex-col gap-y-2">
                      {navigation.map((item, index) => (
                        <li key={item.name} className='relative'>
                          <Link
                            to={item.href}
                            onClick={() => navigationClick(item.href)}
                            className={classNames(
                              item.current
                                ? 'bg-gray-3 text-black-1'
                                : 'text-gray-1 hover:bg-gray-3 hover:text-black-1',
                              'group flex items-center justify-between rounded-xl font-medium p-[10px] text-sm leading-6 overflow-hidden relative',
                            )}
                          >
                            <div className="flex items-center">
                              <div className="relative">
                                <item.icon className={`h-5 w-5 shrink-0 mr-3 ${item.current ? 'opacity-100' : 'opacity-60 group-hover:opacity-100'}`} />
                              </div>
                              <span
                                className="overflow-hidden whitespace-nowrap select-none"
                              >
                                {item.name}
                              </span>
                            </div>
                          </Link>
                        </li>
                      ))}
                    </ul>
                  </nav>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </Dialog>
      </Transition.Root>

      {/* Static settings sidebar for desktop */}
      <div
        className={`hidden w-[14.75rem] lg:flex lg:fixed lg:inset-y-0 lg:z-50 lg:flex-col transition-all duration-300 ease-in-out ${showSettingsSidebar ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-full'}`}
      >
        {/* Sidebar component */}
        <div className="group/sidebar flex h-screen flex-col gap-y-2 bg-white px-5 scrollbar-none">
          <Link
            className="group flex my-8 shrink-0 items-center space-x-2 w-fit"
            to="/hitl"
          >
            <ArrowLeft className="w-4 h-4 opacity-60 group-hover:opacity-100" />
            <div className="group-hover:text-black-1 font-medium text-gray-1 text-sm leading-6">{i18n.BACK[lang]}</div>
          </Link>

          {/* Navigation */}
          <nav className="flex-1 overflow-y-auto scrollbar-none transition-all duration-200">
            <ul role="list"
              className="flex flex-col gap-y-2">
              {navigation.map((item, index) => (
                <li key={item.name} className='relative'>
                  <Link
                    to={item.href}
                    onClick={() => navigationClick(item.href)}
                    className={classNames(
                      item.current
                        ? 'bg-gray-3 text-black-1'
                        : 'text-gray-1 hover:bg-gray-3 hover:text-black-1',
                      'group flex items-center justify-between rounded-xl font-medium p-[10px] text-sm leading-6 overflow-hidden relative',
                    )}
                  >
                    <div className="flex items-center">
                      <div className="relative">
                        <item.icon className={`h-5 w-5 shrink-0 mr-3 ${item.current ? 'opacity-100' : 'opacity-60 group-hover:opacity-100'}`} />
                      </div>
                      <span
                        className="overflow-hidden whitespace-nowrap select-none"
                      >
                        {item.name}
                      </span>
                    </div>
                  </Link>
                </li>
              ))}
            </ul>
          </nav>
        </div>
      </div>
    </>
  );
}
