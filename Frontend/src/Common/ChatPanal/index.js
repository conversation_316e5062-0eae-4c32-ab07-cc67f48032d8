import React, { useCallback, useEffect, useRef, useState } from 'react';

import { motion } from 'framer-motion';
import { useLocation , useNavigate } from 'react-router-dom';

import { createThread } from '../../api';
import { OneConversation } from '../../AskPage/components/oneConversation';
import { OneRunningConversation } from '../../AskPage/components/oneRunningConversation';
import SingleLineInput from '../../AskPage/components/singleLineInput';
import { ReactComponent as CloseIcon } from '../../assets/close.svg';
import { useAlert } from '../../utils/context/alert';
import { useLanguage } from '../../utils/context/lang';
import { useLayoutWidth } from '../../utils/context/layout-width';
import { useWebSocketContext } from '../../utils/context/main-websocket';
import { usePro } from '../../utils/context/pro';
import { ReactComponent as ArrowLongRight } from '../assets/arrow-long-right.svg';
import { ReactComponent as ChatBlack } from '../assets/chat-black.svg';
import { ReactComponent as PencilSquareIcon } from '../assets/pencil-square.svg';
import Tooltip from '../Components/Tooltip';
import { i18n as chatI18n } from '../i18n/chat';
import { i18n as commonI18n } from '../i18n/common';

import ReferenceView from './ReferenceView';
import selectedTips from './selected-tips.gif';
import SelectedItems from './SelectedItems';

const langMap = {
  en: 'EN',
  zh: 'CN',
  jp: 'JP',
};

const getHintMessage = (assetType, assetId, lang) => {
  const messages = {
    discover: '',
    conference: chatI18n.HINT_CONFERENCE[lang],
    default: '',
    tool: {
      'clinical-result': chatI18n.HINT_CLINICAL_RESULT[lang],
      'drug-compete': chatI18n.HINT_DRUG_COMPETE[lang],
    },
    'clinical-guideline': chatI18n.HINT_CLINICAL_GUIDELINE[lang],
  };
  if (assetType === 'tool') {
    return messages.tool[assetId] || messages.default;
  }
  return messages[assetType] || messages.default;
};

const ChatPanel = ({
  enableUpload = false,
  className = '',
  isOpen = true,
  onToggle,
  initialConversations = [],
  suggestions = [],
  reference = null,
  onReferenceClose = () => {},
  assetId = null,
  assetType = null,
  assetTitle = null,
  threadId,
  treeId,
  onThreadCreated = () => {},
  selectedItems = [],
  onItemRemove,
  itemType,
  baseWidthPercentage = 0,
}) => {
  const { mainWidth } = useLayoutWidth();
  const [ isMobile, setIsMobile ] = useState(false);
  const [ isChatVisible, setIsChatVisible ] = useState(isOpen);
  const [ isReferenceVisible, setIsReferenceVisible ] = useState(false);
  const [ conversations, setConversations ] = useState(initialConversations);
  const [ isFollowupFetching, setIsFollowupFetching ] = useState(false);
  const [ latestQuestion, setLatestQuestion ] = useState(null);
  const [ runningAnswer, setRunningAnswer ] = useState(null);
  const [ currentReference, setCurrentReference ] = useState(reference); // Track current active reference
  const navigate = useNavigate();
  const chatContainerRef = useRef(null);
  const bottomRef = useRef(null);
  const { lang } = useLanguage();
  const showAlert = useAlert();
  const { isSocketOpen, latestMessage, sendMessage, clearLatestMessage } = useWebSocketContext();
  const { isWebSearchOn, hiddenChatPanalTips, switchHiddenChatPanalTips } = usePro();
  const location = useLocation();
  const [ newQuestionTriggered, setNewQuestionTriggered ] = useState(false);
  const autoScrollRef = useRef(true);
  const [ showDoubleCheck, setShowDoubleCheck ] = useState(false);
  // Track internal thread ID to prevent premature refreshes
  const previousThreadId = useRef(threadId);
  const [ widthPercentage, setWidthPercentage ] = useState(baseWidthPercentage);
  const [ isDragging, setIsDragging ] = useState(false);
  const dragRef = useRef(null);
  // Calculate chat panel width using percentage of mainWidth
  const chatWidth = Math.max(mainWidth * widthPercentage, 484);

  // Record drag start position and percentage
  const dragStartRef = useRef({
    startX: 0,
    startPercentage: baseWidthPercentage
  });

  // Handle thread changes
  useEffect(() => {
    // Skip if it's the same thread or it's a new thread
    if (threadId === previousThreadId.current || !previousThreadId.current) {
      return;
    }

    // Update reference and reset state
    previousThreadId.current = threadId;
    clearLatestMessage();
    setConversations(initialConversations);
    setLatestQuestion(null);
    setRunningAnswer(null);
    setIsFollowupFetching(false);
    if (!threadId && !reference) {
      setIsChatVisible(true);
      setIsReferenceVisible(false);
    }
  }, [ assetId, assetType, threadId, initialConversations, clearLatestMessage ]);

  // Clear message only on unmount
  useEffect(() => {
    return () => clearLatestMessage();
  }, []);

  useEffect(() => {
    console.log('222222222')
    if (reference) {
      setCurrentReference(reference);
      setIsReferenceVisible(true);
      setIsChatVisible(true);
      onToggle(true);
    }
  }, [ reference ]);

  useEffect(() => {
    setIsChatVisible(isOpen);
  }, [ isOpen ]);

  // Handle incoming WebSocket messages
  useEffect(() => {
    console.log('333333333', latestMessage)
    if (!latestMessage) return;

    if (!latestMessage.type || latestMessage.type === 'LIMIT') {
      showAlert('error', commonI18n.DAILY_LIMIT_ERROR[lang]);
      setIsFollowupFetching(false);
    } else if (latestMessage.type === 'CONTENT') {
      // NOTE: avoid to update the running answer when the threadId is not the same as the latestMessage.id
      if (latestMessage.thread_id !== threadId) {
        return;
      }
      setRunningAnswer(latestMessage);

      if (latestMessage.status === 'DONE') {
        setIsFollowupFetching(false);
        setRunningAnswer(null);
        setConversations((prev) => [ ...prev, latestMessage ]);
        setShowDoubleCheck(false);
      }
    }
  }, [ latestMessage ]);

  // Replace the existing scroll effect
  useEffect(() => {
    const container = chatContainerRef.current;
    if (!container) return;

    const handleScroll = () => {
      console.log('scrolled side chat');
      // Update the ref value instead of state
      autoScrollRef.current = false;
    };

    container.addEventListener('wheel', handleScroll);
    return () => container.removeEventListener('wheel', handleScroll);
  }, [ isChatVisible ]);

  // Modify the existing scroll effect
  useEffect(() => {
    if (autoScrollRef.current) {
      setTimeout(() => {
        bottomRef.current?.scrollIntoView({ behavior: 'smooth' });
      }, 300);
    }
  }, [ conversations, latestQuestion, runningAnswer ]);

  // Reset newQuestionTriggered when the scroll completes
  useEffect(() => {
    if (newQuestionTriggered) {
      const timeoutId = setTimeout(() => {
        setNewQuestionTriggered(false);
      }, 1000); // Adjust timing as needed

      return () => clearTimeout(timeoutId);
    }
  }, [ newQuestionTriggered ]);

  // Add effect to reset panel state when assetId or threadId changes
  useEffect(() => {
    // Reset all chat-related states
    setConversations(initialConversations);
    setLatestQuestion(null);
    setRunningAnswer(null);
    setIsFollowupFetching(false);
    setShowDoubleCheck(false);
  }, [ assetId, initialConversations ]); // Dependencies that should trigger reset

  const handleSubmit = async (text, attachments = [], useUpgrade = false) => {
    setShowDoubleCheck(false);
    if (selectedItems.length === 0 && assetType !== 'discover' && assetType !== 'clinical-guideline' && !threadId) {
      showAlert('error', commonI18n.SELECT_ITEM_ERROR[lang]);
      return;
    }
    if (isFollowupFetching || !isSocketOpen) {
      showAlert('error', commonI18n.CONNECTION_ERROR[lang]);
      return;
    }

    // Update ref instead of state
    autoScrollRef.current = true;
    setNewQuestionTriggered(true);
    setLatestQuestion({
      question: text,
      upload_files: attachments,
    });
    setIsFollowupFetching(true);
    
    try {
      let currentThreadId = threadId;
      if (!currentThreadId) {
        currentThreadId = await createThread(
          text,
          assetType === 'discover' ? 'mindsearchrefer' : assetType === 'clinical-guideline' ? 'mindsearchclinicalguideline' : 'mindsearchworkflowrefer',
          assetType,
          assetId,
        );
        onThreadCreated(currentThreadId);
      }

      let context = {
        ids: [
          ...selectedItems.map((item) => item.id),
        ],
        query: {},
      };
      if (assetType === 'clinical-guideline') {
        context = {
          ...context,
          'reference_type': assetType,
          reference: assetId,
          ids: treeId ? [ treeId ] : [],
        }
      }

      sendMessage({
        thread_id: currentThreadId,
        question: text,
        upload_files: attachments?.map((attachment) => attachment.id),
        enable_rag: isWebSearchOn,
        agent: assetType === 'discover' ? 'mindsearchrefer' : assetType === 'clinical-guideline' ? 'mindsearchclinicalguideline' : 'mindsearchworkflowrefer',
        language: langMap[lang].toUpperCase(),
        context,
      });
    } catch (error) {
      setIsFollowupFetching(false);
      showAlert('error', error?.response?.data?.error || error.message);
    }
  };

  // Calculate total width based on visible panels
  const getTotalWidth = useCallback(() => {
    if (isChatVisible && isReferenceVisible) {
      const referenceWidth = currentReference?.url?.toLowerCase().includes('.pdf') ? 800 : 400;
      const minWidth = 484 + referenceWidth;
      return chatWidth < minWidth ? minWidth : chatWidth;
    } else if (isChatVisible) {
      return chatWidth;
    } else if (isReferenceVisible) {
      return currentReference?.url?.toLowerCase().includes('.pdf') ? 800 : 400;
    }
    return 0;
  }, [ isChatVisible, isReferenceVisible, currentReference, chatWidth ]);

  useEffect(() => {
    if (isChatVisible && isReferenceVisible) {
      const referenceWidth = currentReference?.url?.toLowerCase().includes('.pdf') ? 800 : 400;
      const minWidth = 484 + referenceWidth;
      if (chatWidth < minWidth) {
        const newPercentage = Math.min(0.8, minWidth / mainWidth);
        setWidthPercentage(newPercentage);
      }
    }
  }, [ isChatVisible, isReferenceVisible, currentReference, chatWidth, mainWidth ]);

  const getToggleButtonRight = () => {
    const totalWidth = getTotalWidth();
    const maxWidth = window.innerWidth - 90; // calc(100vw - 90px)
    return Math.min(totalWidth, maxWidth);
  };

  const handleReferenceClick = (ref) => {
    console.log('chat panel reference', ref);
    setIsReferenceVisible(true);
    setIsChatVisible(true);
    setCurrentReference(ref);
    onToggle(true);
  };

  const doubleCheck = () => {
    // send a message via the socket service
    let context = {
      ids: [
        ...selectedItems.map((item) => item.id),
      ],
      query: {},
    };
    // Update ref instead of state
    autoScrollRef.current = true;
    setShowDoubleCheck(false);
    setNewQuestionTriggered(true);
    setLatestQuestion({
      question: commonI18n.DOUBLE_CHECK_QUESTION[lang],
      upload_files: [],
    });
    setIsFollowupFetching(true);
    sendMessage({
      thread_id: threadId,
      question: commonI18n.DOUBLE_CHECK_QUESTION[lang],
      upload_files: [],
      enable_rag: isWebSearchOn,
      context,
      agent: 'mindsearchdoublecheck',
      language: langMap[lang].toUpperCase(),
    });
  };

  // Handle drag start
  const handleDragStart = (e) => {
    setIsDragging(true);
    
    // Record initial values
    dragStartRef.current = {
      startX: e.clientX,
      startPercentage: chatWidth / mainWidth,
    };
    
    document.addEventListener('mousemove', handleDrag);
    document.addEventListener('mouseup', handleDragEnd);
  };

  // Handle dragging
  const handleDrag = (e) => {    
    // 获取浏览器窗口宽度
    const windowWidth = window.innerWidth;
    // 计算距离右侧的距离
    const distanceFromRight = windowWidth - e.clientX;

    if (distanceFromRight < 266) {
      handleDragEnd();
      handleChatClose();
      return;
    }
    
    // Calculate drag distance as percentage of mainWidth
    const dragDistance = dragStartRef.current.startX - e.clientX;
    const dragPercentage = dragDistance / mainWidth;
    
    // Calculate new width percentage
    let newPercentage = dragStartRef.current.startPercentage + dragPercentage;
    
    // Limit percentage range
    newPercentage = Math.min(0.8, newPercentage);
    
    mainWidth * newPercentage >= 484 && setWidthPercentage(newPercentage);
  };

  // Handle drag end
  const handleDragEnd = () => {
    setIsDragging(false);
    document.removeEventListener('mousemove', handleDrag);
    document.removeEventListener('mouseup', handleDragEnd);
  };

  const handleChatClose = () => {
    setIsChatVisible(false);
    setIsReferenceVisible(false);
    onToggle(false);
  }

  return (
    <>
      {/* Always visible toggle button */}
      <motion.div
        className={`fixed bottom-[110px] -translate-y-1/2 z-40 ${isChatVisible ? 'hidden' : ''}`}
        animate={{
          right: getToggleButtonRight(),
        }}
        transition={{ duration: 0.2, ease: 'easeInOut' }}
      >
        <div className="flex items-center">
          <div className="relative group/tooltip">
            <div
              className="bg-white cursor-pointer border border-[#F1F1F1] border-r-0 rounded-l-full shadow-[4px_0_16px_#00000014] p-1.5 pl-2.5"
              onClick={() => {
                if (!isChatVisible && !isReferenceVisible) {
                  setIsChatVisible(true);
                  onToggle(true);
                  !hiddenChatPanalTips && switchHiddenChatPanalTips();
                } else {
                  setIsChatVisible(false);
                  setIsReferenceVisible(false);
                  onToggle(false);
                }
              }}
            >
              <ChatBlack className="h-5 w-5" />
            </div>

            {
              !hiddenChatPanalTips && <div className="absolute -left-2 -translate-x-full top-1/2 -translate-y-1/2 pr-1.5">
                <div className="relative p-4 bg-white rounded-[20px] shadow-[5px_0_20px_#0000001F] w-max">
                  <div className="absolute -right-1.5 top-1/2 -translate-y-1/2 w-3 h-3 bg-white rotate-45 rounded-tr-[4px] shadow-[4px_-4px_8px_-4px_#0000001F]"></div>
                  <div className="text-base font-medium text-black">{chatI18n.ASK_HERE[lang]}</div>
                  <div className="mt-2 text-xs text-gray-1">{chatI18n.VARIOUS_TYPES[lang]}</div>
                  <div className="mt-2 flex justify-end">
                    <button
                      className="px-3 py-1.5 text-sm font-semibold text-white 
                    bg-noah-theme rounded-lg
                    transition-all duration-200 text-center"
                      onClick={switchHiddenChatPanalTips}
                    >
                      {chatI18n.GOT_IT[lang]}
                    </button>
                  </div>
                </div>
              </div>
            }
            
            {/* Optional: Tooltip */}
            <Tooltip
              tipClassName="right-10 top-1/2 -translate-y-1/2"
              text={(isChatVisible || isReferenceVisible) ? chatI18n.HIDE_PANEL[lang] : chatI18n.SHOW_PANEL[lang]}
              show={hiddenChatPanalTips}
            />
          </div>
        </div>
      </motion.div>

      {/* Panel Container */}
      <motion.div
        initial={false}
        animate={{
          x: isChatVisible || isReferenceVisible ? 0 : chatWidth,
        }}
        style={{ maxWidth: 'calc(100vw - 90px)', width: getTotalWidth() }}
        transition={{ duration: 0.3, ease: 'easeInOut' }}
        className={`pl-3 fixed top-0 right-0 z-40 h-screen border-l border-[#E7E7E7] bg-base-100 flex overflow-x-auto shadow-[4px_0_12px_#00000050] ${className} ${isDragging ? 'select-none' : ''}`}
      >
        {/* Drag handle */}
        <div
          ref={dragRef}
          className="flex items-center justify-center fixed z-50 top-0 w-3 h-full cursor-ew-resize bg-[#3333330A] hover:bg-base-200/80 transition-colors"
          style={{ right: `${getToggleButtonRight() - 13}px` }}
          onMouseDown={handleDragStart}
        >
          <div className="bg-[#888D92] w-1 h-7 rounded-full"></div>
        </div>
        
        {/* Chat Section */}
        {isChatVisible && (
          <div className="flex flex-col flex-1 relative" style={{ maxWidth: 'calc(100vw - 90px)' }}>
            <div className="p-5 pb-4 border-b border-[#ededed]">
              <div className="flex justify-between items-center">
                <h3 className="text-base font-semibold text-black">{chatI18n.CHAT[lang]}</h3>
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => {
                      let url = '';
                      if (assetType === 'discover') {
                        url = `/discover/article/${assetId}`;
                      } else if (assetType === 'clinical-guideline') {
                        url = `/tool/clinical-guidelines/${assetId}/${treeId || ''}`;
                      } else {
                        url = `/${assetType}/${assetId}`;
                      }
                      // navigate(url);
                      window.location.href = url;
                    }}
                    className="p-1 hover:bg-base-200 rounded-lg transition-colors duration-200 group/tooltip relative"
                    data-tip={chatI18n.CREATE_NEW[lang]}
                  >
                    <Tooltip
                      tipClassName="-left-1 -translate-x-full top-1/2 -translate-y-1/2"
                      text={chatI18n.CREATE_NEW[lang]}
                    />
                    <PencilSquareIcon className="w-4 h-4" />
                  </button>
                  <button
                    onClick={handleChatClose}
                    className="p-1 hover:bg-base-200 rounded-lg transition-colors duration-200"
                  >
                    <CloseIcon className="w-4 h-4" />
                  </button>
                </div>
              </div>
              {/* Add Selected Items */}
              {selectedItems?.length > 0 && (
                <div className="max-h-[200px] overflow-y-auto">
                  <SelectedItems 
                    items={selectedItems}
                    onRemove={onItemRemove}
                    type={itemType}
                  />
                </div>
              )}
            </div>


            {/* Chat Messages */}
            <div ref={chatContainerRef} className="flex-1 overflow-y-auto p-4 bg-white">
              {/* Add Hint Message when no conversations */}
              {selectedItems.length === 0
                && conversations.length === 0
                && !isFollowupFetching
                && getHintMessage(assetType, assetId, lang)
                && assetType !== 'clinical-guideline'
                && (
                  <div className="bg-base-200/50 backdrop-blur-sm rounded-lg p-4 mb-4">
                    <p className="text-sm text-gray-600">
                      {getHintMessage(assetType, assetId, lang)}
                    </p>
                    <div className="mt-4 flex justify-center">
                      <img 
                        src={selectedTips} 
                        alt="Selected Tips"
                        className="rounded-lg shadow-md max-w-full h-auto object-contain"
                        style={{ maxHeight: '200px' }}
                      />
                    </div>
                  </div>
                )}

              {/* Suggestions Section */}
              {suggestions.length > 0 &&
                conversations.length === 0 &&
                !isFollowupFetching && (
                <div className="mt-3">
                  <h3 className="text-base font-semibold mb-3 text-black-1">
                    {commonI18n.RELATED[lang]}
                  </h3>
                  <ul className="space-y-3">
                    {suggestions.map((suggestion, index) => (
                      <li
                        key={index}
                        className="group relative flex items-center justify-between min-w-0 py-4 px-6 cursor-pointer rounded-2xl bg-[#3333330A] gap-x-1"
                        onClick={() => handleSubmit(suggestion)}
                      >
                        <p className="text-sm leading-5 text-black line-clamp-2 mr-1 align-middle">
                          {suggestion}
                        </p>
                        <ArrowLongRight className="w-4 h-4 align-middle hidden group-hover:block" />
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Conversations */}
              {conversations.map((item, index) => (
                <OneConversation
                  key={index}
                  item={item}
                  onTriggerNewQuestion={handleSubmit}
                  onReferenceClick={handleReferenceClick}
                />
              ))}
              {showDoubleCheck && (
                <div className="mt-4 text-center">
                  <button className="btn btn-primary" onClick={() => doubleCheck()}>
                    {chatI18n.DOUBLE_CHECK[lang]}
                  </button>
                </div>
              )}
              {/* Running Conversation */}
              {isFollowupFetching && (
                <OneRunningConversation
                  item={latestQuestion}
                  runningAnswer={runningAnswer}
                />
              )}

              <div className="h-24" />
              <div ref={bottomRef} />
            </div>

            {/* Input Section */}
            <div className="w-full absolute bottom-0 left-0 p-5 bg-transparent">
              <SingleLineInput
                onSubmit={handleSubmit}
                enableUpload={enableUpload}
                disabled={isFollowupFetching}
                className="w-full"
                placeholder={chatI18n.TYPE_MESSAGE[lang]}
                onFileCountChange={(count) => {
                  console.log('count', count);
                }}
              />
            </div>
          </div>
        )}

        {/* Reference Section */}
        {isReferenceVisible && currentReference && (
          <ReferenceView 
            reference={currentReference}
            isChatVisible={isChatVisible}
            onClose={() => {
              setIsReferenceVisible(false);
              onReferenceClose();
              if (!isChatVisible) onToggle(false);
            }}
            onOpenChat={() => setIsChatVisible(true)}
          />
        )}
      </motion.div>

      {/* Mobile overlay */}
      {isMobile && (isChatVisible || isReferenceVisible) && (
        <div
          className="fixed inset-0 bg-black/20 z-30"
          onClick={() => {
            setIsChatVisible(false);
            setIsReferenceVisible(false);
            onToggle(false);
          }}
        />
      )}
      {/* Mask layer when dragging */}
      {isDragging && (
        <div className="fixed inset-0 z-50 cursor-ew-resize" />
      )}
    </>
  );
};

export default ChatPanel;
