import React, { useCallback, useEffect, useRef, useState } from 'react';

import { blue } from '@ant-design/colors';
import { 
  ApiOutlined,
  CheckCircleFilled,
  ClockCircleFilled, 
  CloseCircleFilled, 
  CodeOutlined,
  DatabaseOutlined,
  ExperimentOutlined,
  FileSearchOutlined,
  InfoCircleOutlined,
  SearchOutlined,
  SettingOutlined,
  <PERSON>boltOutlined,
  ToolOutlined
} from '@ant-design/icons';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { Avatar, ConfigProvider, List, Popover, Steps, Tooltip } from 'antd';

import { PlanStep } from '../../AskPage/components/hitl/conversationComponents';
import DisplayFormattedText from '../../AskPage/components/markdown';
import { useLanguage } from '../../utils/context/lang';
import { useLayout } from '../../utils/context/layout';
import { i18n } from '../i18n/common';

/**
 * PlanView - A component for displaying plan steps in a vertical timeline
 * using Ant Design's Steps component in a side panel
 * 
 * @param {Object} planUpdate - The plan update object containing plan steps
 * @param {Function} onReferenceClick - Function to handle reference clicks
 * @param {Function} onClose - Function to close the panel
 * @returns {JSX.Element|null} - The rendered sidebar panel or null if no plan
 */
const PlanView = ({ planUpdate, isChatVisible, onReferenceClick, onClose, onOpenChat = () => {} }) => {
  const { lang } = useLanguage();
  const { collapseSidebar } = useLayout();
  const [ width, setWidth ] = useState(400);
  const MIN_WIDTH = 400;
  const MAX_WIDTH = 600;
  const resizingRef = useRef(false);
  const startPosRef = useRef(0);
  const startWidthRef = useRef(0);

  // Collapse sidebar when the plan view is shown
  useEffect(() => {
    collapseSidebar();
  }, [ collapseSidebar ]);

  // Handle resize functionality
  const handleMouseDown = useCallback(
    (e) => {
      resizingRef.current = true;
      startPosRef.current = e.clientX;
      startWidthRef.current = width;
      document.body.style.cursor = 'ew-resize';
      document.body.style.userSelect = 'none';
    },
    [ width ]
  );

  const handleMouseMove = useCallback((e) => {
    if (!resizingRef.current) return;
    const delta = e.clientX - startPosRef.current;
    const newWidth = Math.min(
      Math.max(startWidthRef.current - delta, MIN_WIDTH),
      MAX_WIDTH
    );
    setWidth(newWidth);
  }, []);

  const handleMouseUp = useCallback(() => {
    resizingRef.current = false;
    document.body.style.cursor = '';
    document.body.style.userSelect = '';
  }, []);

  useEffect(() => {
    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('mouseup', handleMouseUp);

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    };
  }, [ handleMouseMove, handleMouseUp ]);

  // Determine the correct icon based on the step tool/action
  const getStepIcon = (step) => {
    const toolName = (step.tool || '').toLowerCase();
    
    if (toolName.includes('search')) return <SearchOutlined className="h-4 w-4" />;
    if (toolName.includes('read') || toolName.includes('file')) return <FileSearchOutlined className="h-4 w-4" />;
    if (toolName.includes('api') || toolName.includes('fetch')) return <ApiOutlined className="h-4 w-4" />;
    if (toolName.includes('code') || toolName.includes('edit')) return <CodeOutlined className="h-4 w-4" />;
    if (toolName.includes('analyze') || toolName.includes('process')) return <ExperimentOutlined className="h-4 w-4" />;
    if (toolName.includes('database') || toolName.includes('query')) return <DatabaseOutlined className="h-4 w-4" />;
    if (toolName.includes('explain') || toolName.includes('summarize')) return <InfoCircleOutlined className="h-4 w-4" />; 
    if (toolName.includes('execute') || toolName.includes('run')) return <ThunderboltOutlined className="h-4 w-4" />;
    if (toolName.includes('config') || toolName.includes('setup')) return <SettingOutlined className="h-4 w-4" />;
    
    // Default to a generic tool icon if no specific match
    return <ToolOutlined className="h-4 w-4" />;
  };

  // Check if we have a valid plan update
  if (!planUpdate || !planUpdate.plan || !Array.isArray(planUpdate.plan) || planUpdate.plan.length === 0) {
    return null;
  }

  return (
    <div
      className="flex flex-col h-screen border-l border-base-200 relative transition-all duration-200 w-[400px]"
      style={{
        width: `${width}px`,
        transition: resizingRef.current ? 'none' : 'width 0.2s ease',
      }}
    >
      {/* Resizer handle */}
      <div
        className={`absolute -left-3 top-0 w-6 h-full z-10 cursor-ew-resize hover:bg-primary-color/20 ${
          resizingRef.current ? 'bg-primary-color/20' : ''
        }`}
        onMouseDown={handleMouseDown}
      />

      {/* Header */}
      <div className="flex-none p-4 border-b border-base-200 bg-base-100">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-semibold">Plan Steps</h3>
          <div className="flex gap-2">
            {!isChatVisible && (
              <button
                onClick={onOpenChat}
                className="text-sm px-2 py-1 hover:bg-base-200 rounded transition-colors duration-200"
              >
                Open Chat
              </button>
            )}
            <button
              onClick={onClose}
              className="p-1.5 hover:bg-base-200 rounded-full transition-colors duration-200"
            >
              <XMarkIcon className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>

      {/* Plan steps content */}
      <div className="flex-1 overflow-auto bg-neutral-50">
        <ConfigProvider
          theme={{
            token: {
              colorPrimary: blue[5],
              borderRadius: 4,
              fontSize: 12
            },
            components: {
              Steps: {
                titleLineHeight: 1.3,
                descriptionMaxWidth: 300,
                colorTextDescription: 'rgba(0, 0, 0, 0.6)'
              }
            }
          }}
        >
          <div className="p-4">
            {/* Main description */}
            <div className="bg-blue-50 rounded-lg p-4 mb-4 border border-blue-100">
              <h4 className="font-medium text-blue-800">Current Plan</h4>
              <p className="text-sm text-blue-600 mt-1">
                {planUpdate.plan[0]?.reason || 'Executing the plan step by step'}
              </p>
            </div>

            {/* Vertical timeline of plan steps */}
            <div className="mt-4">
              {planUpdate.plan.map((step, index) => (
                <PlanStep 
                  key={`step-${index}`}
                  plan={step} 
                  isLastInSequence={index === planUpdate.plan.length - 1}
                  onReferenceClick={onReferenceClick}
                />
              ))}
            </div>
          </div>
        </ConfigProvider>
      </div>
    </div>
  );
};

export default PlanView; 