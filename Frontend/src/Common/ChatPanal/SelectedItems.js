import React, { useEffect, useRef, useState } from 'react';

import { ChevronDownIcon, ChevronUpIcon } from '@heroicons/react/20/solid';

import { useLanguage } from '../../utils/context/lang';
import { ReactComponent as CloseIcon } from '../assets/close-green.svg';

const itemType = {
  'clinical-result': {
    en: 'trials',
    zh: '临床试验',
  },
  'drug-compete': {
    en: 'drugs',
    zh: '药物',
  },
  'conference': {
    en: 'Papers',
    zh: '论文',
  },
  'catalyst': {
    en: 'Catalysts',
    zh: '催化剂',
  },
  'clinical-guideline': {
    en: 'treatment nodes',
    zh: '诊疗节点',
  }
}

const selectLimitText = {
  'clinical-result': {
    en: 'You can select up to 5 trials',
    zh: '您最多可以选择5个临床试验',
  },
  'drug-compete': {
    en: 'You can select up to 50 drugs',
    zh: '您最多可以选择50个药物',
  },
  'conference': {
    en: 'You can select up to 5 papers',
    zh: '您最多可以选择5篇论文',
  },
  'catalyst': {
    en: 'You can select up to 5 catalysts',
    zh: '您最多可以选择5个催化剂',
  }
}

const selectLimit = {
  'clinical-result': 10,
  'drug-compete': 100,
  'conference': 10,
  'catalyst': 10,
}

const SelectedItems = ({ items, onRemove, type = 'conference' }) => {
  const { lang } = useLanguage();
  const [ isExpanded, setIsExpanded ] = useState(false);
  const [ hasOverflow, setHasOverflow ] = useState(false);
  const containerRef = useRef(null);

  useEffect(() => {
    const checkOverflow = () => {
      if (containerRef.current) {
        const isOverflowing = containerRef.current.scrollHeight > 32;
        setHasOverflow(isOverflowing);
      }
    };

    checkOverflow();
    const resizeObserver = new ResizeObserver(checkOverflow);
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }

    return () => resizeObserver.disconnect();
  }, [ items ]);

  if (!items?.length) return null;

  return (
    <div className="pt-4">
      <div className="flex items-center justify-between mb-2 h-6">
        <h3 className="text-xs font-medium text-gray-1">{lang === 'zh' ? '已选择' : 'Selected'} {itemType[type][lang]}  - {items.length}{selectLimit[type] ? `/${selectLimit[type]} ` : ' '}</h3>
        {hasOverflow && (
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="p-[3px] hover:bg-gray-3 rounded-lg transition-colors duration-200"
          >
            {isExpanded ? (
              <ChevronUpIcon className="w-4 h-4 text-gray-1" />
            ) : (
              <ChevronDownIcon className="w-4 h-4 text-gray-1" />
            )}
          </button>
        )}
      </div>
      <div 
        ref={containerRef}
        className={`flex flex-wrap gap-[10px] overflow-hidden transition-all duration-300 ${isExpanded ? 'max-h-96' : 'max-h-[26px]'}`}
      >
        {items.map(item => (
          <div 
            key={item.id}
            className="group flex items-center gap-2 px-2 py-1 rounded-lg bg-[#E3F6F2] border border-noah-theme"
          >
            <span className="text-xs font-medium text-noah-theme truncate max-w-[125px]">
              {item.title || item.name || item.official_title|| item.citation_title || item.drug_name || item.content}
            </span>
            <CloseIcon
              onClick={(e) => {
                e.stopPropagation();
                onRemove(item);
              }}
              className="w-3 h-3 cursor-pointer"
            >
              ×
            </CloseIcon>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SelectedItems;