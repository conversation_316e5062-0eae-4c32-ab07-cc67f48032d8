import React from 'react';

import { Link } from 'react-router-dom';

import { useLanguage } from '../utils/context/lang';

const i18n = {
  PAGE_NOT_FOUND: {
    en: '404 - Page Not Found',
    zh: '404 - 页面未找到'
  },
  PAGE_NOT_FOUND_DESCRIPTION: {
    en: 'The page you are looking for does not exist or has been moved.',
    zh: '您访问的页面不存在或已被移动。'
  },
  GO_HOME: {
    en: 'Go to Home',
    zh: '返回首页'
  },
  GO_BACK: {
    en: 'Go Back',
    zh: '返回上页'
  }
};

export default function NotFound() {
  const { lang } = useLanguage();
  
  const handleGoBack = () => {
    window.history.back();
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-[60vh] py-12 px-4">
      <div className="bg-base-200/50 backdrop-blur-sm rounded-xl p-8 max-w-lg w-full text-center">
        <div className="mb-6">
          <div className="text-6xl font-bold text-noah-theme mb-4">404</div>
          <svg 
            className="mx-auto h-16 w-16 text-base-content/30 mb-4" 
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" 
            />
          </svg>
        </div>
        
        <h1 className="text-2xl font-semibold text-base-content mb-3">
          {i18n.PAGE_NOT_FOUND[lang]}
        </h1>
        
        <p className="text-base-content/60 text-base mb-8">
          {i18n.PAGE_NOT_FOUND_DESCRIPTION[lang]}
        </p>
        
        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          <Link 
            to="/hitl"
            className="px-6 py-2.5 text-sm font-semibold text-white 
                     bg-noah-theme rounded-lg hover:bg-noah-theme/90
                     transition-all duration-200 text-center"
          >
            {i18n.GO_HOME[lang]}
          </Link>
          
          <button
            onClick={handleGoBack}
            className="px-6 py-2.5 text-sm font-semibold text-noah-theme 
                     bg-transparent border border-noah-theme rounded-lg 
                     hover:bg-noah-theme hover:text-white
                     transition-all duration-200 text-center"
          >
            {i18n.GO_BACK[lang]}
          </button>
        </div>
      </div>
    </div>
  );
} 