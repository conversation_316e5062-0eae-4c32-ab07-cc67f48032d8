const DRUG = {
  en: {
    title: 'Drug',
    placeholder: 'Drug name like Pembrolizumab',
  },
  zh: {
    title: '药物',
    placeholder: '药物名称，如 Pembrolizumab',
  },
  jp: {
    title: '薬剤',
    placeholder: '薬剤名（例：Pembrolizumab）',
  },
};

const DISEASE = {
  en: {
    title: 'Disease',
    placeholder: 'Disease name like Lung Cancer',
  },
  zh: {
    title: '疾病',
    placeholder: '疾病名称，如 Lung Cancer',
  },
  jp: {
    title: '疾患',
    placeholder: '疾患名（例：Lung Cancer）',
  },
};

const TARGET = {
  en: {
    title: 'Target',
    placeholder: 'Target name like PD-1',
  },
  zh: {
    title: '靶点',
    placeholder: '靶点名称，如 PD-1',
  },
  jp: {
    title: '標的',
    placeholder: '標的名（例：PD-1）',
  },
};

const COMPANY = {
  en: {
    title: 'Company',
    placeholder: 'Company name like <PERSON>fizer',
  },
  zh: {
    title: '公司',
    placeholder: '公司名称，如 Pfizer',
  },
  jp: {
    title: '企業',
    placeholder: '企業名（例：Pfizer）',
  },
};

const NCTID = {
  en: {
    title: 'NCT ID',
    placeholder: 'NCT ID like NCT02486718',
  },
  zh: {
    title: 'NCT ID',
    placeholder: 'NCT ID，如 NCT02486718',
  },
  jp: {
    title: 'NCT ID',
    placeholder: 'NCT ID（例：NCT02486718）',
  },
};

const PHASE = {
  en: {
    title: 'Phase',
    placeholder: 'Select phase',
  },
  zh: {
    title: '阶段',
    placeholder: '选择阶段',
  },
  jp: {
    title: 'フェーズ',
    placeholder: 'フェーズを選択',
  },
};

const GENDER = {
  en: {
    title: 'Gender',
    placeholder: 'Select gender',
  },
  zh: {
    title: '性别',
    placeholder: '选择性别',
  },
  jp: {
    title: '性別',
    placeholder: '性別を選択',
  },
};

const STUDY_TYPE = {
  en: {
    title: 'Study Type',
    placeholder: 'Select study type',
  },
  zh: {
    title: '研究类型',
    placeholder: '选择研究类型',
  },
  jp: {
    title: '研究タイプ',
    placeholder: '研究タイプを選択',
  },
};

const STATUS = {
  en: {
    title: 'Status',
    placeholder: 'Select status',
  },
  zh: {
    title: '状态',
    placeholder: '选择状态',
  },
  jp: {
    title: 'ステータス',
    placeholder: 'ステータスを選択',
  },
};

const ROUTE = {
  en: {
    title: 'Route',
    placeholder: 'Select route',
  },
  zh: {
    title: '给药途径',
    placeholder: '选择给药途径',
  },
  jp: {
    title: '投与経路',
    placeholder: '投与経路を選択',
  },
};

const DRUG_TYPE = {
  en: {
    title: 'Drug Type',
    placeholder: 'Select drug type',
  },
  zh: {
    title: '药物类型',
    placeholder: '选择药物类型',
  },
  jp: {
    title: '薬剤タイプ',
    placeholder: '薬剤タイプを選択',
  },
};

const DRUG_FEATURE = {
  en: {
    title: 'Drug Feature',
    placeholder: 'Select drug feature',
  },
  zh: {
    title: '药物特征',
    placeholder: '选择药物特征',
  },
  jp: {
    title: '薬剤特性',
    placeholder: '薬剤特性を選択',
  },
};

const LOCATION = {
  en: {
    title: 'Location',
    placeholder: 'Select location',
  },
  zh: {
    title: '地区',
    placeholder: '选择地区',
  },
  jp: {
    title: '地域',
    placeholder: '地域を選択',
  },
};

const DRUG_MODALITY = {
  en: {
    title: 'Drug Modality',
    placeholder: 'Select drug modality',
  },
  zh: {
    title: '药物类型',
    placeholder: '选择药物类型',
  },
  jp: {
    title: '薬剤タイプ',
    placeholder: '薬剤タイプを選択',
  },
};

const DATE_RANGE = {
  en: {
    past: 'Past',
    tillNow: 'Till Now',
  },
  zh: {
    past: '过去',
    tillNow: '至今',
  },
  jp: {
    past: '過去',
    tillNow: '現在',
  },
};

const IMPACT = {
  en: {
    title: 'Impact',
    placeholder: 'Select impact',
  },
  zh: {
    title: '影响',
    placeholder: '选择影响',
  },
  jp: {
    title: '影響',
    placeholder: '影響を選択',
  },
};

const TYPE = {
  en: {
    title: 'Type',
    placeholder: 'Select type',
  },
  zh: {
    title: '类型',
    placeholder: '选择类型',
  },
  jp: {
    title: 'タイプ',
    placeholder: 'タイプを選択',
  },
};

const ASSOCIATION = {
  en: {
    title: 'Association',
    placeholder: 'Association name like NCCN',
  },
  zh: {
    title: '协会',
    placeholder: '协会名称，如 NCCN',
  },
  jp: {
    title: '協会',
    placeholder: '協会名（例：NCCN）',
  },
};

export const i18n = {
  DRUG,
  DISEASE,
  TARGET,
  COMPANY,
  NCTID,
  PHASE,
  GENDER,
  STUDY_TYPE,
  STATUS,
  ROUTE,
  DRUG_TYPE,
  DRUG_FEATURE,
  LOCATION,
  DRUG_MODALITY,
  DATE_RANGE,
  IMPACT,
  TYPE,
  ASSOCIATION,
};
