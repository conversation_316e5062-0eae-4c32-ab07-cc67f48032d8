import React, { memo } from 'react';

import { useLanguage } from '../utils/context/lang';

const i18n = {
  CONTENT_NOT_AVAILABLE: {
    en: 'Content Not Available',
    zh: '内容暂不可见'
  },
  PLEASE_LOG_IN: {
    en: 'Please log in to access more features and content',
    zh: '请登录后查看更多功能和内容'
  }
};

const LogOutState = memo(() => {
  const { lang } = useLanguage();
  
  return (
    <div className="flex flex-col items-center justify-center py-12 px-4">
      <div className="bg-base-200/50 backdrop-blur-sm rounded-xl p-8 max-w-lg w-full text-center">
        <div className="mb-4">
          <svg className="mx-auto h-12 w-12 text-base-content/30" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <h3 className="text-lg font-semibold text-base-content mb-2">
          {i18n.CONTENT_NOT_AVAILABLE[lang]}
        </h3>
        <p className="text-base-content/60 text-sm mb-6">
          {i18n.PLEASE_LOG_IN[lang]}
        </p>
      </div>
    </div>
  );
});

LogOutState.displayName = 'LogOutState';

export default LogOutState;