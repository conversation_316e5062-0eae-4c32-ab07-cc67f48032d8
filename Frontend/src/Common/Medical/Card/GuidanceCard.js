import React from 'react';

import { ArrowRightIcon } from '@heroicons/react/24/outline';

import { useLanguage } from '../../../utils/context/lang';

import Badge from './Badge';

const i18n = {
  MORE_INFO: {
    zh: '更多信息',
    en: 'More info',
    jp: '詳細情報'
  },
  SOURCE: {
    en: 'Source',
    zh: '来源',
    jp: '出典'
  },
  PUBLICATION_DATE: {
    en: 'Publication Date',
    zh: '发布日期',
    jp: '公開日'
  },
  VERSION: {
    en: 'Version',
    zh: '版本',
    jp: 'バージョン'
  }
};

const GuidanceCard = ({ item, onClick }) => {
  const { lang } = useLanguage();
  
  return (
    <div
      onClick={onClick}
      className="group h-full p-6 border border-gray-200 rounded-lg overflow-hidden 
                 hover:shadow-xl hover:border-gray-300 transition-all duration-300 ease-in-out
                 bg-gradient-to-br from-white to-gray-50 
                 cursor-pointer flex flex-col justify-between"
    >
      <h3 className="text-xl font-semibold text-gray-800 group-hover:text-noah-theme
                   transition-colors duration-300 line-clamp-2">
        {item.name}
      </h3>
      
      <div className="space-y-4 mt-2">
        <div className="flex flex-wrap gap-2">
          {item.source && (
            <Badge variant="drug">{i18n.SOURCE[lang]}: {item.source}</Badge>
          )}
          {item.publication_date && (
            <Badge variant="status">{i18n.PUBLICATION_DATE[lang]}: {item.publication_date}</Badge>
          )}
          {item.version && (
            <Badge variant="disease">{i18n.VERSION[lang]}: {item.version}</Badge>
          )}
        </div>
        <div className="flex items-center gap-2 text-sm font-medium text-noah-theme
                      transition-colors">
          <span>{i18n.MORE_INFO[lang]}</span>
          <ArrowRightIcon className="w-4 h-4 transform group-hover:translate-x-1 transition-transform" />
        </div>
      </div>
    </div>
  );
};

export default GuidanceCard;
