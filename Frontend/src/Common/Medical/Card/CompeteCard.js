import React from 'react';

import { motion } from 'framer-motion';

import { ReactComponent as VectorIcon } from '../../../assets/vector.svg';
import { selectionStyles } from '../../styles/selection';

import Badge from './Badge';

// const Badge = ({ children, variant = 'default' }) => {
//   const variants = {
//     phase: 'bg-primary/80 text-primary-content border border-primary/20',
//     target: 'bg-info/10 text-primary-content border border-info/20',
//     type: 'bg-secondary/80 text-secondary-content border border-secondary/20',
//     indication: 'bg-accent/10 text-accent border border-accent/20',
//     default: 'bg-base-200 text-base-content/60'
//   };

//   return (
//     <span className={`px-2.5 py-1 text-xs font-medium rounded-full inline-flex items-center ${variants[variant]}`}>
//       {children}
//     </span>
//   );
// };

const InfoSection = ({ label, children }) => (
  <div className="space-y-1.5">
    <div className="text-xs text-base-content/50">{label}</div>
    <div className="text-sm text-base-content">{children}</div>
  </div>
);

const CompeteCard = ({ item, isSelected, onClick, lang = 'en' }) => {
  return (
    <motion.div
      onClick={onClick}
      className="bg-base-100 p-5 rounded-[20px] border border-[#F1F1F1] hover:border-base-300 overflow-hidden relative cursor-pointer transition-colors duration-200 flex"
    >
      {/* Selection Indicator */}
      <div className={`${selectionStyles.checkbox} 
          ${isSelected ? selectionStyles.selected : selectionStyles.unselected}`}
      >
        {isSelected && (
          <VectorIcon className={selectionStyles.checkIcon} />
        )}
      </div>

      <div className="ml-2 flex-1">
        {/* Header Section */}
        <div className="mb-2 space-y-2">
          <h3 className="text-base leading-[22px] font-medium text-black-1">
            {item.name}
          </h3>
          <div className="text-sm text-gray-1 font-normal flex justify-between">
            <div>
              {item.lead_company}
            </div>
            <div>
              {item.location}
            </div>
          </div>
        </div>

        {/* Status Section */}
        <div className="flex flex-wrap gap-1 mb-4">
          {item.phase && (
            <Badge variant="phase">
              {[ 'I', 'II', 'III', 'IV' ].includes(item.phase) ? `Phase ${item.phase}` : item.phase}
            </Badge>
          )}
          {item.indication && (
            <Badge variant="drug">
              {item.indication}
            </Badge>
          )}
        </div>

        {/* Details Section */}
        <div className="space-y-5">
          {/* Targets */}
          {item.target?.length > 0 && (
            <InfoSection label={
              <span className="font-medium uppercase tracking-wider text-[11px]">Targets</span>
            }>
              <div className="flex flex-wrap gap-2">
                {item.target.map((target, idx) => (
                  <Badge key={idx} variant="disease">{target}</Badge>
                ))}
              </div>
            </InfoSection>
          )}

          {/* Drug Types */}
          {item.drug_type?.length > 0 && (
            <InfoSection label={
              <span className="font-medium uppercase tracking-wider text-[11px]">Drug Types</span>
            }>
              <div className="flex flex-wrap gap-2">
                {item.drug_type.map((type, idx) => (
                  <Badge key={idx} variant="status">{type}</Badge>
                ))}
              </div>
            </InfoSection>
          )}

          {/* Drug Modality */}
          {item.drug_modality?.length > 0 && (
            <InfoSection label={
              <span className="font-medium uppercase tracking-wider text-[11px]">Drug Modality</span>
            }>
              <div className="flex flex-wrap gap-2">
                {item.drug_modality.map((type, idx) => (
                  <Badge key={idx} variant="status">{type}</Badge>
                ))}
              </div>
            </InfoSection>
          )}
        </div>
      </div>
    </motion.div>
  );
};

export default CompeteCard;
