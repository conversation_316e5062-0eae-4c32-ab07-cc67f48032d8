import React from 'react';

const Badge = ({ children, variant = 'default' }) => {
  const variants = {
    phase: 'bg-[#FF70700F] text-[#FF7070]',
    drug: 'bg-[#00A98F0F] text-noah-theme',
    disease: 'bg-[#03BBEE0F] text-[#03BBEE]',
    status: 'bg-[#ffbf000f] text-[#ffbf00]',
    default: 'bg-[#F1F3F4] text-gray-1'
  };
  
  return (
    <span className={`px-2.5 py-0.5 text-xs font-medium rounded-md ${variants[variant]}`}>
      {children}
    </span>
  );
};

export default Badge;