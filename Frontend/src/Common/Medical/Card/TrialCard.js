import React, { useEffect, useRef, useState } from 'react';

import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';

import { ReactComponent as ChevronDown } from '../../../assets/arrow-down.svg';
import { ReactComponent as VectorIcon } from '../../../assets/vector.svg';
import { useLanguage } from '../../../utils/context/lang';
import { i18n as commonI18n } from '../../i18n/common';
import { selectionStyles } from '../../styles/selection';

import Badge from './Badge';

const TrialCard = ({ item, isSelected, onClick }) => {
  const [ isExpanded, setIsExpanded ] = useState(false);
  const [ shouldShowViewAll, setShouldShowViewAll ] = useState(false);
  const { lang } = useLanguage();
  const contentRef = useRef(null);

  useEffect(() => {
    if (contentRef.current && !isExpanded) {      
      const height = contentRef.current.scrollHeight;
      setShouldShowViewAll(height > 260);
    }
  }, [ contentRef?.current?.scrollHeight ]);

  const handleViewAllClick = (e) => {
    e.stopPropagation();
    setIsExpanded(true);
  };

  const highlightNumbers = (text) => {
    if (!text?.finding) return text;
    return text.finding.split(/(\s|^)(\d+(?:\.\d+)?%|\d+\.\d+)/).map((part, index) => {
      if (part.match(/^(\d+(?:\.\d+)?%|\d+\.\d+)$/)) {
        return (
          <span key={index} className="text-primary-content font-medium">
            {part}
          </span>
        );
      }
      return part;
    });
  };

  return (
    <motion.div
      onClick={onClick}
      className="bg-base-100 p-5 rounded-[20px] border border-[#F1F1F1] hover:border-base-300 overflow-hidden relative cursor-pointer transition-colors duration-200 flex"
    >
      {/* Selection Indicator */}
      <div className={`${selectionStyles.checkbox} 
          ${isSelected ? selectionStyles.selected : selectionStyles.unselected}`}
      >
        {isSelected && (
          <VectorIcon className={selectionStyles.checkIcon} />
        )}
      </div>
      <div ref={contentRef} className={`ml-2 transition-all duration-300 ease-in-out ${!isExpanded ? 'max-h-[260px]' : 'max-h-[9999px]'} overflow-hidden`}>
        {/* Header without Selection Indicator */}
        <div className="mb-2">
          <div className="space-y-2">
            <a 
              href={`/detail/clinical-trial/${item.id}`}
              target="_blank" 
              rel="noopener noreferrer" 
              className="hover:underline"
              onClick={(e) => e.stopPropagation()}
            >
              <h3 className="text-base leading-[22px] font-medium text-black-1">
                {item.official_title}
              </h3>
            </a>
            <div className="text-sm text-gray-1 font-normal">
              {item.lead_company}
            </div>
          </div>
        </div>

        {/* Status Badges */}
        <div className="flex flex-wrap gap-1 mb-4">
          {item.drug_name && (
            <Badge variant="drug">{item.drug_name}</Badge>
          )}
          {item.indication_name && (
            <Badge variant="disease">{item.indication_name}</Badge>
          )}
          {item.phase && (
            <Badge variant="phase">Phase {item.phase}</Badge>
          )}
          {item.current_status && (
            <Badge variant="status">{item.current_status}</Badge>
          )}
        </div>

        {/* Key Findings */}
        {item.key_findings?.length > 0 && (
          <div className="pt-4 border-t-[0.5px] border-[#EAEAEA]">
            <div className="text-sm font-normal text-gray-1 mb-2.5">
                Key Findings
            </div>
            <ul className="space-y-2 list-disc pl-5 marker:text-gray-1">
              {item.key_findings.map((finding, idx) => (
                <li key={idx} className="text-sm font-normal text-gray-1">
                  {highlightNumbers(finding)}
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
      {shouldShowViewAll && !isExpanded && (
        <div className="absolute bottom-5 mx-5 left-0 right-0">
          <div className="h-2 w-full bg-gradient-to-t from-white via-white to-transparent"></div>
          <div 
            className="flex items-center justify-end py-0.5 bg-white"
            onClick={handleViewAllClick}
          >
            <div className="text-sm text-gray-1 mr-0.5">{commonI18n.VIEW_ALL[lang]}</div>
            <ChevronDown className="h-4 w-4" />
          </div>
        </div>
      )}
    </motion.div>
  );
};

export default TrialCard;
