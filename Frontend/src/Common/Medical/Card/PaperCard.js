import React, { useEffect, useRef, useState } from 'react';

import { motion } from 'framer-motion';

import { ReactComponent as ChevronDown } from '../../../assets/arrow-down.svg';
import { ReactComponent as VectorIcon } from '../../../assets/vector.svg';
import { useLanguage } from '../../../utils/context/lang';
import { i18n as commonI18n } from '../../i18n/common';
import { selectionStyles } from '../../styles/selection';

import Badge from './Badge';

const PaperCard = ({ paper, isSelected, onClick }) => {
  const [ isExpanded, setIsExpanded ] = useState(false);
  const [ shouldShowViewAll, setShouldShowViewAll ] = useState(false);
  const contentRef = useRef(null);
  const { lang } = useLanguage();

  const handleTitleClick = (e) => {
    // Prevent the click event from bubbling up to the card
    e.stopPropagation();
  };

  const handleViewAllClick = (e) => {
    e.stopPropagation();
    setIsExpanded(true);
  };

  useEffect(() => {
    if (contentRef.current && !isExpanded) {      
      const height = contentRef.current.scrollHeight;
      setShouldShowViewAll(height > 260);
    }
  }, [ contentRef?.current?.scrollHeight ]);

  return (
    <motion.div
      onClick={onClick}
      className="bg-base-100 p-5 rounded-[20px] border border-[#F1F1F1] hover:border-base-300 overflow-hidden relative cursor-pointer transition-colors duration-200 flex"
    >
      {/* Selection Indicator */}
      <div className={`absolute ${selectionStyles.checkbox} 
          ${isSelected ? selectionStyles.selected : selectionStyles.unselected}`}
      >
        {isSelected && (
          <VectorIcon className={selectionStyles.checkIcon} />
        )}
      </div>

      <div ref={contentRef} className={`pl-6 transition-all duration-300 ease-in-out ${!isExpanded ? 'max-h-[260px]' : 'max-h-[9999px]'} overflow-hidden`}>
        {/* Header */}
        <div className="mb-2">
          <div className="flex items-center justify-start mb-2">
            {paper.presentation_type && (
              <span className="px-2 py-1 text-xs rounded-full bg-base-200 text-base-content/60">
                {paper.presentation_type}
              </span>
            )}
          </div>
          <h3 className="text-base leading-[22px] font-medium text-black-1">
            <a 
              href={paper.link || 'https://apsard.org/wp-content/uploads/2025/01/2025-APSARD-Abstract-Book.pdf'} 
              target="_blank" 
              rel="noopener noreferrer" 
              className="hover:underline"
              onClick={handleTitleClick}
            >
              {paper.citation_title}
            </a>
          </h3>
        </div>

        {/* Author Info */}
        <div className="text-sm text-gray-1 mb-2">
          <div className="flex items-center gap-2 max-w-full">
            <span className="font-medium truncate">{paper.citation_author}</span>
            <span className="text-black/50 flex-shrink-0">·</span>
            <span className="text-xs truncate text-black/50">{paper.citation_author_institution}</span>
          </div>
        </div>

        {/* Tags Section */}
        <div className="flex flex-wrap gap-2 mb-4">
          {/* Primary Tags */}
          {paper.drug.map((drug, idx) => (
            drug !== 'Not Reported' && (
              <Badge key={idx} variant="drug">{drug}</Badge>
            )
          ))}
          {paper.target.map((target, idx) => (
            target !== 'Not Reported' && (
              <Badge key={idx} variant="phase">Phase {target}</Badge>
            )
          ))}
          {/* Secondary Tags */}
          <Badge variant="status">{paper.design}</Badge>
          <Badge variant="disease">{paper.disease}</Badge>
        </div>

        {/* Key Findings */}
        <div className="pt-4 border-t-[0.5px] border-[#EAEAEA]">
          <div className="text-sm font-normal text-gray-1 mb-2.5">
            Key Points
          </div>
          <ul className="space-y-2 list-disc pl-5 marker:text-gray-1">
            {paper.summary.map((point, idx) => (
              <li key={idx} className="text-sm font-normal text-gray-1">
                {point}
              </li>
            ))}
          </ul>
        </div>

        {/* Footer */}
        <div className="mt-4 pt-3 border-t border-base-200">
          <div className="text-xs text-base-content/40 text-center">
            {paper.study_type}
          </div>
        </div>
      </div>

      {shouldShowViewAll && !isExpanded && (
        <div className="absolute bottom-5 mx-5 left-0 right-0">
          <div className="h-2 w-full bg-gradient-to-t from-white via-white to-transparent"></div>
          <div 
            className="flex items-center justify-end py-0.5 bg-white"
            onClick={handleViewAllClick}
          >
            <div className="text-sm text-gray-1 mr-0.5">{commonI18n.VIEW_ALL[lang]}</div>
            <ChevronDown className="h-4 w-4" />
          </div>
        </div>
      )}
    </motion.div>
  );
};

export default PaperCard;
