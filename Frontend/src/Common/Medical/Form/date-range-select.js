import { DatePicker } from 'antd';
import dayjs from 'dayjs';

import { useLanguage } from '../../../utils/context/lang';
import { i18n as formI18n } from '../../i18n/form';

const dateFormat = 'YYYY-MM-DD';

const DateRangeSelect = ({ onChange, value = [] }) => {
  const { lang } = useLanguage();
  // if no value, we don't set defaultValue
  if (!value || value.length === 0) {
    return (
      <div>
        <DatePicker.RangePicker
          placeholder={[ formI18n.DATE_RANGE[lang].past, formI18n.DATE_RANGE[lang].tillNow ]}
          allowEmpty={[ true, true ]}
          onChange={(date, dateString) => {
            onChange?.(date, dateString);
          }}
        />
      </div>
    );
  } else {
    const [ startDate, endDate ] = value;
    return (
      <div>
        <DatePicker.RangePicker
          placeholder={[ 'Past', 'Till Now' ]}
          allowEmpty={[ true, true ]}
          onChange={(date, dateString) => {
            onChange?.(date, dateString);
          }}
          defaultValue={[
            dayjs(startDate, dateFormat),
            dayjs(endDate, dateFormat),
          ]}
          format={'YYYY-MM-DD'}
        />
      </div>
    );
  }
};

export default DateRangeSelect;
