import { Select } from 'antd';

import { useLanguage } from '../../../utils/context/lang';
import { i18n as formI18n } from '../../i18n/form';

import AndOrInput from './common/and-or-select';

const RouteInput = ({ name, value, onChange }) => {
  const ROUTES = [ 'Intraarterial', 'Intraurethral', 'Inhaled', 'Intranasal', 'Subcutaneous (SQ) - Unspecified', 'Transdermal', 'Intraocular/Subretinal/Subconjunctival', 'Subcutaneous (SQ) Injection', 'Intrauterine', 'Intralymphatic', 'Intradiscal', 'Intra-amniotic', 'Intrathecal', 'Intracerebral/cerebroventricular', 'Intramuscular (IM)', 'Intraarticular', 'Intracochlear', 'Surgical Implantation', 'Hemoperfusion', 'Subcutaneous (SQ) Infusion', 'Intravitreal', 'Intravenous (IV)', 'Oral (PO)', 'Intradermal', 'Percutaneous Catheter/Injection', 'Intranodal', 'Intravesical', 'Intracameral', 'Intratympanic', 'Sublingual (SL)/Oral Transmucosal', 'Intratumoral', 'Intravaginal', 'N/A', 'Rectal', 'Intracavitary', 'Intra-Cisterna Magna (ICM) Injection', 'Injectable - Unspecified', 'Intratracheal', 'Topical', 'Instillation', 'Intraintestinal', 'Submucosal' ].map((item)=>({ label: item, value: item }));
  const { lang } = useLanguage();
  return (
    <>
      <AndOrInput
        name={name}
        value={value}
        onChange={onChange}
        options={ROUTES}
        placeholder={formI18n.ROUTE[lang].placeholder}
        filterOption={(input, option) =>
          (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
        }
      />

      {/* <Select
        mode="multiple"
        style={{
          width: '100%',
        }}
        placeholder={formI18n.ROUTE[lang].placeholder}
        value={value}
        onChange={(value) => onChange(value)}
        notFoundContent={null}
        options={ROUTES.map((filter) => ({
          label: filter,
          value: filter,
        }))}
        maxTagCount="responsive"
      /> */}
    </>
  );
};

export default RouteInput;
