import { Select } from 'antd';

import { useLanguage } from '../../../utils/context/lang';
import { i18n as formI18n } from '../../i18n/form';

import AndOrInput from './common/and-or-select';

const LocationSelect = ({ name, value, onChange }) => {
  const options = [ 'Georgia', 'Peru', 'Nicaragua', 'Norway', 'Tunisia', 'Panama', 'Dominican Republic', 'El Salvador', 'Egypt', 'Iceland', 'Finland', 'Jamaica', 'Denmark', 'Montserrat', 'Canada', 'Malaysia', 'Mongolia', 'Oman', 'Honduras', 'Italy', 'Virgin Islands (U.S.)', 'Bolivia', 'France', 'Philippines', 'Tanzania', 'Israel', 'Ukraine', 'Kenya', 'Cyprus', 'Bulgaria', 'United Arab Emirates', 'Lebanon', 'Bangladesh', 'Brazil', 'Nigeria', 'Grenada', 'Macedonia, The Former Yugoslav Republic of', 'Croatia', 'Bosnia and Herzegovina', 'Zambia', 'Belarus', 'Hong Kong', 'Ireland', 'Indonesia', 'Sri Lanka', 'Puerto Rico', 'Nepal', 'Botswana', 'Mauritius', 'Sierra Leone', 'Turkey', 'Russian Federation', 'South Africa', 'Portugal', 'Former Serbia and Montenegro', 'New Zealand', 'Estonia', 'Australia', 'Ethiopia', 'Mexico', 'Swaziland', 'Malawi', 'Korea, Republic of', 'Belgium', 'Morocco', 'Ecuador', 'Singapore', 'Bahamas', 'Uganda', 'Netherlands Antilles', 'Sweden', 'Malta', 'Montenegro', 'Costa Rica', 'Greece', 'Romania', 'China', 'Zimbabwe', 'Monaco', 'Pakistan', 'Lithuania', 'Serbia', 'Vietnam', 'Venezuela', 'United States', 'Luxembourg', 'Albania', 'Colombia', 'Uruguay', 'Moldova, Republic of', 'Czech Republic', 'Liberia', 'Poland', 'Netherlands', 'Kuwait', 'Jordan', 'Kazakhstan', 'Congo, The Democratic Republic of the', 'Austria', 'Slovakia', 'Thailand', 'Switzerland', 'North Macedonia', 'Taiwan', 'Algeria', 'Cambodia', 'Hungary', 'Chile', 'Argentina', 'Paraguay', 'India', 'Andorra', 'United Kingdom', 'Germany', 'Qatar', 'Czechia', 'Spain', 'Cuba', 'Ghana', 'Guatemala', 'Slovenia', 'Latvia', 'Armenia', 'Japan', 'Saudi Arabia', 'Guinea' ]
    .sort().map((item)=>({ label: item, value: item }));

  const { lang } = useLanguage();
  return (
    <>
      <AndOrInput
        name={name}
        onChange={onChange} // Use the memoized handleChange here
        value={value}
        placeholder={formI18n.LOCATION[lang].placeholder}
        options={options}
        filterOption={(input, option) =>
          (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
        }
      />
      {/* <Select
        mode="multiple"
        style={{
          width: '100%',
        }}
        placeholder={'Select Location'}
        value={value}
        onChange={(value) => onChange(value)}
        notFoundContent={null}
        options={options}
      /> */}
    </>
  );
};

export default LocationSelect;