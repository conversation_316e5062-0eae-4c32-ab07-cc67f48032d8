import { useCallback, useState } from 'react';

import { Select } from 'antd';
import debounce from 'lodash/debounce';

import { fetchTypeAhead } from '../../../api';
import { ReactComponent as ChevronDown } from '../../../assets/arrow-down.svg';
import { useLanguage } from '../../../utils/context/lang';
import { i18n as formI18n } from '../../i18n/form';

const CompanySelect = ({ name, onChange, value }) => {
  const { lang } = useLanguage();
  const [ options, setOptions ] = useState([]);
  const handleSearch = useCallback(debounce((value) => {
    fetchTypeAhead(value, 'company').then((res) => {
      setOptions(res?.result?.map((item) => ({ label: item, value: item })) || []);
    })
  }, 500), []);
  return (
    <>
      <Select
        mode="multiple"
        style={{
          width: '100%',
        }}
        placeholder={formI18n.COMPANY[lang].placeholder}
        value={value}
        onChange={(value) => onChange(value)}
        notFoundContent={null}
        options={options}
        filterOption={false}
        onSearch={handleSearch}
        suffixIcon={
          <ChevronDown className="h-4 w-4" />
        }
        // maxTagCount="responsive"
      />
    </>
  );
};

export default CompanySelect;
