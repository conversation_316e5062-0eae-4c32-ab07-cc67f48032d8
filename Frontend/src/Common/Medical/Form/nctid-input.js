import { useLanguage } from '../../../utils/context/lang';
import { i18n as formI18n } from '../../i18n/form';

import MultiInput from './common/multi-input';

const NctIdInput = ({ name, onChange, value = [] }) => {
  const { lang } = useLanguage();
  return (
    <>
      <div className="flex flex-wrap gap-2">
        <MultiInput
          name={name}
          placeholder={formI18n.NCTID[lang].placeholder}
          value={value}
          tokenSeparators={[ ',', ' ' ]}
          onChange={(value) => onChange(value)}
        />
      </div>
    </>
  );
};

export default NctIdInput;
