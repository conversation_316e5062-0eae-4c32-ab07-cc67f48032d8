import { Select } from 'antd';

import { useLanguage } from '../../../utils/context/lang';
import { i18n as formI18n } from '../../i18n/form';

const StudyTypeSelect = ({ name, value = [], onChange }) => {
  const { lang } = useLanguage();
  const STATUS = [ 'EXPANDED_ACCESS', 'INTERVENTIONAL', 'OBSERVATIONAL' ];
  return (
    <>
      <Select
        mode="multiple"
        style={{
          width: '100%',
        }}
        placeholder={formI18n.STUDY_TYPE[lang].placeholder}
        value={value}
        onChange={(value) => onChange(value)}
        notFoundContent={null}
        options={STATUS.map((filter) => ({
          label: filter,
          value: filter,
        }))}
        maxTagCount="responsive"
      />
    </>
  );
};

export default StudyTypeSelect;
