import { useCallback, useEffect, useState } from 'react';

import debounce from 'lodash/debounce';

import { fetchTypeAhead } from '../../../api';
import { useLanguage } from '../../../utils/context/lang';
import { i18n as formI18n } from '../../i18n/form';

import AndOrSelect from './common/and-or-select';

const DiseaseInput = ({ name, onChange, value }) => {
  const [ options, setOptions ] = useState([]);
  const { lang } = useLanguage();
  useEffect(() => {
    fetchTypeAhead(value || '', 'citeline_target').then((res) => {
      setOptions(res?.result?.map((item) => ({ label: item, value: item })) || []);
    })
  }, []);
  const handleSearch = useCallback(debounce((value) => {
    fetchTypeAhead(value, 'citeline_target').then((res) => {
      setOptions(res?.result?.map((item) => ({ label: item, value: item })) || []);
    })
  }, 500), []);
  return (
    <>
      <div className="flex flex-wrap gap-2">
        <AndOrSelect
          name={name}
          onChange={onChange} // Use the memoized handleChange here
          value={value}
          placeholder={formI18n.TARGET[lang].placeholder}
          options={options}
          filterOption={false}
          onSearch={handleSearch}
        />
      </div>
    </>
  );
};

export default DiseaseInput;
