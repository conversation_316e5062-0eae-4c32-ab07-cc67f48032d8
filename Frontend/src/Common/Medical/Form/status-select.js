import { Select } from 'antd';

import { useLanguage } from '../../../utils/context/lang';
import { i18n as formI18n } from '../../i18n/form';

const StatusSelect = ({ name, value = [], onChange }) => {
  const { lang } = useLanguage();
  const STATUS = [ 'Announced', 'Enrolled', 'Interim Data', 'Suspended', 'Initiated', 'Final Data', 'Completed' ];
  return (
    <>
      <Select
        mode="multiple"
        style={{
          width: '100%',
        }}
        placeholder={formI18n.STATUS[lang].placeholder}
        value={value}
        onChange={(value) => onChange(value)}
        notFoundContent={null}
        options={STATUS.map((filter) => ({
          label: filter,
          value: filter,
        }))}
        maxTagCount="responsive"
      />
    </>
  );
};

export default StatusSelect;
