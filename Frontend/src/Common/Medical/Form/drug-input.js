import { useCallback, useState } from 'react';

import debounce from 'lodash/debounce';
import { use } from 'markdown-it-texmath';

import { fetchTypeAhead } from '../../../api';
import { useLanguage } from '../../../utils/context/lang';
import { i18n as formI18n } from '../../i18n/form';

import AndOrInput from './common/and-or-select';


const dict = {
  en: {
    title: 'Drug',
    placeholder: 'Drug name like Pembrolizumab',
  },
  zh: {
    title: '药物',
    placeholder: '药物名称，如 Pembrolizumab',
  },
};
const DrugInput = ({ name, onChange, value }) => {
  const { lang } = useLanguage();
  const [ options, setOptions ] = useState([]);
  const handleSearch = useCallback(debounce((value) => {
    fetchTypeAhead(value, 'drug').then((res) => {
      setOptions(res?.result?.map((item) => ({ label: item, value: item })) || []);
    })
  }, 500), []);
  return (
    <>
      <div className="flex flex-wrap gap-2">
        <AndOrInput
          name={name}
          onChange={onChange}
          value={value}
          placeholder={formI18n.DRUG[lang].placeholder}
          options={options}
          filterOption={false}
          onSearch={handleSearch}
          // mode='tags'
        />
        {/* <MultiInput
          name={name}
          placeholder={'Drug name like Pembrolizumab'}
          value={value}
          tokenSeparators={[ ',' ]}
          onChange={(value) => onChange(value)}
        /> */}
      </div>
    </>
  );
};

export default DrugInput;