import { useCallback, useEffect, useState } from 'react';

import { Select } from 'antd';
import debounce from 'lodash/debounce';

import { fetchTypeAhead } from '../../../api';
import { ReactComponent as ChevronDown } from '../../../assets/arrow-down.svg';
import { useLanguage } from '../../../utils/context/lang';
import { i18n as formI18n } from '../../i18n/form';



const LocationInput = ({ name, onChange, value }) => {
  const { lang } = useLanguage();
  const [ options, setOptions ] = useState([]);
  useEffect(() => {
    fetchTypeAhead(value||'', 'location').then((res) => {
      setOptions(res?.result?.map((item) => ({ label: item, value: item })) || []);
    })
  }, []);
  const handleSearch = useCallback(debounce((value) => {
    fetchTypeAhead(value, 'location').then((res) => {
      setOptions(res?.result?.map((item) => ({ label: item, value: item })) || []);
    })
  }, 500), []);
  // return (
  //   <>
  //     <div className="flex flex-wrap gap-2">
  //       <AndOrSelect
  //         name={name}
  //         onChange={onChange} // Use the memoized handleChange here
  //         value={value}
  //         placeholder={dict['en'].placeholder}
  //         options={options}
  //         filterOption={false}
  //         onSearch={handleSearch}
  //       />
  //     </div>
  //   </>
  // );
  return (
    <Select
      mode="multiple"
      style={{ width: '100%' }}
      tokenSeparators={[ ',' ]}
      placeholder={formI18n.LOCATION[lang].placeholder}
      notFoundContent={null}
      value={value}
      onChange={onChange}
      options={options}
      onSearch={handleSearch}
      suffixIcon={
        <ChevronDown className="h-4 w-4" />
      }
      // maxTagCount="responsive"
    />
  )
};

export default LocationInput;
