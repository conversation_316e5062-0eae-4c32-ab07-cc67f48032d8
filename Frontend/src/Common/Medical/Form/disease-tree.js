import { useEffect, useState } from 'react';

// import { Cascader } from 'antd';
import { TreeSelect } from 'antd';

import { ReactComponent as ChevronDown } from '../../../assets/arrow-down.svg';
import { useLanguage } from '../../../utils/context/lang';
import { i18n as formI18n } from '../../i18n/form';

// import json file
import diseaseData from './disease-data.json';

const DiseaseTree = ({ name, value, onChange }) => {
  const { lang } = useLanguage();
  const [ selectList, setSelectList ] = useState(value || []);

  // The value of the Cascader must be a complete array, so data preservation needs to be done here.
  useEffect(() => {
    onChange(value);
    // Clear selectList when switching workflowId.
    if (!value || value?.length === 0) {
      setSelectList([]);
    }
  }, [ value ]); // no onChange

  useEffect(() => {
    // Initialize selectList on load and clear selectList on unload.
    setSelectList(value || []);
    return () => setSelectList([]);
  }, []);

  const filter = (inputValue, path) => {
    return path.some((option) => option.label.toLowerCase().indexOf(inputValue.toLowerCase()) > -1);
  }

  return (
    <>
      <TreeSelect
        placeholder={formI18n.DISEASE[lang].placeholder}
        style={{ width: '100%' }}
        treeData={diseaseData}
        showSearch={{ filter }}
        value={selectList}
        onChange={(selectedValue) => {          
          setSelectList(selectedValue);
          // Retrieve the original label text as the value
          const result = selectedValue.map(val => val.split(':')[1] || val.split(':')[0]);
          onChange(result);
        }}
        multiple
        changeOnSelect
        suffixIcon={<ChevronDown className="h-4 w-4" />}
      />
    </>
  );
};

export default DiseaseTree;

