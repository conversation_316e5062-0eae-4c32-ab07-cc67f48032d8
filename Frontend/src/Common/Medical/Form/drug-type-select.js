import { Select } from 'antd';

import { useLanguage } from '../../../utils/context/lang';
import { i18n as formI18n } from '../../i18n/form';

const DrugTypeSelect = ({ name, value, onChange }) => {
  const options = [ 'Immuno-Oncology', 'New Molecular Entity (NME)', '505b2', 'Steroids', 'Vaccine', 'Antisense RNA', 'Small Molecule Drugs', 'Fixed-Dose Combination', 'Antibody-Drug Conjugates, ADCs', 'Large Molecules', 'Unknown', 'Biologic', 'Precision Medicine', 'Protein Degrader', 'Monoclonal Antibodies', 'mRNA', 'Bacterial Product', 'Others', 'Cell-based Therapies', 'Nucleic Acid-based', 'Gene Therapy', 'miRNA', 'N/A', 'Polypeptide', 'Device', 'Recombinant Proteins', 'Specialty Drug', 'Non-NME', 'Small Molecule', 'Reformulation', 'siRNA/RNAi', 'Trispecific Antibodies', 'Polyclonal Antibodies', 'Bi-specific Antibodies', 'Viral', 'Glycoconjugates', 'Radiopharmaceutical', 'Biosimilar' ]
    .sort().map((item)=>({ label: item, value: item }));
  const { lang } = useLanguage();

  return (
    <>
      {/* <AndOrInput
        name={name}
        onChange={onChange} // Use the memoized handleChange here
        value={value}
        placeholder={'Target name like Target 1'}
        options={options}
      /> */}

      <Select
        mode="multiple"
        tokenSeparators={[ ',' ]}
        style={{
          width: '100%',
        }}
        placeholder={formI18n.DRUG_TYPE[lang].placeholder}
        notFoundContent={null}
        value={value?.data}
        onChange={(value)=>onChange({ data:value, logic:'or' })}
        // onChange={(value)=>onChange(value)}
        options={options}
        // maxTagCount="responsive"
      />
  
      {/* <Select
        mode="multiple"
        style={{
          width: '100%',
        }}
        placeholder={'Select Location'}
        value={value}
        onChange={(value) => onChange(value)}
        notFoundContent={null}
        options={options}
      /> */}
    </>
  );
};

export default DrugTypeSelect;