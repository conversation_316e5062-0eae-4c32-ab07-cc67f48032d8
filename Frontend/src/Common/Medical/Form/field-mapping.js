import { lazy } from 'react';

// Lazy load components
const NctIdInput = lazy(() => import('./nctid-input'));
const DrugInput = lazy(() => import('./drug-input'));
const CompanySelect = lazy(() => import('./company-select'));
const DiseaseInput = lazy(() => import('./disease-tree'));
const TargetInput = lazy(() => import('./target-input'));
const LocationSelect = lazy(() => import('./location-select'));
const LocationSelectCompete = lazy(() => import('./location-input-compete'));
const GenderSelect = lazy(() => import('./gender-select'));
const StudyTypeSelect = lazy(() => import('./study-type-select'));
const StatusSelect = lazy(() => import('./status-select'));
const PhaseSelect = lazy(() => import('./phase-select'));
const PhaseSelectCompete = lazy(() => import('./phase-select-compete'));
const RouteInput = lazy(() => import('./route-input'));
const DrugTypeSelect = lazy(() => import('./drug-type-select'));
const DrugFeatureSelect = lazy(() => import('./drug-feature-select'));
const DrugModalityTree = lazy(() => import('./drug-modality-tree'));
const DrugModalityTreeCiteline = lazy(() => import('./drug-modality-tree-citeline'));
const MiscInput = lazy(() => import('./misc-input'));
const DateRangeSelect = lazy(() => import('./date-range-select'));
const ImpactInput = lazy(() => import('./impact-input'));
const PhaseSelectCatalyst = lazy(() => import('./phase-select-catalyst'));
const CatalystTypeSelect = lazy(() => import('./catalyst-type-input'));
const ClinicalGuidelineDiseaseInput = lazy(() =>
  import('./clinical-guideline-disease-select')
);
const ClinicalGuidelineAssociationInput = lazy(() =>
  import('./clinical-guideline-association-select')
);
const DiseaseInputCiteline = lazy(() => import('./disease-tree-citeline'));
const TargetInputCiteline = lazy(() => import('./target-input-citeline'));
const CompanySelectCiteline = lazy(() => import('./company-select-citeline'));
const DrugInputCiteline = lazy(() => import('./drug-input-citeline'));

export const FIELD_COMPONENTS = {
  NctIdInput,
  DrugInput,
  CompanySelect,
  DiseaseInput,
  TargetInput,
  LocationSelect,
  GenderSelect,
  StudyTypeSelect,
  StatusSelect,
  PhaseSelect,
  PhaseSelectCompete,
  RouteInput,
  DrugTypeSelect,
  MiscInput,
  DrugFeatureSelect,
  DrugModalityTree,
  DateRangeSelect,
  ImpactInput,
  PhaseSelectCatalyst,
  CatalystTypeSelect,
  ClinicalGuidelineDiseaseInput,
  ClinicalGuidelineAssociationInput,
  DiseaseInputCiteline,
  TargetInputCiteline,
  CompanySelectCiteline,
  DrugInputCiteline,
  LocationSelectCompete,
  DrugModalityTreeCiteline,
};
