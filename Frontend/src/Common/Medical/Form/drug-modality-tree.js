import { useLanguage } from '../../../utils/context/lang';
import { i18n as formI18n } from '../../i18n/form';

import AndOrCascader from './common/and-or-cascader';
// import json file
const modalityData = [
  {
    'label': 'Small Molecule Drugs',
    'value': 'Small Molecule Drugs',
    'children': [
      {
        'label': 'Carbohydrates',
        'value': 'Carbohydrates'
      },
      {
        'label': 'Steroids',
        'value': 'Steroids'
      },
      {
        'label': 'Protein Degrader',
        'value': 'Protein Degrader'
      },
      {
        'label': 'Glycoconjugates',
        'value': 'Glycoconjugates'
      },
      {
        'label': 'Small Molecule',
        'value': 'Small Molecule'
      }
    ]
  },
  {
    'label': 'Large Molecules',
    'value': 'Large Molecules',
    'children': [
      {
        'label': 'Monoclonal Antibodies',
        'value': 'Monoclonal Antibodies'
      },
      {
        'label': 'Bi-specific Antibodies',
        'value': 'Bi-specific Antibodies'
      },
      {
        'label': 'Trispecific Antibodies',
        'value': 'Trispecific Antibodies'
      },
      {
        'label': 'Polyclonal Antibodies',
        'value': 'Polyclonal Antibodies'
      },
      {
        'label': 'Antibody-Drug Conjugates, ADCs',
        'value': 'Antibody-Drug Conjugates, ADCs'
      },
      {
        'label': 'Recombinant Proteins',
        'value': 'Recombinant Proteins'
      }
    ]
  },
  {
    'label': 'Nucleic Acid-based',
    'value': 'Nucleic Acid-based',
    'children': [
      {
        'label': 'siRNA/RNAi',
        'value': 'siRNA/RNAi'
      },
      {
        'label': 'mRNA',
        'value': 'mRNA'
      },
      {
        'label': 'Antisense RNA',
        'value': 'Antisense RNA'
      },
      {
        'label': 'miRNA',
        'value': 'miRNA'
      },
      {
        'label': 'Others',
        'value': 'Others'
      }
    ]
  },
  {
    'label': 'Polypeptide',
    'value': 'Polypeptide',
  },
  {
    'label': 'Cell-based Therapies',
    'value': 'Cell-based Therapies',
  },
  {
    'label': 'Vaccine',
    'value': 'Vaccine',
  },
  {
    'label': 'Imaging Agents',
    'value': 'Imaging Agents',
  },
  {
    'label': 'Radiopharmaceutical',
    'value': 'Radiopharmaceutical',
  },
  {
    'label': 'Gene Therapy',
    'value': 'Gene Therapy',
  },
  {
    'label': 'Unknown',
    'value': 'Unknown',
  }
]

const DrugModalityTree = ({ name, value, onChange }) => {
  const { lang } = useLanguage();
  console.log('drug modality tree default', value)

  return (
    <>
      <AndOrCascader
        name={name}
        onChange={onChange}
        value={value}
        placeholder={formI18n.DRUG_MODALITY[lang].placeholder}
        options={modalityData}
      />
    </>
  );
};

export default DrugModalityTree;

