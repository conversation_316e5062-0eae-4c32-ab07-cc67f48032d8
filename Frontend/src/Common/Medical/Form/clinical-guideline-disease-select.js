import { useCallback, useEffect, useState } from 'react';

import { Select } from 'antd';
import debounce from 'lodash/debounce';

import { fetchTypeAhead } from '../../../api';
import { ReactComponent as ChevronDown } from '../../../assets/arrow-down.svg';
import { useLanguage } from '../../../utils/context/lang';
import { i18n as formI18n } from '../../i18n/form';

const ClincalGuidelineDiseaseSelect = ({ name, onChange, value }) => {
  const { lang } = useLanguage();
  const [ options, setOptions ] = useState([]);
  useEffect(() => {
    fetchTypeAhead(value || '', 'ClincalGuidelineDisease').then((res) => {
      setOptions(res?.result?.map((item) => ({ label: item, value: item })) || []);
    })
  }, []);
  const handleSearch = useCallback(debounce((value) => {
    fetchTypeAhead(value, 'ClincalGuidelineDisease').then((res) => {
      setOptions(res?.result?.map((item) => ({ label: item, value: item })) || []);
    })
  }, 500), []);
  return (
    <>
      <Select
        mode="multiple"
        style={{
          width: '100%',
        }}
        placeholder={formI18n.DISEASE[lang].placeholder}
        value={value}
        onChange={(value) => onChange(value)}
        notFoundContent={null}
        options={options}
        filterOption={false}
        onSearch={handleSearch}
        maxTagCount="responsive"
        suffixIcon={
          <ChevronDown className="h-4 w-4" />
        }
      />
    </>
  );
};

export default ClincalGuidelineDiseaseSelect;
