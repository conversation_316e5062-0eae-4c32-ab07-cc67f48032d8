import React from 'react';

import { Select } from 'antd';

import { ReactComponent as ChevronDown } from '../../../../assets/arrow-down.svg';

const MultiInput = ({ tokenSeparators, placeholder, value, onChange }) => {

  const handleChange = (value) => {
    onChange(value);
  };

  return (
    <Select
      style={{ width: '100%' }}
      tokenSeparators={tokenSeparators || [ ',' ]}
      mode="tags"
      notFoundContent={null}
      placeholder={placeholder}
      value={value ||[]}
      onChange={handleChange}
      maxTagCount="responsive"
      suffixIcon={
        <ChevronDown className="h-4 w-4" />
      }
    />
  );
};

export default MultiInput;
