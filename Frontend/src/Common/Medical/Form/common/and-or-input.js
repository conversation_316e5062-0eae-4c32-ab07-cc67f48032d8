import { useState } from 'react';

import { Select, Space } from 'antd';

export default function AndOrInput({ onChange, value = { data: [], logic: 'or' }, placeholder, options = [], onSearch, filterOption=false }) {
  const handleOperatorChange = (newOperator) => {
    onChange({ data: value.data, logic: newOperator });
  };

  const handleTagsChange = (newTags) => {
    onChange({ data: newTags, logic: value.logic });
  };

  return (
    <Space.Compact style={{ width: '100%' }}>
      <Select
        className="w-20"
        value={value.logic}
        options={[
          { value: 'and', label: 'AND' },
          { value: 'or', label: 'OR' },
          { value: 'not', label: 'NOT' }
        ]}
        onSelect={handleOperatorChange}
      />
      <Select
        mode="tags"
        tokenSeparators={[ ',' ]}
        style={{ width: '100%' }}
        placeholder={placeholder}
        onChange={handleTagsChange}
        notFoundContent={null}  
        options={options}
        value={value.data}
        onSearch={onSearch}
        filterOption={filterOption}
        maxTagCount="responsive"
      />
    </Space.Compact>
  );
}
