import { useState } from 'react';

import { Select, Space } from 'antd';

import { ReactComponent as ChevronDown } from '../../../../assets/arrow-down.svg';

export default function AndOrInput({ onChange, value = { data: [], logic: 'or' }, placeholder, options = [], onSearch, filterOption=false, mode='multiple' }) {
  const handleOperatorChange = (newOperator) => {
    onChange({ data: value.data, logic: newOperator });
  };

  const handleTagsChange = (newTags) => {
    onChange({ data: newTags, logic: value.logic });
  };

  return (
    <div className="relative" style={{ width: '100%' }}>
      <Select
        className="and-or-select-prefix"
        dropdownStyle={{ 
          padding: '2px',
          width: 'fit',
        }}
        value={value.logic}
        options={[
          { value: 'and', label: 'AND', className: 'and-or-select-prefix-dropdown-item' },
          { value: 'or', label: 'OR', className: 'and-or-select-prefix-dropdown-item' },
          { value: 'not', label: 'NOT', className: 'and-or-select-prefix-dropdown-item' }
        ]}
        suffixIcon={null}
        onSelect={handleOperatorChange}
      />
      <Select
        mode={mode}
        tokenSeparators={[ ',' ]}
        style={{ width: '100%' }}
        className="and-or-select"
        placeholder={placeholder}
        onChange={handleTagsChange}
        notFoundContent={null}
        options={options}
        value={value.data}
        onSearch={onSearch}
        filterOption={filterOption}
        defaultActiveFirstOption={false}
        suffixIcon={
          <ChevronDown className="h-4 w-4" />
        }
        {...(mode === 'tags' ? { maxTagCount: 'responsive' } : {})}
      />
    </div>
  );
}
