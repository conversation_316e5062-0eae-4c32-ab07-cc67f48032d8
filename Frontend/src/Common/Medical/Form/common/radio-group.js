import React, { useState } from 'react';

import { Radio, Space } from 'antd';

import AndOrInput from './and-or-select'; 
import MultiInput from './multi-input';

const RadioGroupComponent = ({ contents, onChange }) => {
  const [ selectedOption, setSelectedOption ] = useState(contents[0].name);

  const handleRadioChange = (e) => {
    setSelectedOption(e.target.value);
  };

  const renderInputComponent = (content) => {
    switch (content.type) {
    case 'multi_input':
      return (
        <MultiInput
          name={content.name}
          onChange={onChange}
          placeholder={content.placeholder}
          value={[]} // pass the default value if available
        />
      );
    case 'and_or_input':
      return (
        <AndOrInput
          name={content.name}
          onChange={onChange}
          placeholder={content.placeholder}
          mode='tags'
        />
      );
    default:
      return null;
    }
  };

  const selectedContent = contents.find(content => content.name === selectedOption);

  return (
    <div>
      <Radio.Group onChange={handleRadioChange} value={selectedOption}>
        {contents.map(content => (
          <Radio key={content.name} value={content.name}>
            {content.display}
          </Radio>
        ))}
      </Radio.Group>
      <div style={{ marginTop: 16 }}>
        {selectedContent && renderInputComponent(selectedContent)}
      </div>
    </div>
  );
};

export default RadioGroupComponent;
