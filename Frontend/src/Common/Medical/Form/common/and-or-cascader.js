import { useEffect, useState } from 'react';

import { <PERSON>r, Select, Space } from 'antd';

import { ReactComponent as ChevronDown } from '../../../../assets/arrow-down.svg';
import { i18n as formI18n } from '../../../i18n/form';

export default function AndOrCascader({ 
  value = { data: [], logic: 'or' }, 
  onChange,
  placeholder = formI18n.DRUG_MODALITY.en.placeholder,
  options = []
}) {
  const [ selectList, setSelectList ] = useState(value?.data || []);

  useEffect(() => {
    // Clear selectList when switching workflowId.
    onChange(value);
    if (!value || value?.data?.length === 0) {
      setSelectList([]);
    }
  }, [ value ]);

  useEffect(() => {
    // Initialize selectList on load and clear selectList on unload.
    setSelectList(value?.data.map(item=>[ item ]) || []);  // for appearance, we need to set the selectList as an array of arrays
    return () => setSelectList([]);
  }, []);

  const handleOperatorChange = (newOperator) => {
    onChange({ data: value.data, logic: newOperator });
  };

  const handleCascaderChange = (selectedValue) => {
    setSelectList(selectedValue);
    const result = { 
      data: selectedValue.map(item => item.at(-1)), 
      logic: value.logic 
    };
    onChange(result);
  };

  const filter = (inputValue, path) => {
    return path.some((option) => 
      option.label.toLowerCase().indexOf(inputValue.toLowerCase()) > -1
    );
  };

  return (
    <div className="relative" style={{ width: '100%' }}>
      <Select
        className="and-or-select-prefix"
        dropdownStyle={{ 
          padding: '2px',
          width: 'fit',
        }}
        value={value.logic}
        options={[
          { value: 'and', label: 'AND', className: 'and-or-select-prefix-dropdown-item' },
          { value: 'or', label: 'OR', className: 'and-or-select-prefix-dropdown-item' },
          { value: 'not', label: 'NOT', className: 'and-or-select-prefix-dropdown-item' }
        ]}
        suffixIcon={null}
        onSelect={handleOperatorChange}
      />
      <Cascader
        className="and-or-select"
        style={{ width: '100%' }}
        options={options}
        showSearch={{ filter }}
        value={selectList}
        onChange={handleCascaderChange}
        multiple
        suffixIcon={
          <ChevronDown className="h-4 w-4" />
        }
        maxTagCount="responsive"
        placeholder={placeholder}
      />
    </div>
  );
}
