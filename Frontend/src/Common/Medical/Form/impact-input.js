import { Select } from 'antd';

import { useLanguage } from '../../../utils/context/lang';
import { i18n as formI18n } from '../../i18n/form';

const PhaseSelectCatalyst = ({ name, value = [], onChange }) => {
  const { lang } = useLanguage();
  const PHASES = [
    'Other',
    'Large',
  ];

  return (
    <>
      <Select
        mode="multiple"
        style={{
          width: '100%',
        }}
        placeholder={formI18n.IMPACT[lang].placeholder}
        value={value}
        onChange={(value) => onChange(value)}
        notFoundContent={null}
        options={PHASES.map((filter) => ({
          label: filter,
          value: filter,
        }))}
        maxTagCount="responsive"
      />
    </>
  );
};

export default PhaseSelectCatalyst;
