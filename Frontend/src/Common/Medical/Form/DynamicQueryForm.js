import React, { Suspense, useEffect, useRef, useState } from 'react';

import { useParams } from 'react-router-dom';

import { ReactComponent as CloseIcon } from '../../../assets/close.svg';
import { useLanguage } from '../../../utils/context/lang';
import { i18n as chatI18n } from '../../i18n/chat';

import { FIELD_COMPONENTS } from './field-mapping';

const DynamicQueryForm = ({ fields, values, isQueryFormExpanded = true, onChange, onSearch, onClose }) => {
  const [ localValues, setLocalValues ] = useState(values);
  const { lang } = useLanguage();
  const contentRef = useRef(null);

  // useEffect(() => {
  //   if (contentRef.current && !isExpanded) {      
  //     const height = contentRef.current.scrollHeight;
  //     setShouldShowViewAll(height > 296);
  //   }
  // }, [ contentRef?.current?.scrollHeight ]);
  
  const handleSearch = (e) => {
    e.preventDefault();
    window.scrollTo(0, 0);
    onSearch(localValues);
    onClose?.();
  };

  const handleFieldChange = (name, value) => {
    setLocalValues(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleClose = () => {
    onClose?.();
  };

  useEffect(() => {
    setLocalValues(values);
  }, [ values ]);

  // Function to get active filter count and summary
  const getActiveFilters = () => {
    const activeFilters = Object.entries(localValues)
      .filter(([ _, value ]) => {
        if (Array.isArray(value)) return value.length > 0;
        if (typeof value === 'object' && value !== null && 'data' in value) {
          return value.data.length > 0;
        }
        return value !== undefined && value !== null && value !== '';
      });
    if (activeFilters.length === 0) return null;

    // Get field display names from the fields prop
    const fieldMap = Object.fromEntries(fields.map(f => [ f.name, f.display ]));
    
    // Get first three filters for preview
    const preview = activeFilters
      .slice(0, 3)
      .map(([ key, value ]) => {
        const displayName = fieldMap[key];
        const displayValue = Array.isArray(value) 
          ? `${value.length} ${chatI18n.SELECTED_COUNT[lang]}` 
          : typeof value === 'object' && value !== null && 'data' in value ? `${value.data.length} ${chatI18n.SELECTED_COUNT[lang]}` : `${value}` ;
        return `${displayName}: ${displayValue}`;
      })
      .join(' • ');

    return {
      count: activeFilters.length,
      preview: preview + (activeFilters.length > 3 ? ` +${activeFilters.length - 3} ${chatI18n.MORE[lang]}` : '')
    };
  };

  const activeFilters = getActiveFilters();

  const getCountText = () => {
    return typeof chatI18n.SELECTED_CONDITIONS[lang] === 'function' 
      ? chatI18n.SELECTED_CONDITIONS[lang](activeFilters?.count || 0)
      : chatI18n.SELECTED_CONDITIONS[lang].replace('{count}', activeFilters?.count || 0)
  };

  return (
    <div
      className={`fixed top-0 left-0 h-screen w-screen z-[100] bg-[#00000080] overflow-hidden flex flex-col justify-center items-center
      transition-opacity duration-300 ${isQueryFormExpanded ? 'opacity-100' : 'opacity-0 pointer-events-none'}`}
      onClick={handleClose}
    >
      <form 
        onClick={(e) => e.stopPropagation()} 
        onSubmit={handleSearch} 
        className={`w-[calc(100%-32px)] max-w-4xl max-h-[calc(100vh-64px)] bg-white overflow-hidden flex flex-col rounded-2xl
        transition-transform duration-300 ${isQueryFormExpanded ? 'scale-100' : 'scale-95'}`}
      >
        <div
          className="flex flex-wrap items-center justify-between gap-4 p-5"
        >
          {/* Title */}
          <div className="flex-1 min-w-[200px] pr-8">
            <h1 className="text-xl font-bold tracking-tight text-base-content leading-none py-0.5">
              {chatI18n.SELECT_SEARCH_CRITERIA[lang]}
            </h1>
          </div>
          <button
            onClick={handleClose}
            className="p-1 hover:bg-base-200 rounded-lg transition-colors duration-200"
          >
            <CloseIcon className="w-4 h-4" />
          </button>
        </div>

        <div
          className="relative overflow-hidden transition-all duration-300 ease-in-out grid flex-1"
        >
          {/* Filter Fields Container */}
          <div 
            id="filter-panel"
            className="overflow-y-auto transition-all duration-300 ease-in-out"
          >
            {/* Scrollable content area */}
            <div ref={contentRef} className="p-5 pt-0 @container">
              <div className="grid grid-cols-1 gap-x-6 gap-y-4 mx-auto">
                {fields.map((field) => {
                  const FieldComponent = FIELD_COMPONENTS[field.component];
                      
                  if (!FieldComponent) {
                    console.warn(`No component found for ${field.component}`);
                    return null;
                  }

                  return (
                    <div key={field.name} className="relative space-y-1 group">
                      <label 
                        htmlFor={`field-${field.name}`}
                        className="text-sm font-medium text-black-2 transition-all duration-200 group-focus-within:text-noah-theme"
                      >
                        {field.display}
                      </label>
                      <Suspense fallback={
                        <div className="h-8 animate-pulse bg-cream rounded-lg" />
                      }>
                        <FieldComponent
                          id={`field-${field.name}`}
                          value={localValues[field.name]}
                          onChange={(value) => handleFieldChange(field.name, value)}
                          placeholder={`${chatI18n.FILTER_BY[lang]} ${field.display.toLowerCase()}`}
                          className="w-full bg-white border border-gray-200 rounded-lg 
                                px-3 py-2.5 text-sm placeholder:text-gray-400
                                focus:ring-1 focus:ring-blue-500/30 focus:border-blue-500 
                                transition-all duration-200 hover:border-gray-300
                                shadow-sm"
                        />
                      </Suspense>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

        </div>

        <div className="flex p-5 border-t border-[#eeeeee] items-center justify-end gap-2">
          {/* <div className="text-black-1 text-xs">{getCountText()}</div> */}
          <div className="flex items-center gap-2">
            <button
              type="button"
              onClick={() => {
                setLocalValues({});
                onChange({});
              }}
              className="px-3.5 py-1.5 text-sm font-semibold text-black-2
                        bg-gray-3 rounded-lg
                        transition-all duration-200 text-center"
            >
              {chatI18n.CLEAR_ALL[lang]}
            </button>
            <button
              type="submit"
              className="px-3.5 py-1.5 text-sm font-semibold text-white 
                        bg-noah-theme rounded-lg
                        transition-all duration-200 text-center"
            >
              {chatI18n.SEARCH[lang]}
            </button>
          </div>
        </div>

        {/* {
          (shouldShowViewAll && !isExpanded) && <div className="w-full h-8 absolute bottom-0 left-0 z-10 bg-gradient-to-t from-white to-[#FFFFFF00]"></div>
        } */}
      </form>
    </div>
  );
};

export default DynamicQueryForm;