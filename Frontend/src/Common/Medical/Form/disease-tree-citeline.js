import { useEffect, useState } from 'react';

import { TreeSelect } from 'antd';

import { ReactComponent as ChevronDown } from '../../../assets/arrow-down.svg';
import { useLanguage } from '../../../utils/context/lang';
import { i18n as formI18n } from '../../i18n/form';

// import json file
import diseaseData from './disease-data-citeline.json';

const DiseaseTree = ({ name, value, onChange }) => {
  const { lang } = useLanguage();
  // The value of the TreeSelect must be a complete array, so data preservation needs to be done here.
  const [ selectList, setSelectList ] = useState(value || []);
  // Trigger onChange with the value only on mount or when value changes
  useEffect(() => {
    onChange(value);
    // Clear selectList when switching workflowId.
    if (!value || value?.length === 0) {
      setSelectList([]);
    }
  }, [ value ]); // no onChange

  useEffect(() => {
    // Initialize selectList on load and clear selectList on unload.
    setSelectList(value || []);
    return () => setSelectList([]);
  }, []);

  console.log('disease tree default', value)
  const filter = (inputValue, path) => {
    return path.some((option) => option.label.toLowerCase().indexOf(inputValue.toLowerCase()) > -1);
  }

  return (
    <>
      <TreeSelect
        placeholder={formI18n.DISEASE[lang].placeholder}
        style={{ width: '100%' }}
        treeData={diseaseData}
        showSearch={{ filter }}
        value={selectList}
        onChange={(selectedValue) => {
          setSelectList(selectedValue);
          const result = selectedValue;
          // The result only needs to include the last item of each entry.
          onChange(result);
        }}
        multiple
        changeOnSelect
        suffixIcon={
          <ChevronDown className="h-4 w-4" />
        }
        // maxTagCount="responsive"
      />
    </>
  );
};

export default DiseaseTree;

