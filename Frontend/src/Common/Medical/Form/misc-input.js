import { ConfigProvider, Input } from 'antd';
import debounce from 'lodash/debounce';

const MiscInput = ({ name, onChange, value = '', title, placeholder }) => {
  return (
    <>
      <div className="flex flex-wrap gap-2">
        {/* <ConfigProvider theme={{ hashed: false }}> */}
        <Input
          name={name}
          placeholder={placeholder || 'Misc Input'}
          value={value}
          onChange={(e) => onChange(e.target.value)}
        />
        {/* </ConfigProvider> */}
      </div>
    </>
  );
};

export default MiscInput;
