import { Select } from 'antd';

import { useLanguage } from '../../../utils/context/lang';
import { i18n as formI18n } from '../../i18n/form';

import AndOrInput from './common/and-or-select';

const DrugFeatureSelect = ({ name, value, onChange }) => {
  const options = [ 'Precision Medicine', 'Reformulation', 'Bacterial Product', '505b2', 'Immuno-Oncology', 'Biosimilar', 'Fixed-Dose Combination', 'Device', 'Non-NME', 'Specialty Drug', 'Viral', 'Biologic', 'New Molecular Entity (NME)' ]
    .sort().map((item)=>({ label: item, value: item }));
  const { lang } = useLanguage();

  return (
    <>
      <AndOrInput
        name={name}
        onChange={onChange} // Use the memoized handleChange here
        value={value}
        placeholder={formI18n.DRUG_FEATURE[lang].placeholder}
        options={options}
        filterOption={(input, option) =>
          (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
        }
      />

      {/* <Select
        mode="multiple"
        tokenSeparators={[ ',' ]}
        style={{
          width: '100%',
        }}
        placeholder={formI18n.DRUG_FEATURE[lang].placeholder}
        notFoundContent={null}
        value={value?.data}
        onChange={(value)=>onChange({ data:value, logic:'or' })}
        // onChange={(value)=>onChange(value)}
        options={options}
        maxTagCount="responsive"
      /> */}
  
      {/* <Select
        mode="multiple"
        style={{
          width: '100%',
        }}
        placeholder={'Select Location'}
        value={value}
        onChange={(value) => onChange(value)}
        notFoundContent={null}
        options={options}
      /> */}
    </>
  );
};

export default DrugFeatureSelect;