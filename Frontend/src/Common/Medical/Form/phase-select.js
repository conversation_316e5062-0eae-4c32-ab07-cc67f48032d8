import { Select } from 'antd';

import { ReactComponent as ChevronDown } from '../../../assets/arrow-down.svg';
import { useLanguage } from '../../../utils/context/lang';
import { i18n as formI18n } from '../../i18n/form';

const PhaseSelect = ({ name, value = [], onChange }) => {
  const { lang } = useLanguage();
  const PHASES = [ 'I', 'II', 'III', 'IV', 'Preclinical', 'Others' ];
  return (
    <>
      <Select
        mode="multiple"
        style={{
          width: '100%',
        }}
        placeholder={formI18n.PHASE[lang].placeholder}
        value={value}
        onChange={(value) => onChange(value)}
        notFoundContent={null}
        options={PHASES.map((filter) => ({
          label: filter,
          value: filter,
        }))}
        suffixIcon={
          <ChevronDown className="h-4 w-4" />
        }
        // maxTagCount="responsive"
      />
    </>
  );
};

export default PhaseSelect;
