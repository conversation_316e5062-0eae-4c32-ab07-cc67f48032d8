import { useEffect } from 'react';

import { Select } from 'antd';

import { useLanguage } from '../../../utils/context/lang';
import { i18n as formI18n } from '../../i18n/form';

const GenderSelect = ({ name, value, onChange }) => {
  const { lang } = useLanguage();
  // Trigger onChange with the value only on mount or when value changes
  useEffect(() => {
    onChange(value || 'Both');
  }, [ value ]); // no onChange

  return (
    <>
      <Select
        style={{ width: '100%' }}
        placeholder={formI18n.GENDER[lang].placeholder}
        value={value || 'Both'}
        onChange={onChange} // Directly pass onChange since it's stable
        notFoundContent={null}
        options={[
          { label: 'Female', value: 'Female' },
          { label: 'Male', value: 'Male' },
          { label: 'Both', value: 'Both' },
        ]}
        maxTagCount="responsive"
      />
    </>
  );
};

export default GenderSelect;
