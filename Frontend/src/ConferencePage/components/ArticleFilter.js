import React from 'react';

import { useLanguage } from '../../utils/context/lang';
import { i18n } from '../i18n/conference';

// Conference types - can be moved to a constants file if needed
const CONFERENCE_TYPES = [
  { id: 'all', label: { en: 'All', zh: '全部' } },
  { id: 'asco', label: { en: 'ASCO', zh: 'ASCO' } },
  { id: 'esmo', label: { en: 'ESMO', zh: 'ESMO' } },
  { id: 'ash', label: { en: 'ASH', zh: 'ASH' } },
  { id: 'wclc', label: { en: 'WCLC', zh: 'WCLC' } },
];

// Generate years from current year backwards
const YEARS = Array.from({ length: 5 }, (_, i) => {
  const year = new Date().getFullYear() - i;
  return { 
    id: year.toString(), 
    label: { 
      en: year.toString(), 
      zh: `${year}年`
    }
  };
});

export function ConferenceFilter({ filters, onChange }) {
  const { lang } = useLanguage();

  const handleYearChange = (event) => {
    onChange({
      ...filters,
      year: event.target.value,
    });
  };

  const handleTypeChange = (event) => {
    onChange({
      ...filters,
      type: event.target.value,
    });
  };

  return (
    <div className="flex flex-col sm:flex-row gap-4 bg-base-200 p-4 rounded-lg">
      {/* Year Filter */}
      <div className="flex-1">
        <label className="block text-sm font-medium mb-2">
          {i18n.FILTER_YEAR[lang]}
        </label>
        <select
          value={filters.year || 'all'}
          onChange={handleYearChange}
          className="select select-bordered w-full"
        >
          <option value="all">{lang === 'en' ? 'All Years' : '所有年份'}</option>
          {YEARS.map((year) => (
            <option key={year.id} value={year.id}>
              {year.label[lang]}
            </option>
          ))}
        </select>
      </div>

      {/* Conference Type Filter */}
      <div className="flex-1">
        <label className="block text-sm font-medium mb-2">
          {i18n.FILTER_TYPE[lang]}
        </label>
        <select
          value={filters.type || 'all'}
          onChange={handleTypeChange}
          className="select select-bordered w-full"
        >
          {CONFERENCE_TYPES.map((type) => (
            <option key={type.id} value={type.id}>
              {type.label[lang]}
            </option>
          ))}
        </select>
      </div>

      {/* Clear Filters Button */}
      {(filters.year || filters.type) && (
        <div className="flex items-end">
          <button
            onClick={() => onChange({})}
            className="btn btn-ghost btn-sm"
          >
            {lang === 'en' ? 'Clear Filters' : '清除筛选'}
          </button>
        </div>
      )}
    </div>
  );
} 