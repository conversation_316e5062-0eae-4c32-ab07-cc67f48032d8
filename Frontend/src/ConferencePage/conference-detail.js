import React, { Suspense, useCallback, useEffect, useMemo, useRef, useState } from 'react';

import { Bars4Icon, TableCellsIcon } from '@heroicons/react/24/outline';
import { AllCommunityModule, ModuleRegistry , themeBalham } from 'ag-grid-community';
import { AgGridReact } from 'ag-grid-react';
import { motion } from 'framer-motion';
import Masonry from 'react-masonry-css';
import { useLoaderData, useLocation, useParams } from 'react-router-dom';

// import 'ag-grid-community/styles/ag-grid.css';
// import 'ag-grid-community/styles/ag-theme-alpine.css';

import { getConferenceDetail, getConferenceSettings, getThreadTasks } from '../api';
import { ReactComponent as VectorIcon } from '../assets/vector.svg';
import { ReactComponent as MagnifierIcon } from '../Common/assets/magnifier.svg';
import ChatPanel from '../Common/ChatPanal';
import EmptyState from '../Common/EmptyState';
import { i18n as chatI18n } from '../Common/i18n/chat';
import LogOutState from '../Common/LogOutState.js';
import PaperCard from '../Common/Medical/Card/PaperCard';
import DynamicQueryForm from '../Common/Medical/Form/DynamicQueryForm';
import { CONFERENCE_COLUMN } from '../Common/Medical/Table/columns/conference';
import { selectionStyles } from '../Common/styles/selection';
import { useAlert } from '../utils/context/alert';
import { useHistory } from '../utils/context/history';
import { useLanguage } from '../utils/context/lang';
import { useLayout } from '../utils/context/layout';
import { useUser } from '../utils/context/user';
import { setPageTitle } from '../utils/helpers';
import { ReactComponent as CardSelectIcon } from '../WorkflowPage/assets/card-slelct.svg';
import { ReactComponent as CardIcon } from '../WorkflowPage/assets/card.svg';
import { ReactComponent as ExportSelectIcon } from '../WorkflowPage/assets/export-select.svg';
import { ReactComponent as ExportIcon } from '../WorkflowPage/assets/export.svg';
import { ReactComponent as TableSelectIcon } from '../WorkflowPage/assets/table-select.svg';
import { ReactComponent as TableIcon } from '../WorkflowPage/assets/table.svg';

ModuleRegistry.registerModules([ AllCommunityModule ]);
const myTheme = themeBalham.withParams({
  fontFamily: 'serif',
  // headerFontFamily: 'Brush Script MT',
  headerFontFamily: 'monospace',
  cellFontFamily: 'monospace',
});

const PaperCardSkeleton = () => (
  <div className="bg-base-100 rounded-lg overflow-hidden border border-base-200 animate-pulse">
    <div className="p-4 border-b border-base-200">
      <div className="flex justify-between mb-4">
        <div className="h-4 w-16 bg-base-200 rounded"></div>
        <div className="h-4 w-24 bg-base-200 rounded"></div>
      </div>
      <div className="h-6 w-3/4 bg-base-200 rounded mb-3"></div>
      <div className="h-4 w-1/2 bg-base-200 rounded"></div>
    </div>
    <div className="p-4 space-y-4">
      <div className="space-y-2">
        <div className="h-4 w-full bg-base-200 rounded"></div>
        <div className="h-4 w-5/6 bg-base-200 rounded"></div>
      </div>
      <div className="flex gap-2">
        <div className="h-6 w-20 bg-base-200 rounded"></div>
        <div className="h-6 w-20 bg-base-200 rounded"></div>
      </div>
    </div>
  </div>
);

const LoadingGrid = ({ count = 6 }) => (
  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 gap-4">
    {[ ...Array(count) ].map((_, i) => (
      <PaperCardSkeleton key={i} />
    ))}
  </div>
);

const breakpointColumns = {
  default: 2,
  1400: 2,
  1024: 2,
  768: 1,
};

const MAX_SELECTED_ASSETS = 10;

const formatQueryValues = (queryValues) => {
  const resultValues = JSON.parse(JSON.stringify(queryValues));
  Object.keys(resultValues).forEach((key) => {
    const item = resultValues[key];
    const data = item?.data;
    const formatArray = (array) => {
      return array.map((subItem) => {
        return subItem[subItem.length - 1]
      })
    }
    if (Array.isArray(item) && Array.isArray(item[0])) {
      resultValues[key] = formatArray(item);
    } else if (Array.isArray(data) && Array.isArray(data[0])) {
      resultValues[key].data = formatArray(data);
    }
  });
  return resultValues;
}

export function ConferenceDetail() {
  const { conferenceId: workflowId, threadId: urlThreadId } = useParams();
  const location = useLocation();
  const { workflowSettings, workflowData: initialWorkflowData, threadData, initialQueryValues, queryValuesForSearch } = useLoaderData();
  
  useEffect(() => {
    if (workflowSettings?.name) {
      setPageTitle(workflowSettings.name);
    }
  }, [ workflowSettings?.name ]);

  console.log('Initial data:', {
    initialWorkflowData,
    isArray: Array.isArray(initialWorkflowData),
    firstItem: initialWorkflowData?.[0]
  });
  const [ isPanelOpen, setIsPanelOpen ] = useState(true);
  const [ workflowData, setWorkflowData ] = useState(initialWorkflowData || { data: [], count: 0, page: 0, total_pages: 0 });
  const [ isLoading, setIsLoading ] = useState(false);
  const [ hasMore, setHasMore ] = useState(true);
  const [ page, setPage ] = useState(1);
  const [ filters, setFilters ] = useState(initialQueryValues || {});
  const [ activeThreadId, setActiveThreadId ] = useState(urlThreadId);
  const [ hasSelectAll, setHasSelectAll ] = useState(false);
  const [ lastScrollPosition, setLastScrollPosition ] = useState(0);
  const [ isSearchFormOpen, setIsSearchFormOpen ] = useState(true);
  const observerTarget = useRef(null);
  const { lang } = useLanguage();
  const showAlert = useAlert();
  const [ queryValues, setQueryValues ] = useState({});
  const [ viewMode, setViewMode ] = useState('grid'); // 'grid' or 'table'
  const gridRef = useRef();
  const prevWorkflowIdRef = useRef();
  const gridApiRef = useRef(null);
  const { user } = useUser();
  const { updateHistory } = useHistory();

  // Add this effect to scroll to top when workflowId changes
  useEffect(() => {
    window.scrollTo(0, 0);
    // Clear the data immediately when workflowId changes
    setWorkflowData([]);
    setIsLoading(true);
  }, [ workflowId ]);

  // Handle initialization of new data
  useEffect(() => {
    console.log('re inited.......')
    
    setWorkflowData(initialWorkflowData || []);
    setIsLoading(false);
    setHasMore(true);
    setPage(1);
    setQueryValues(queryValuesForSearch || {});

    // clear states when workflowId changes
    const previousWorkflowId = prevWorkflowIdRef.current;
    prevWorkflowIdRef.current = workflowId;
    if (previousWorkflowId && (previousWorkflowId !== workflowId)) {
      // Clear query values
      setFilters({});
      // Clear selected assets
      setSelectedAssets([]);
      // Reset chat-related states
      setActiveThreadId(null);
      // Reset panel state if needed
      setIsPanelOpen(true);
      setIsSearchFormOpen(true);
      setHasSelectAll(false);
    }
    
    // Set query values from loader if they exist
    if (initialQueryValues && Object.keys(initialQueryValues).length > 0) {
      console.log('Setting query values from loader:', initialQueryValues);
      setFilters(initialQueryValues);
    }
  }, [ workflowId, initialWorkflowData, initialQueryValues ]);

  const [ selectedAssets, setSelectedAssets ] = useState([]);
  const containerRef = useRef(null);
  const { collapseSidebar } = useLayout();

  const rowSelection = useMemo(() => {
    return { mode: 'multiRow' };
  }, [  ]);
  // NOTE: SEEMS IMPORTANT. Memoize initialConversations to maintain stable reference, otherwise it will trigger the chat panel to reset.
  // const initialConversations = useMemo(() => 
  //   threadData?.initialData?.results || [], 
  // [ threadData?.initialData?.results ]
  // );

  // Handle infinite scroll
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMore && !isLoading) {
          loadMoreData();
        }
      },
      { threshold: 0.1 }
    );

    if (observerTarget.current && hasMore && !isLoading && viewMode === 'grid') {
      observer.observe(observerTarget.current);
    }

    return () => {
      if (observerTarget.current) {
        observer.unobserve(observerTarget.current);
      }
    };
  }, [ hasMore, isLoading, page, queryValues, viewMode ]);

  // Load more data
  const loadMoreData = async () => {
    if (isLoading || !hasMore) return;
    
    setIsLoading(true);
    try {
      const newData = await getConferenceDetail(workflowId, queryValues, page + 1);
      if (!newData?.data?.length) {
        setHasMore(false);
      } else {
        setWorkflowData(prev => {
          const prevData = prev?.data || [];
          return {
            data: [ ...prevData, ...newData.data ],
            count: newData.count || 0,
            page: newData.page || 1,
            total_pages: newData.total_pages || 1
          };
        });
        setPage(prev => prev + 1);
      }
    } catch (error) {
      setHasMore(false);
      showAlert('error', 'Failed to load more data');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle query submission
  const handleQuerySubmit = async (newQueryValues) => {
    const formattedQueryValues = formatQueryValues(newQueryValues);
    setIsLoading(true);
    setQueryValues(formattedQueryValues);
    setSelectedAssets([]);
    setHasSelectAll(false);
    try {
      const newData = await getConferenceDetail(workflowId, formattedQueryValues, 1);
      setWorkflowData({
        data: newData?.data || [],
        count: newData?.count || 0,
        page: newData?.page || 1,
        total_pages: newData?.total_pages || 1
      });
      setPage(1);
      setHasMore(newData?.data?.length > 0);
    } catch (error) {
      showAlert('error', 'Failed to query papers');
    } finally {
      setIsLoading(false);
    }
  };
  
  const onGridScroll = useCallback((event) => {
    const gridApi = event.api;
    const scrollPosition = gridApi.getVerticalPixelRange();
  
    // Avoid repeated triggering
    if (scrollPosition.bottom === lastScrollPosition) return;
    setLastScrollPosition(scrollPosition.bottom);
  
    // Calculate if it is close to the bottom
    const rowHeight = 80;
    const totalHeight = gridApi.getDisplayedRowCount() * rowHeight;
    const percentScrolled = (scrollPosition.bottom / totalHeight) * 100;
  
    if (percentScrolled > 90 && !isLoading && hasMore) {
      setIsLoading(true);
      loadMoreData();
    }
  }, [ loadMoreData, hasMore, isLoading, lastScrollPosition ]);

  // Handle thread creation
  const handleThreadCreated = (newThreadId) => {
    // NOTE: we should not set activeThreadId here, because it will trigger the chat panel to reset.
    // setActiveThreadId(newThreadId);
    window.history.replaceState(
      null, 
      '', 
      `/conference/${workflowId}/chat/${newThreadId}`
    );
    setActiveThreadId(newThreadId);
    updateHistory();
  };

  // Sync with URL changes
  useEffect(() => {
    setActiveThreadId(urlThreadId);
  }, [ urlThreadId ]);

  const toggleAssetSelect = (asset) => {
    setSelectedAssets(prev => {
      const isSelected = prev.some(p => p.id === asset.id);
      if (isSelected) {
        // Remove the asset if it's already selected
        return prev.filter(p => p.id !== asset.id);
      } else {
        // Add the asset if we're under the limit
        if (prev.length >= MAX_SELECTED_ASSETS) {
          showAlert('error', `You can only select up to ${MAX_SELECTED_ASSETS}`);
          return prev;
        }
        return [ ...prev, asset ];
      }
    });
  };
  // Handle responsive sidebar collapse
  useEffect(() => {
    const handleResize = () => {
      if (containerRef.current) {
        const containerWidth = containerRef.current.offsetWidth;
        if (containerWidth < 400) {
          collapseSidebar();
        }
      }
    };

    // Initial check
    handleResize();

    // Add resize listener
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Get column definitions based on workflow type
  const columnDefs = useMemo(() => {
    return CONFERENCE_COLUMN;
  }, [ ]);

  // AG Grid default configuration
  const defaultColDef = useMemo(() => ({
    sortable: true,
    filter: true,
    resizable: true,
    minWidth: 30,
  }), []);

  // Add effect to sync grid selection with selectedAssets
  useEffect(() => {
    if (viewMode === 'table' && gridApiRef.current) {
      const api = gridApiRef.current;
      
      // Get all row nodes
      api.forEachNode(node => {
        // Check if this row should be selected
        const shouldBeSelected = selectedAssets.some(asset => asset.id === node.data.id);
        // Only update if the selection state is different
        if (node.isSelected() !== shouldBeSelected) {
          node.setSelected(shouldBeSelected);
        }
      });
    }
  }, [ selectedAssets, viewMode ]);
  // Update the refreshIndexColumn function to also sync selection
  const refreshIndexColumn = () => {
    if (gridApiRef.current) {
      // First refresh the index column
      gridApiRef.current.refreshCells({
        columns: [ 'rowIndex' ],
        force: true
      });

      // Then re-sync the selection state
      gridApiRef.current.forEachNode(node => {
        const shouldBeSelected = selectedAssets.some(asset => asset.id === node.data.id);
        if (node.isSelected() !== shouldBeSelected) {
          node.setSelected(shouldBeSelected);
        }
      });
    }
  };
  const handleSelectAll = () => {
    if (hasSelectAll) {
      setSelectedAssets([]);
      if (viewMode === 'table' && gridApiRef.current) {
        gridApiRef.current.deselectAll();
      }
    } else {
      const maxItems = MAX_SELECTED_ASSETS;
      
      if (viewMode === 'table' && gridApiRef.current) {
        // Table view selection logic
        const allVisibleRows = [];
        gridApiRef.current.forEachNodeAfterFilterAndSort((node) => {
          allVisibleRows.push(node);
        });
  
        // Take only the first maxItems
        const rowsToSelect = allVisibleRows.slice(0, maxItems);
        
        // Deselect all first
        gridApiRef.current.deselectAll();
        
        // Select the first maxItems rows
        rowsToSelect.forEach(node => node.setSelected(true));
        
        // Update selectedAssets state
        setSelectedAssets(rowsToSelect.map(node => node.data));
      } else {
        // Grid view selection logic
        const itemsToSelect = workflowData?.data?.slice(0, maxItems);
        setSelectedAssets(itemsToSelect);
      }
      // Show alert if there are more items than the limit
      if (workflowData?.data?.length > maxItems) {
        showAlert('error', `${chatI18n.MAX_SELECT_ERROR[lang]} ${maxItems} ${chatI18n.ITEMS[lang]}`);
      }
    }
    setHasSelectAll(!hasSelectAll);
  };

  const handleDownload = () => {
    let rowCount = 0;
    if (gridApiRef.current) {
      // Get all visible rows in their current sort/filter order
      const allVisibleRows = [];
      gridApiRef.current.forEachNodeAfterFilterAndSort((node) => {
        allVisibleRows.push(node.data);
      });
      
      // Export using the current API
      gridApiRef.current.exportDataAsCsv({
        onlySelected: false,
        shouldRowBeSkipped: (params) => {
          return false;
        },
        processCellCallback: (params) => {
          return params.value;
        },
        getCustomContentBelowRow: () => null,
      });

      // if (allVisibleRows.length > 50) {
      //   showAlert('info', lang === 'en' ? 'Download limited to first 50 items' : '下载限制为前50条数据');
      // }
    }
  };

  const getCountText = () => {
    return typeof chatI18n.TOTAL_RESULTS_COUNT[lang] === 'function' 
      ? chatI18n.TOTAL_RESULTS_COUNT[lang](workflowData?.count >= 10000 ? '10000+' : workflowData?.count)
      : chatI18n.TOTAL_RESULTS_COUNT[lang].replace('{count}', workflowData?.count >= 10000 ? '10000+' : workflowData?.count)
  };

  if (!user) {
    return (
      <div ref={containerRef} className="relative min-h-screen bg-cream">
        <LogOutState />
      </div>
    )
  }

  return (
    <>
      {/* Search Section */}
      <DynamicQueryForm
        fields={workflowSettings.schema.fields}
        values={filters}
        onChange={setFilters}
        onSearch={handleQuerySubmit}
        isQueryFormExpanded={isSearchFormOpen}
        onClose={() => setIsSearchFormOpen(false)}
      />
      <div ref={containerRef} className="relative min-h-screen bg-cream">
        <div className={`transition-all duration-300 ease-in-out mx-auto h-screen
            ${isPanelOpen ? 'lg:mr-[484px]' : 'lg:mr-0'}`}>
          <div className="max-w-7xl mx-auto p-6 h-full">
            <div className="flex flex-col border h-full border-[#F1F1F1] rounded-[20px] bg-white">
              <div className="flex items-center justify-between p-5 flex-wrap border-b border-[#F1F1F1]"> 
                <div className="text-balck-1 text-xl font-bold">{workflowSettings?.name}</div>
                <button
                  onClick={() => setIsSearchFormOpen(true)}
                  className="px-3 py-1.5 text-sm font-semibold text-white 
                                                  bg-noah-theme rounded-lg
                                                  transition-all duration-200 text-center flex items-center"
                >
                  <MagnifierIcon className="w-4 h-4 mr-1" />
                  <div>{chatI18n.SEARCH[lang]}</div>
                </button>
              </div>
              <div className="overflow-auto flex-1 flex flex-col py-0">
                <div className="flex items-center justify-between pt-3 pb-4 px-5 flex-wrap gap-1">
                  <div
                    className="flex items-center gap-2 cursor-pointer"
                    onClick={handleSelectAll}
                  >
                    {/* Selection Indicator */}
                    <div className={`${selectionStyles.checkbox} 
                                                    ${hasSelectAll ? selectionStyles.selected : selectionStyles.unselected}`}
                    >
                      {hasSelectAll && (
                        <VectorIcon className={selectionStyles.checkIcon} />
                      )}
                    </div>
                    <div className="text-sm text-gray-1 font-medium">{getCountText()}</div>
                  </div>
                  {
                    workflowId !== 'clinical-guidelines' && (
                      <div className="flex items-center gap-2">
                        {
                          viewMode === 'table' && <button
                            onClick={handleDownload}
                            className="h-8 group flex items-center px-2.5 rounded-lg text-sm font-medium 
                                            transition-all duration-200 text-gray-1 hover:bg-base-200/80 hover:text-black-1"
                          >
                            <>
                              <ExportIcon className="w-4 h-4 mr-1.5 group-hover:hidden" />
                              <ExportSelectIcon className="w-4 h-4 mr-1.5 hidden group-hover:block" />
                            </>
                            {lang === 'en' ? 'Download Sheet' : '下载表格'}
                          </button>
                        }
                
                        {workflowId !== 'catalyst' && (
                          <div className="flex items-center bg-[#F1F3F4] backdrop-blur-sm gap-1 rounded-[10px] p-1">
                            <button
                              onClick={() => setViewMode('grid')}
                              className={`group relative flex items-center px-2.5 rounded-lg text-sm font-medium 
                                            transition-all duration-200 ${
                          viewMode === 'grid'
                            ? 'bg-white text-black shadow-sm'
                            : 'text-gray-1 hover:bg-base-200/80 hover:text-black-1'
                          }`}
                            >
                              <div className="w-8 h-8">
                                {viewMode === 'grid' ? (
                                  <CardSelectIcon className="w-8 h-8" />
                                ) : (
                                  <>
                                    <CardIcon className="w-8 h-8 absolute group-hover:opacity-0 transition-opacity" />
                                    <CardSelectIcon className="w-8 h-8 absolute opacity-0 group-hover:opacity-100 transition-opacity" />
                                  </>
                                )}
                              </div>
                              {chatI18n.GRID[lang]}
                            </button>
                            <button
                              onClick={() => setViewMode('table')}
                              className={`group relative flex items-center px-2.5 rounded-lg text-sm font-medium 
                                                    transition-all duration-200 ${
                          viewMode === 'table'
                            ? 'bg-white text-black shadow-sm'
                            : 'text-gray-1 hover:bg-base-200/80 hover:text-black-1'
                          }`}
                            >
                              <div className="w-8 h-8">
                                {viewMode === 'table' ? (
                                  <TableSelectIcon className="w-8 h-8" />
                                ) : (
                                  <>
                                    <TableIcon className="w-8 h-8 absolute group-hover:opacity-0 transition-opacity" />
                                    <TableSelectIcon className="w-8 h-8 absolute opacity-0 group-hover:opacity-100 transition-opacity" />
                                  </>
                                )}
                              </div>
                              {chatI18n.TABLE[lang]}
                            </button>
                          </div>
                        )}
                      </div>
                    )
                  }
                </div>
                <div className="flex-1 overflow-auto px-5">
                  {isLoading && !workflowData?.data ? (
                    <LoadingGrid count={6} />
                  ) : workflowData?.data === 0 ? (
                    <EmptyState />
                  ) : viewMode === 'grid' ? (
                  // Grid View
                    <>
                      <Masonry
                        breakpointCols={breakpointColumns}
                        className="flex -ml-4 w-auto"
                        columnClassName="pl-4 bg-clip-padding"
                      >
                        {workflowData?.data?.map((item, index) => {
                          return (
                            <div key={index} className="mb-4">
                              <PaperCard
                                paper={item}
                                isSelected={selectedAssets.some(p => p.id === item.id)}
                                onClick={() => toggleAssetSelect(item)}
                              />
                            </div>
                          );
                        })}
                      </Masonry>
                      {hasMore && <div ref={observerTarget} className="h-10 mt-4" />}
                    </>
                  ) : (
                  // Table View
                    <div className="flex-grow h-full">
                      <AgGridReact
                        theme={myTheme}
                        ref={gridRef}
                        rowHeight={80}
                        rowData={workflowData?.data || []}
                        columnDefs={columnDefs}
                        defaultColDef={defaultColDef}
                        rowSelection={{ mode: 'multiRow', headerCheckbox: false, enableSelectionWithoutKeys: true, enableClickSelection: true }}
                        selectionColumnDef={{
                          suppressHeaderMenuButton: false,
                          suppressHeaderFilterButton: true,
                          pinned: 'left',
                        }}
                        suppressRowClickSelection={false}
                        rowMultiSelectWithClick={true}
                        onRowSelected={(event) => {
                          console.log(event)
                          const selectedNodes = event.api.getSelectedNodes();
                          const selectedData = selectedNodes.map(node => node.data);
                          const allNodes = [];
                          event.api.forEachNode(node => allNodes.push(node));

                          // If this is a deselection (unselect all), allow it to proceed
                          if (selectedData.length < selectedAssets.length) {
                            setSelectedAssets(selectedData);
                            return;
                          }

                          // If trying to select more than limit
                          if (selectedData.length > MAX_SELECTED_ASSETS) {
                            // Find selected nodes in their original order
                            const orderedSelectedNodes = allNodes.filter(node => node.isSelected());
                                  
                            // Keep only the first N nodes where N is the limit
                            const nodesToKeep = orderedSelectedNodes.slice(0, MAX_SELECTED_ASSETS);
                                  
                            // Deselect all nodes first
                            event.api.deselectAll();
                                  
                            // Select only the first N nodes
                            nodesToKeep.forEach(node => node.setSelected(true));
                                  
                            showAlert('error', `You can only select up to ${MAX_SELECTED_ASSETS}`);
                                  
                            // Update selectedAssets with the kept nodes
                            setSelectedAssets(nodesToKeep.map(node => node.data));
                            return;
                          }
                                
                          setSelectedAssets(selectedData);
                        }}
                        getRowId={(params) => params.data.id}
                        onGridReady={(params) => {
                          gridApiRef.current = params.api;
                                
                          // Initial selection sync
                          if (selectedAssets.length > 0) {
                            params.api.forEachNode(node => {
                              const shouldBeSelected = selectedAssets.some(asset => asset.id === node.data.id);
                              node.setSelected(shouldBeSelected);
                            });
                          }
                        }}
                        animateRows={true}
                        enableCellTextSelection={true}
                        suppressNoRowsOverlay={true}
                        suppressColumnMoveAnimation={true}
                        maintainColumnOrder={true}
                        onSortChanged={() => {
                          // Refresh index column after sorting
                          refreshIndexColumn();
                        }}
                        onFilterChanged={() => {
                          // Refresh index column after filtering
                          refreshIndexColumn();
                        }}
                        onBodyScroll={onGridScroll}
                        pagination={false}
                        rowModelType="clientSide"
                        overlayLoadingTemplate={
                          isLoading ? 
                            '<span class="loading loading-spinner loading-sm"></span>' : 
                            ''
                        }
                      />
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Chat Panel */}
        <ChatPanel 
          enableUpload={false}
          isOpen={isPanelOpen}
          onToggle={setIsPanelOpen}
          suggestions={workflowSettings?.schema?.followup_questions}
          initialConversations={threadData?.initialData?.results || []}
          assetId={workflowId}
          assetType="conference"
          assetTitle={workflowSettings?.title}
          threadId={activeThreadId}
          onThreadCreated={handleThreadCreated}
          selectedItems={selectedAssets}
          onItemRemove={toggleAssetSelect}
          itemType="conference"
        />
      </div>
    </>
  );
}

export async function conferenceDetailLoader(location) {
  console.log('conferenceDetailLoader');
  const { conferenceId: workflowId, threadId } = location.params;
  
  // Get initial query values
  let initialQueryValues = {};
  const id = new URLSearchParams(new URL(location.request.url).search).get('id');

  const queryValuesForSearch = formatQueryValues(initialQueryValues);
  // Fetch data using the format initial query values
  const [ workflowSettings, workflowData ] = await Promise.all([
    getConferenceSettings(workflowId),
    getConferenceDetail(workflowId, queryValuesForSearch)
  ]);

  if (threadId) {
    const threadTasks = await getThreadTasks(threadId);
    const needFetchLatest =
      threadTasks?.results?.at(0) &&
      ![ 'complete', 'failed' ].includes(threadTasks?.results?.at(0)?.task_status);

    let runningTask = null;
    if (needFetchLatest) {
      runningTask = threadTasks.results.splice(0, 1)?.at(0);
    }
    threadTasks.results.reverse();

    return {
      workflowSettings,
      workflowData,
      initialQueryValues,
      queryValuesForSearch,
      threadData: {
        needFetchLatest,
        initialData: threadTasks,
        runningTask,
        threadId,
      },
    };
  }
  console.log('conference loader data', { 
    workflowSettings,
    workflowData,
    initialQueryValues,
    queryValuesForSearch,
    threadData: {
      needFetchLatest: false,
      initialData: { results: [] },
      runningTask: null,
      threadId: null,
    },
  })
  return { 
    workflowSettings,
    workflowData,
    initialQueryValues,
    queryValuesForSearch,
    threadData: {
      needFetchLatest: false,
      initialData: { results: [] },
      runningTask: null,
      threadId: null,
    },
  };
}
