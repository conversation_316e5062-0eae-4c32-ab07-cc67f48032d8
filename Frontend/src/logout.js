import { useEffect } from 'react';

import { redirect, useNavigate } from 'react-router-dom';

import { useUser } from './utils/context/user_deprecated.js';
import { deleteAllCookies } from './utils/cookie.js';

export function LogoutUI() {
  const navigate = useNavigate();
  const { logout } = useUser();

  useEffect(() => {
    logout();
    deleteAllCookies()
    navigate('/login');
  }, []);

  return (
    <div>
      <p>Logging out...</p>
    </div>
  );
}

export function logoutLoader() {
  return null;
}