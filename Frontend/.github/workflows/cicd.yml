name: Send submodule updates to parent repo

on:
  push:
    branches: 
      - main
      - dev

jobs:
  update:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
        with: 
          repository: NOAH-AI-CO/NoahServer
          token: ${{ secrets.PAT }}
          submodules: true
          ref: dev
          fetch-depth: 0

      - name: Pull & update submodules recursively
        run: |
          git config --global url.https://${{ secrets.PAT }}@github.com/.insteadOf https://github.com/
          git submodule update --force --init --remote

      - name: Commit
        run: |
          git config user.email "<EMAIL>"
          git config user.name "GitHub Actions - update submodules - frontend"
          git add --all
          git commit -m "Update submodules - frontend" || echo "No changes to commit"
          git push
          