# Frontend
react main product repo

## start dev
use `yarn dev` to start local dev, it's based on proxy middleware, reference setupProxy.js for more contexts.
Basically, in local dev, the browser fires requests to `localhost:3000/api`, it will be proxied to `REACT_APP_API_URL`.
websockets shouldn't require doing so, as there are no CORS limits on it.

## pre-commit
we ues pre-commit framework, so everything is within `.pre-commit-config.yaml`, to activate it now,
we need manually install by running `brew install pre-commit` and `pre-commit install`, so that this yaml is actually loaded into `.git/hooks/pre-config`


## TODO
- [ ] SSO rewrite
- [ ] CSS container queries https://tailwindcss.com/blog/tailwindcss-v3-2#container-queries




createThread:
      thread_content = {
        thread_name: question, //use question as thread name
        agent, // mindsearch, mindsearchrefer
        context: {
          reference: 'ash', 'clinical-result', '429',// may be empty
          reference_type: 'conference', 'workflow', 'discover',// 'workflow', 'conference', 'catalyst', this serves more for history's URL.
        }
      };



socket send:
      sendMessage({
        thread_id: threadId,
        question: text,
        upload_files: attachments?.map(attachment => attachment.id),
        agent: 'mindsearchrefer',
        language: lang.toUpperCase(),
        context: {
          ids: [],
        }
      });




