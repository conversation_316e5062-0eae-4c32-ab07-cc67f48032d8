#!/usr/bin/env python3
"""
测试沙箱文件操作API
"""
import asyncio
import aiohttp
import json


async def test_file_operations(sandbox_url="http://0.0.0.0:8194"):
    """测试沙箱文件操作API"""
    print(f"🧪 测试沙箱文件操作API: {sandbox_url}")
    
    file_endpoint = f"{sandbox_url}/v1/sandbox/file"
    workspace_endpoint = f"{sandbox_url}/v1/sandbox/workspace"
    
    async with aiohttp.ClientSession() as session:
        
        # 1. 测试获取工作空间信息
        print("\n📊 测试获取工作空间信息...")
        try:
            async with session.get(workspace_endpoint) as response:
                if response.status == 200:
                    result = await response.json()
                    print(f"✅ 工作空间信息: {json.dumps(result, indent=2, ensure_ascii=False)}")
                else:
                    print(f"❌ 获取工作空间信息失败: {response.status}")
        except Exception as e:
            print(f"❌ 工作空间信息请求异常: {e}")
        
        # 2. 测试创建目录
        print("\n📁 测试创建目录...")
        mkdir_payload = {
            "operation": "mkdir",
            "file_path": "test_dir",
            "content": ""
        }
        try:
            async with session.post(file_endpoint, json=mkdir_payload) as response:
                result = await response.json()
                print(f"📁 创建目录结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        except Exception as e:
            print(f"❌ 创建目录异常: {e}")
        
        # 3. 测试创建文件
        print("\n📄 测试创建文件...")
        create_payload = {
            "operation": "create",
            "file_path": "test_dir/hello.py",
            "content": "print('Hello from sandbox file!')\nprint('文件操作测试成功')\n"
        }
        try:
            async with session.post(file_endpoint, json=create_payload) as response:
                result = await response.json()
                print(f"📄 创建文件结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        except Exception as e:
            print(f"❌ 创建文件异常: {e}")
        
        # 4. 测试读取文件
        print("\n📖 测试读取文件...")
        read_payload = {
            "operation": "read",
            "file_path": "test_dir/hello.py",
            "content": ""
        }
        try:
            async with session.post(file_endpoint, json=read_payload) as response:
                result = await response.json()
                print(f"📖 读取文件结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        except Exception as e:
            print(f"❌ 读取文件异常: {e}")
        
        # 5. 测试列出目录
        print("\n📋 测试列出目录...")
        list_payload = {
            "operation": "list",
            "file_path": "",  # 根目录
            "content": ""
        }
        try:
            async with session.post(file_endpoint, json=list_payload) as response:
                result = await response.json()
                print(f"📋 目录列表结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        except Exception as e:
            print(f"❌ 列出目录异常: {e}")
        
        # 6. 测试写入文件（修改现有文件）
        print("\n✏️ 测试写入文件...")
        write_payload = {
            "operation": "write",
            "file_path": "test_dir/hello.py",
            "content": "print('Hello from updated sandbox file!')\nprint('文件修改测试成功')\nprint('新增内容')\n"
        }
        try:
            async with session.post(file_endpoint, json=write_payload) as response:
                result = await response.json()
                print(f"✏️ 写入文件结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        except Exception as e:
            print(f"❌ 写入文件异常: {e}")
        
        # 7. 再次读取文件验证修改
        print("\n🔍 验证文件修改...")
        try:
            async with session.post(file_endpoint, json=read_payload) as response:
                result = await response.json()
                print(f"🔍 修改后文件内容: {json.dumps(result, indent=2, ensure_ascii=False)}")
        except Exception as e:
            print(f"❌ 验证文件修改异常: {e}")
        
        # 8. 测试删除文件
        print("\n🗑️ 测试删除文件...")
        delete_payload = {
            "operation": "delete",
            "file_path": "test_dir/hello.py",
            "content": ""
        }
        try:
            async with session.post(file_endpoint, json=delete_payload) as response:
                result = await response.json()
                print(f"🗑️ 删除文件结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        except Exception as e:
            print(f"❌ 删除文件异常: {e}")
        
        # 9. 测试不安全路径
        print("\n🔒 测试路径安全检查...")
        unsafe_payload = {
            "operation": "read",
            "file_path": "../../../etc/passwd",
            "content": ""
        }
        try:
            async with session.post(file_endpoint, json=unsafe_payload) as response:
                result = await response.json()
                print(f"🔒 不安全路径测试结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        except Exception as e:
            print(f"❌ 不安全路径测试异常: {e}")
        
        # 10. 最终工作空间状态
        print("\n📊 最终工作空间状态...")
        try:
            async with session.get(workspace_endpoint) as response:
                if response.status == 200:
                    result = await response.json()
                    print(f"📊 最终工作空间信息: {json.dumps(result, indent=2, ensure_ascii=False)}")
        except Exception as e:
            print(f"❌ 最终工作空间信息异常: {e}")


async def test_integration_with_code_execution(sandbox_url="http://0.0.0.0:8194"):
    """测试文件操作与代码执行的集成"""
    print(f"\n🔗 测试文件操作与代码执行集成: {sandbox_url}")
    
    file_endpoint = f"{sandbox_url}/v1/sandbox/file"
    run_endpoint = f"{sandbox_url}/v1/sandbox/run"
    
    async with aiohttp.ClientSession() as session:
        
        # 1. 创建Python脚本文件
        print("📝 创建Python脚本...")
        create_payload = {
            "operation": "create",
            "file_path": "integration_test.py",
            "content": """
import os
import json

# 测试文件操作集成
print("=== 沙箱集成测试 ===")
print(f"当前工作目录: {os.getcwd()}")
print(f"目录内容: {os.listdir('.')}")

# 创建测试数据
data = {
    "message": "Hello from integrated test!",
    "timestamp": "2024-01-01",
    "success": True
}

# 写入JSON文件
with open("test_data.json", "w") as f:
    json.dump(data, f, indent=2)

print("✅ JSON文件创建成功")

# 读取并验证
with open("test_data.json", "r") as f:
    loaded_data = json.load(f)

print(f"📖 读取的数据: {loaded_data}")
print("🎉 集成测试完成!")
"""
        }
        
        try:
            async with session.post(file_endpoint, json=create_payload) as response:
                result = await response.json()
                print(f"📝 脚本创建结果: {result.get('message', 'Unknown')}")
        except Exception as e:
            print(f"❌ 脚本创建异常: {e}")
            return
        
        # 2. 执行Python脚本
        print("\n🚀 执行Python脚本...")
        exec_payload = {
            "language": "python3",
            "code": "exec(open('integration_test.py').read())"
        }
        
        try:
            async with session.post(run_endpoint, json=exec_payload) as response:
                result = await response.json()
                print(f"🚀 执行结果:")
                print(f"   输出: {result.get('data', {}).get('stdout', 'No output')}")
                if result.get('data', {}).get('error'):
                    print(f"   错误: {result['data']['error']}")
        except Exception as e:
            print(f"❌ 脚本执行异常: {e}")
        
        # 3. 验证生成的文件
        print("\n🔍 验证生成的文件...")
        read_payload = {
            "operation": "read",
            "file_path": "test_data.json",
            "content": ""
        }
        
        try:
            async with session.post(file_endpoint, json=read_payload) as response:
                result = await response.json()
                if result.get('code') == 0:
                    print(f"🔍 生成的JSON文件内容:")
                    print(f"   {result.get('data', {}).get('content', 'No content')}")
                else:
                    print(f"❌ 读取生成文件失败: {result.get('message')}")
        except Exception as e:
            print(f"❌ 验证文件异常: {e}")


async def main():
    """主测试函数"""
    print("🧪 开始沙箱文件操作API测试")
    
    # 基础文件操作测试
    await test_file_operations()
    
    # 集成测试
    await test_integration_with_code_execution()
    
    print("\n✅ 所有测试完成!")


if __name__ == "__main__":
    asyncio.run(main())
