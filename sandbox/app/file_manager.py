"""
沙箱文件管理器
提供安全的文件操作功能，限制在沙箱工作目录内
"""
import os
import tempfile
import shutil
from typing import Dict, List, Any, Optional
from pathlib import Path
import json


class FileManager:
    """沙箱文件管理器 - 提供安全的文件操作"""
    
    def __init__(self, work_dir: str = "/tmp/sandbox_workspace"):
        """
        初始化文件管理器
        
        Args:
            work_dir: 沙箱工作目录，默认为 /tmp/sandbox_workspace
        """
        self.work_dir = Path(work_dir).resolve()
        self._ensure_work_dir()
    
    def _ensure_work_dir(self):
        """确保工作目录存在"""
        self.work_dir.mkdir(parents=True, exist_ok=True)
    
    def _get_safe_path(self, file_path: str) -> Optional[Path]:
        """
        获取安全的文件路径，防止路径遍历攻击
        
        Args:
            file_path: 相对文件路径
            
        Returns:
            安全的绝对路径，如果路径不安全则返回 None
        """
        try:
            # 移除危险字符和路径
            if '..' in file_path or file_path.startswith('/'):
                return None
            
            # 构建完整路径
            full_path = (self.work_dir / file_path).resolve()
            
            # 确保路径在工作目录内
            if not str(full_path).startswith(str(self.work_dir)):
                return None
            
            return full_path
        except Exception:
            return None
    
    async def read_file(self, file_path: str) -> Dict[str, Any]:
        """
        读取文件内容
        
        Args:
            file_path: 文件路径
            
        Returns:
            包含文件内容和元信息的字典
        """
        try:
            safe_path = self._get_safe_path(file_path)
            if not safe_path:
                return {"error": "文件路径不安全"}
            
            if not safe_path.exists():
                return {"error": "文件不存在"}
            
            if not safe_path.is_file():
                return {"error": "路径不是文件"}
            
            # 读取文件内容
            with open(safe_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            return {
                "success": True,
                "content": content,
                "file_size": len(content),
                "message": "文件读取成功"
            }
            
        except UnicodeDecodeError:
            # 尝试以二进制模式读取
            try:
                with open(safe_path, 'rb') as f:
                    content = f.read()
                return {
                    "success": True,
                    "content": f"<二进制文件，大小: {len(content)} 字节>",
                    "file_size": len(content),
                    "is_binary": True,
                    "message": "二进制文件读取成功"
                }
            except Exception as e:
                return {"error": f"文件读取失败: {str(e)}"}
        except Exception as e:
            return {"error": f"文件读取失败: {str(e)}"}
    
    async def write_file(self, file_path: str, content: str) -> Dict[str, Any]:
        """
        写入文件内容
        
        Args:
            file_path: 文件路径
            content: 文件内容
            
        Returns:
            操作结果字典
        """
        try:
            safe_path = self._get_safe_path(file_path)
            if not safe_path:
                return {"error": "文件路径不安全"}
            
            # 确保父目录存在
            safe_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 写入文件
            with open(safe_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            return {
                "success": True,
                "bytes_written": len(content),
                "message": "文件写入成功"
            }
            
        except Exception as e:
            return {"error": f"文件写入失败: {str(e)}"}
    
    async def create_file(self, file_path: str, content: str = "") -> Dict[str, Any]:
        """
        创建新文件
        
        Args:
            file_path: 文件路径
            content: 文件内容，默认为空
            
        Returns:
            操作结果字典
        """
        try:
            safe_path = self._get_safe_path(file_path)
            if not safe_path:
                return {"error": "文件路径不安全"}
            
            if safe_path.exists():
                return {"error": "文件已存在"}
            
            # 创建文件
            return await self.write_file(file_path, content)
            
        except Exception as e:
            return {"error": f"文件创建失败: {str(e)}"}
    
    async def delete_file(self, file_path: str) -> Dict[str, Any]:
        """
        删除文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            操作结果字典
        """
        try:
            safe_path = self._get_safe_path(file_path)
            if not safe_path:
                return {"error": "文件路径不安全"}
            
            if not safe_path.exists():
                return {"error": "文件不存在"}
            
            if safe_path.is_file():
                safe_path.unlink()
                return {
                    "success": True,
                    "message": "文件删除成功"
                }
            elif safe_path.is_dir():
                shutil.rmtree(safe_path)
                return {
                    "success": True,
                    "message": "目录删除成功"
                }
            else:
                return {"error": "未知文件类型"}
            
        except Exception as e:
            return {"error": f"文件删除失败: {str(e)}"}
    
    async def list_files(self, dir_path: str = "") -> Dict[str, Any]:
        """
        列出目录中的文件
        
        Args:
            dir_path: 目录路径，默认为根目录
            
        Returns:
            包含文件列表的字典
        """
        try:
            if not dir_path:
                target_path = self.work_dir
            else:
                target_path = self._get_safe_path(dir_path)
                if not target_path:
                    return {"error": "目录路径不安全"}
            
            if not target_path.exists():
                return {"error": "目录不存在"}
            
            if not target_path.is_dir():
                return {"error": "路径不是目录"}
            
            files = []
            for item in target_path.iterdir():
                try:
                    stat = item.stat()
                    files.append({
                        "name": item.name,
                        "type": "file" if item.is_file() else "directory",
                        "size": stat.st_size if item.is_file() else 0,
                        "modified": stat.st_mtime,
                        "path": str(item.relative_to(self.work_dir))
                    })
                except Exception:
                    # 跳过无法访问的文件
                    continue
            
            # 按名称排序
            files.sort(key=lambda x: (x["type"] == "file", x["name"]))
            
            return {
                "success": True,
                "files": files,
                "count": len(files),
                "message": "目录列表获取成功"
            }
            
        except Exception as e:
            return {"error": f"目录列表获取失败: {str(e)}"}
    
    async def create_directory(self, dir_path: str) -> Dict[str, Any]:
        """
        创建目录
        
        Args:
            dir_path: 目录路径
            
        Returns:
            操作结果字典
        """
        try:
            safe_path = self._get_safe_path(dir_path)
            if not safe_path:
                return {"error": "目录路径不安全"}
            
            if safe_path.exists():
                return {"error": "目录已存在"}
            
            safe_path.mkdir(parents=True, exist_ok=True)
            
            return {
                "success": True,
                "message": "目录创建成功"
            }
            
        except Exception as e:
            return {"error": f"目录创建失败: {str(e)}"}
    
    def get_workspace_info(self) -> Dict[str, Any]:
        """
        获取工作空间信息
        
        Returns:
            工作空间信息字典
        """
        try:
            total_size = 0
            file_count = 0
            dir_count = 0
            
            for item in self.work_dir.rglob("*"):
                if item.is_file():
                    file_count += 1
                    total_size += item.stat().st_size
                elif item.is_dir():
                    dir_count += 1
            
            return {
                "workspace_path": str(self.work_dir),
                "total_size": total_size,
                "file_count": file_count,
                "directory_count": dir_count,
                "exists": self.work_dir.exists()
            }
            
        except Exception as e:
            return {"error": f"获取工作空间信息失败: {str(e)}"}
