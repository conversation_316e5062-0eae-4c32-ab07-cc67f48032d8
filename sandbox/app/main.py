from fastapi import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, HTTPEx<PERSON>, Request
from starlette.middleware.base import BaseHTTPMiddleware
from pydantic import BaseModel
from typing import Optional, List
import asyncio
from .executor import CodeExecutor
from .file_manager import FileManager
import os


# 配置
API_KEY = os.getenv("API_KEY", "dify-sandbox")
MAX_REQUESTS = int(os.getenv("MAX_REQUESTS", "100"))
MAX_WORKERS = int(os.getenv("MAX_WORKERS", "10"))
WORKER_TIMEOUT = int(os.getenv("WORKER_TIMEOUT", "15"))

app = FastAPI()
executor = CodeExecutor(timeout=WORKER_TIMEOUT, max_workers=MAX_WORKERS)
file_manager = FileManager()

# 请求模型
class CodeRequest(BaseModel):
    language: str
    code: str
    preload: Optional[str] = ""
    enable_network: Optional[bool] = False

class FileRequest(BaseModel):
    operation: str  # read, write, create, delete, list, mkdir
    file_path: str
    content: Optional[str] = ""

# 认证中间件
class AuthMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        if request.url.path.startswith("/v1/sandbox"):
            api_key = request.headers.get("X-Api-Key")
            if not api_key or api_key != API_KEY:
                # 修改这里：返回 JSONResponse 而不是直接返回 HTTPException
                from fastapi.responses import JSONResponse
                return JSONResponse(
                    status_code=401,
                    content={
                        "code": -401,
                        "message": "Unauthorized",
                        "data": None
                    }
                )
        return await call_next(request)

# 并发控制中间件
class ConcurrencyMiddleware(BaseHTTPMiddleware):
    def __init__(self, app):
        super().__init__(app)
        self.semaphore = asyncio.Semaphore(MAX_WORKERS)
        self.current_requests = 0

    async def dispatch(self, request: Request, call_next):
        if request.url.path.startswith("/v1/sandbox/"):
            if self.current_requests >= MAX_REQUESTS:
                from fastapi.responses import JSONResponse
                return JSONResponse(
                    status_code=503,
                    content={
                        "code": -503,
                        "message": "Too many requests",
                        "data": None
                    }
                )

            self.current_requests += 1
            try:
                async with self.semaphore:
                    response = await call_next(request)
                return response
            finally:
                self.current_requests -= 1
        return await call_next(request)

# 添加中间件
# app.add_middleware(AuthMiddleware)
app.add_middleware(ConcurrencyMiddleware)

@app.get("/health")
async def health_check():
    return "ok"

@app.post("/v1/sandbox/run")
async def execute_code(request: CodeRequest):
    if request.language not in ["python3", "nodejs"]:
        return {
            "code": -400,
            "message": "unsupported language",
            "data": None
        }

    result = await executor.execute(request.code, request.language)

    return {
        "code": 0,
        "message": "success",
        "data": {
            "error": result["error"] or "",
            "stdout": result["output"] or "",
        }
    }

@app.post("/v1/sandbox/file")
async def file_operation(request: FileRequest):
    """
    沙箱文件操作API
    支持的操作：read, write, create, delete, list, mkdir
    """
    try:
        operation = request.operation.lower()
        file_path = request.file_path
        content = request.content or ""

        # 根据操作类型调用相应的文件管理器方法
        if operation == "read":
            result = await file_manager.read_file(file_path)
        elif operation == "write":
            result = await file_manager.write_file(file_path, content)
        elif operation == "create":
            result = await file_manager.create_file(file_path, content)
        elif operation == "delete":
            result = await file_manager.delete_file(file_path)
        elif operation == "list":
            result = await file_manager.list_files(file_path)
        elif operation == "mkdir":
            result = await file_manager.create_directory(file_path)
        else:
            return {
                "code": -400,
                "message": f"不支持的操作: {operation}",
                "data": None
            }

        # 检查操作是否成功
        if "error" in result:
            return {
                "code": -400,
                "message": result["error"],
                "data": None
            }
        else:
            return {
                "code": 0,
                "message": result.get("message", "操作成功"),
                "data": result
            }

    except Exception as e:
        return {
            "code": -500,
            "message": f"文件操作异常: {str(e)}",
            "data": None
        }

@app.get("/v1/sandbox/workspace")
async def get_workspace_info():
    """获取沙箱工作空间信息"""
    try:
        info = file_manager.get_workspace_info()
        return {
            "code": 0,
            "message": "success",
            "data": info
        }
    except Exception as e:
        return {
            "code": -500,
            "message": f"获取工作空间信息失败: {str(e)}",
            "data": None
        }


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8194)