"use client";

import { useRef, useState, Fragment } from "react";
import { Tab, TabGroup, TabList, TabPanel, TabPanels } from "@headlessui/react";
import { Transition } from "@headlessui/react";
import type { ReactElement } from "react";
import { 
  BeakerIcon, 
  ChartBarSquareIcon, 
  ClipboardDocumentListIcon, 
  AcademicCapIcon, 
  UsersIcon,
  PlayIcon
} from "@heroicons/react/24/solid";

export default function UseCases() {
  const tabsRef = useRef<HTMLDivElement>(null);
  const [selectedTab, setSelectedTab] = useState<number>(0);

  const useCasesData = {
    "Biopharma Professionals": {
      description: "From early-stage scientific research to clinical development, from technical analysis to commercial competitiveness, and from information synthesis to task automation—<PERSON> provides end-to-end support to accelerate the journey from bench to bedside for the Innovative therapies.",
      cases: [
        {
          title: "Drug Development Potential of Target CDH-17",
          url: "https://www.noahai.co/share/f4160104-1707-4808-92eb-e6080f481483"
        },
        {
          title: "Car-T in Multiple Sclerosis and Neuromyelitis Optica Spectrum Disorder",
          url: "https://www.noahai.co/share/999c63a9-2e70-446c-a974-e40c0dfd660b"
        },
        {
          title: "Latest Advances about Diagnosis and Treatment of Multiple Sclerosis",
          url: "https://www.noahai.co/share/682cd76a-983b-47c2-9aa6-6303ea238e24"
        },
        {
          title: "Animal Modes for Depression Study",
          url: "https://www.noahai.co/share/cffd349e-77a5-467a-b674-6e674d407a5a"
        }
      ]
    },
    "Biopharma Investors": {
      description: "From technical due diligence to clinical data analysis and industry intelligence, Noah empowers investors to uncover the next high-potential opportunity in healthcare.",
      cases: [
        {
          title: "Investment Memo of Regenxbio",
          url: "https://www.noahai.co/share/5ac03898-3b87-43ae-8884-7255a1edf140"
        },
        {
          title: "Industry Report about Radiopharmaceutical Drugs for Therapy",
          url: "https://www.noahai.co/share/3c51f8c4-c568-4223-b14d-11005a08a01a"
        },
        {
          title: "Competition Landscape of Oral Glp-1 Drugs",
          url: "https://www.noahai.co/share/59dbe025-9f08-4983-85c5-5370f4b879fd"
        },
        {
          title: "Best-in-class Drug for Weight-loss",
          url: "https://www.noahai.co/share/81f2c975-ed78-45d7-99f8-e894adb8ea10"
        }
      ]
    },
    "Clinicians": {
      description: "Noah supports doctors in quickly staying abreast of cutting-edge medical information via smart search and expert drug databases.",
      cases: [
        {
          title: "Optimizing Antithrombotic and Antiplatelet Strategies for Cardiogenic Stroke",
          url: "https://www.noahai.co/share/98e3bb04-8359-4519-bbda-67a265bb8611"
        },
        {
          title: "Indications and Contraindications of Lumbar Puncture",
          url: "https://www.noahai.co/share/a3796438-92b4-4401-9aa9-08fe1fc175b5"
        },
        {
          title: "Blood Pressure Management in Post-Thrombolysis Stroke Patients",
          url: "https://www.noahai.co/share/83c6987f-07c7-4cba-a737-6522ba2d1d3d"
        },
        {
          title: "Treatment Options for Asymptomatic Cerebral White Matter Demyelination",
          url: "https://www.noahai.co/share/a2af67ad-b036-45d8-9f3a-3f7cd04b2558"
        }
      ]
    },
    "Research Users": {
      description: "Noah empowers scientists to efficiently identify and process core literature, stay at the forefront of research, and accelerate innovative discoveries.",
      cases: [
        {
          title: "Animal Modes for Depression Study",
          url: "https://www.noahai.co/share/cffd349e-77a5-467a-b674-6e674d407a5a"
        },
        {
          title: "Relationship between Gut Microbiome and Metabolic diseases",
          url: "https://www.noahai.co/share/f5e7d0d1-3777-4447-af7e-604f9c3bb8e6"
        },
        {
          title: "How Does Auditory Hair Cell Regenerate?",
          url: "https://www.noahai.co/share/f36ba581-984f-443f-85c1-2d9afd30dd51"
        },
        {
          title: "How Does Weight-loss Drugs Work",
          url: "https://www.noahai.co/share/238ccacb-a0a6-4174-b1b0-1cf7530e21f5"
        }
      ]
    },
    "General Users": {
      description: "With the launch of Agent, Noah makes it easier for people to access professional and authoritative knowledge about diseases and medications.",
      cases: [
        {
          title: "Best-in-class Drug for Weight-loss",
          url: "https://www.noahai.co/share/81f2c975-ed78-45d7-99f8-e894adb8ea10"
        },
        {
          title: "Side Effects of Weight-loss Drugs",
          url: "https://www.noahai.co/share/1a78ce48-e1cf-4a4a-bf89-fcfaa893259b"
        },
        {
          title: "How Does Weight-loss Drugs Work",
          url: "https://www.noahai.co/share/238ccacb-a0a6-4174-b1b0-1cf7530e21f5"
        }
      ]
    }
  };

  const categories = Object.keys(useCasesData);

  const getTabIcon = (category: string) => {
    const iconMap: { [key: string]: ReactElement } = {
      "Biopharma Professionals": (
        <BeakerIcon className="w-5 h-5" />
      ),
      "Biopharma Investors": (
        <ChartBarSquareIcon className="w-5 h-5" />
      ),
      "Clinicians": (
        <ClipboardDocumentListIcon className="w-5 h-5" />
      ),
      "Research Users": (
        <AcademicCapIcon className="w-5 h-5" />
      ),
      "General Users": (
        <UsersIcon className="w-5 h-5" />
      )
    };
    return iconMap[category] || iconMap["General Users"];
  };

  return (
    <section id="use-cases" className="py-12 sm:py-16 md:py-20 bg-white">
      <div className="mx-auto max-w-5xl px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12 sm:mb-16">
          <h2 className="text-2xl sm:text-3xl font-semibold text-gray-900 mb-3">Use Cases</h2>
          <p className="text-base sm:text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed">
            Explore how Noah AI handles real-world tasks across different domains.
          </p>
        </div>

        <TabGroup selectedIndex={selectedTab} onChange={setSelectedTab}>
          {/* Category Navigation */}
          <div className="flex justify-center mb-8 sm:mb-12">
            <TabList className="relative inline-flex flex-col sm:flex-row sm:flex-wrap justify-center rounded-xl sm:rounded-2xl bg-white p-1 shadow-sm border border-gray-200/60 gap-1 w-full max-w-4xl">
              {categories.map((category, index) => (
                <Tab key={index} as={Fragment}>
                  <button
                    className={`flex items-center justify-center sm:justify-start gap-2 sm:gap-3 px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg sm:rounded-xl text-xs sm:text-sm font-medium transition-all duration-300 cursor-pointer w-full sm:w-auto min-h-[44px] ${
                      selectedTab === index 
                        ? "bg-gray-900 text-white shadow-sm" 
                        : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
                    }`}
                  >
                    <span className={`flex-shrink-0 transition-all duration-300 ${
                      selectedTab === index 
                        ? "text-white scale-110" 
                        : "text-gray-400 scale-100"
                    }`}>
                      {getTabIcon(category)}
                    </span>
                    <span className="font-medium text-center sm:text-left leading-tight">{category}</span>
                  </button>
                </Tab>
              ))}
            </TabList>
          </div>

          {/* Tab Content */}
          <TabPanels>
            {categories.map((category, tabIndex) => (
              <TabPanel key={tabIndex} as={Fragment}>
                <Transition
                  show={selectedTab === tabIndex}
                  enter="transition-all duration-300 ease-out"
                  enterFrom="opacity-0 translate-y-4"
                  enterTo="opacity-100 translate-y-0"
                  leave="transition-all duration-200 ease-in"
                  leaveFrom="opacity-100 translate-y-0"
                  leaveTo="opacity-0 -translate-y-4"
                >
                  <div className="space-y-6 sm:space-y-8">
                    {/* Category Description */}
                    <div className="text-center max-w-3xl mx-auto px-4 sm:px-0">
                      <p className="text-sm sm:text-base text-gray-600 leading-relaxed">
                        {useCasesData[category as keyof typeof useCasesData].description}
                      </p>
                    </div>

                    {/* Use Cases List */}
                    <div className="space-y-1 px-4 sm:px-0">
                      {useCasesData[category as keyof typeof useCasesData].cases.map((useCase, index) => (
                        <a
                          key={index}
                          href={useCase.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="group flex items-start sm:items-center justify-between p-3 sm:p-4 rounded-lg hover:bg-gray-50 transition-all duration-200 border border-transparent hover:border-gray-200 min-h-[60px] sm:min-h-[auto]"
                        >
                          <div className="flex items-start sm:items-center gap-3 sm:gap-4 flex-1 min-w-0">
                            <span className="text-xs sm:text-sm font-mono text-gray-400 w-5 sm:w-6 mt-0.5 sm:mt-0 flex-shrink-0">
                              {String(index + 1).padStart(2, '0')}
                            </span>
                            <h3 className="text-sm sm:text-base text-gray-900 font-medium group-hover:text-gray-700 transition-colors duration-200 leading-snug pr-2">
                              {useCase.title}
                            </h3>
                          </div>
                          <div className="flex items-center justify-center w-8 h-8 sm:w-8 sm:h-8 rounded-full bg-gray-100 group-hover:bg-gray-900 transition-all duration-200 flex-shrink-0">
                            <PlayIcon className="w-3 sm:w-4 h-3 sm:h-4 text-gray-600 group-hover:text-white transition-all duration-200" />
                          </div>
                        </a>
                      ))}
                    </div>
                  </div>
                </Transition>
              </TabPanel>
            ))}
          </TabPanels>
        </TabGroup>
      </div>
    </section>
  );
}
