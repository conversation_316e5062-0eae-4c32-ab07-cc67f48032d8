import django
import os
from django.conf import settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'NoahBackend.settings.docker_migration')
django.setup()

from posthog import Posthog
from Dashboard.module_view_reusables import *
posthog = Posthog('phc_70QuDVGIctzA09RyOqGtAYn4yxOvws2O7LbVXYmQHbM', host='https://posthog.noahai.co')

from datetime import datetime
from django.utils.timezone import make_aware
from Account.models import User

for user in User.objects.all():
    # if user.email != '<EMAIL>':
    #     continue
    joined = user.date_joined
    # print(f"Processing user {user.username} who joined on {joined}.")
    target_date = make_aware(datetime.strptime('2025-06-27', '%Y-%m-%d'))
    if user.date_joined and user.date_joined < target_date:
        print(f"User {user.username} joined on {joined}.")
        posthog.capture(
            'signup_completed',
            distinct_id=user.email,
            properties={
                'host': settings.HOST,
                'email': user.email,
            },
            timestamp = joined
        )
        # break