from django.contrib import admin
from Account.models import User
from Account.admin import HandlerFilter
from .models import Feedback, WaitlistSubject, ReferralCode
import random
import string
from django.contrib.admin.helpers import ACTION_CHECKBOX_NAME
from import_export.admin import ImportExportMixin
from import_export import resources, fields
from import_export.widgets import ForeignKeyWidget
    
# Register your models here.
@admin.register(Feedback)
class FeedbackAdmin(admin.ModelAdmin):
    list_display = ['user', 'ip', 'last_name', 'first_name', 'message', 'time_created']
    list_filter = []
    list_editable = []
    search_fields = ['user', 'last_name', 'first_name', 'message']
    
@admin.register(WaitlistSubject)
class WaitlistSubjectAdmin(admin.ModelAdmin):
    list_display = ['user', 'list_name', 'time_created']
    list_filter = ['list_name']
    list_editable = []
    search_fields = ['user']

class ReferralCodeResource(resources.ModelResource):
    user = fields.Field(
        column_name='user',
        attribute='user',
        widget=ForeignKeyWidget(User, 'username')
    )
    handler = fields.Field(
        column_name='handler',
        attribute='handler',
        widget=ForeignKeyWidget(User, 'username')
    )

    claim_status = fields.Field(
        column_name='claim_status',
        attribute='time_claimed',
        widget=fields.widgets.BooleanWidget()
    )

    def dehydrate_claim_status(self, obj):
        return "已使用" if obj.time_claimed else "未使用"

    class Meta:
        model = ReferralCode
        fields = ('code', 'status', 'claim_status', 'referral_type', 'user', 'handler', 'comment', 'comment_2', 'time_created', 'time_claimed')

@admin.register(ReferralCode)
class ReferralCodeAdmin(ImportExportMixin, admin.ModelAdmin):
    resource_class = ReferralCodeResource
    list_display = ['code', 'status', 'claim_status', 'referral_type', 'user', 'handler', 'comment', 'comment_2', 'time_created', 'time_claimed']
    list_filter = [HandlerFilter]
    list_editable = ['referral_type', 'comment', 'status', 'comment_2']
    search_fields = ['code']
    readonly_fields = ['code', 'time_created', 'time_claimed', 'handler','user']
    
    actions = ['generate_1_referral_code', 'generate_5_referral_codes', 'generate_10_referral_codes',
               'generate_20_referral_codes', 'generate_50_referral_codes', 'generate_150_referral_codes']
    
    
    @admin.display(description='使用状态')
    def claim_status(self, obj):
        return "已使用" if obj.time_claimed else "未使用"

    def generate_n_referral_codes(self, request, queryset, n):
        count = 0
        extra_params = {}
        if queryset:
            extra_params['referral_type'] = queryset[0].referral_type
            extra_params['status'] = queryset[0].status
            extra_params['handler'] = request.user
            extra_params['comment'] = queryset[0].comment
            extra_params['comment_2'] = queryset[0].comment_2
        for _ in range(n*2):
            try:
                code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=10))
                ReferralCode.objects.create(code=code, **extra_params)
                count += 1
                if count >= n:
                    break
            except Exception as e:
                continue
        self.message_user(request, f"Successfully generated {n} new referral code(s).")
        
    def save_model(self, request, obj, form, change):
        if not obj.handler:
            obj.handler = request.user
        return super().save_model(request, obj, form, change)
        
    @admin.action()
    def generate_1_referral_code(self, request, queryset):
        self.generate_n_referral_codes(request, queryset, 1)
    @admin.action()
    def generate_5_referral_codes(self, request, queryset):
        self.generate_n_referral_codes(request, queryset, 5)
    @admin.action()
    def generate_10_referral_codes(self, request, queryset):
        self.generate_n_referral_codes(request, queryset, 10)
    @admin.action()
    def generate_20_referral_codes(self, request, queryset):
        self.generate_n_referral_codes(request, queryset, 20)
    @admin.action()
    def generate_50_referral_codes(self, request, queryset):
        self.generate_n_referral_codes(request, queryset, 50)
    @admin.action()
    def generate_150_referral_codes(self, request, queryset):
        self.generate_n_referral_codes(request, queryset, 150)

    generate_1_referral_code.short_description = "生成1个同渠道的邀请码"
    generate_5_referral_codes.short_description = "生成5个同渠道的邀请码"
    generate_10_referral_codes.short_description = "生成10个同渠道的邀请码"
    generate_20_referral_codes.short_description = "生成20个同渠道的邀请码"
    generate_50_referral_codes.short_description = "生成50个同渠道的邀请码"
    generate_150_referral_codes.short_description = "生成150个同渠道的邀请码"
    # generate_1_referral_code.acts_on_all = True