from Dashboard.utils import task_objs, thread_objs
from Dashboard.module_view_reusables import get_task_type
from django.conf import settings

count = 0
for task in task_objs.filter(thread__isnull=False):
    try:
        if task.agent and not task.agent_name:
            task.agent_name = task.agent.agent_name
        task.type = get_task_type(task)
        task.save()
        count += 1
    except Exception as e:
        print(e)
    
print('success count', count)