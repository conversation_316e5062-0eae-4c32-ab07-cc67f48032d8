import traceback
from typing import <PERSON><PERSON>
from datetime import datetime, timedelta
from django.forms import model_to_dict
from django.utils import timezone
from channels.db import database_sync_to_async
from asgiref.sync import sync_to_async
from API.models import Thread, Task
from utils.fieldenum import EnumTaskStatus
from copy import deepcopy, copy
import re

@database_sync_to_async
def get_params_from_latest_task(thread_id: str) -> Tuple[str, str, str]:
    try:
        thread = Thread.objects.get(id=thread_id)
        agent_name = thread.agent_name
        owner = thread.owner
        history = thread.task_set.order_by('-time_created')
        if history:
            latest_task = history[0]
            if latest_task.task_status == EnumTaskStatus.Complete or latest_task.task_status == EnumTaskStatus.Submitted and latest_task.time_created > timezone.now() - timedelta(minutes=3):
                return None, None, None, None, None, 'not retryable'
            question = latest_task.question
            agent_name = latest_task.agent_name
        else:
            return None, None, None, None, None, 'empty thread'
        return question, agent_name, owner, thread, latest_task, None
    except Exception as e:
        return None, None, None, None, None, str(e)

@database_sync_to_async
def get_latest_task(thread_id: str) -> Task:
    thread = Thread.objects.get(id=thread_id)
    history = thread.task_set.order_by('-time_created')
    if history:
        latest_task = history[0]
        return latest_task
    return None
    
@database_sync_to_async
def get_dict_repr_task_and_sub_tasks(task: Task):
    try:
        sub_task_objs = Task.objects.filter(parent_task=task).order_by('time_created')
        sub_tasks = list(model_to_dict(sub_task, exclude=['id', 'owner', 'thread', 'parent_task']) for sub_task in sub_task_objs)
    except Exception as e:
        traceback.print_exc()
        sub_tasks = []
    try:
        if task.parent_task:
            task = task.parent_task
        task_dict = model_to_dict(task, exclude=['owner', 'thread', 'parent_task', 'response'])
        task_dict['id'] = str(task.id)
    except Exception as e:
        traceback.print_exc()
        task_dict = {}
    return task_dict, sub_tasks

# def create_sub_task(self, rslt):
#     if rslt.get('tool_uses', '') and not rslt.get('running_task', None):
#         if not rslt.get('status', '') == 'DONE':
#             self.create_task(self.event['email'], self.event['thread'], self.event['question'], self.event['agent_name'])

async def validate_planning_task(text_data_json):
    task_id = text_data_json['planning_task']
    focused_task = await sync_to_async(Task.objects.get)(id=task_id)
    if not task_id or not focused_task:
        return None, 'planning task not found'
    planning_context = focused_task.context
    if not planning_context:
        return None, 'planning task context not found' 
    current_step = planning_context.get('current_step', None)
    if current_step is None:
        return None, 'planning task current_step not found'
    plan = planning_context.get('plan', [])
    if current_step > len(plan):
        return None, 'planning task reached end'
    question = planning_context.get('question', '')
    return question, None

async def process_planning_task(rslt, task:Task, prev_steps=[]):
    if not task.context:
        task.context = {}
    if not task.result_json:
        task.result_json = {}
    # update_flag = False
    # if (hitl_mode:=rslt.get('hitl_mode', False)): task.context['hitl_mode'] = hitl_mode
    if not (current_tool:=rslt.get('current_tool', {})): rslt.pop('current_tool', None)
    editable = rslt.get('editable', None)
    if not (rewrite_question:=rslt.get('rewrite_question', '')): rslt.pop('rewrite_question', None)
    if not (rewrite_result:=rslt.get('rewrite_result', '')): rslt.pop('rewrite_result', None)
    saveChat = rslt.pop('saveChat', False)
    type = rslt.get('type', '')
    if (current_step:=rslt.pop('current_step', 0)) is not None: task.context['current_step'] = current_step
    if (plan:=rslt.get('plan', [])): 
        for step in plan:
            if 'result' in step:
                step.pop('result')
        task.context['plan'] = deepcopy(plan)
    plan = prev_steps + plan
    for i, step in enumerate(plan):
        step['id'] = f'step_{str(i+1).zfill(3)}'
    rslt['plan'] = plan
    if type != 'planUpdate':
        rslt.pop('plan', None)
    if (language:=rslt.pop('language', '')): task.context['language'] = language
    if (feedback:=rslt.pop('feedback', [])): task.context['feedback'] = feedback
    if (question:=rslt.pop('question', '')) and not task.context.get('question', ''): 
        task.context['question'] = question
    type_symbol = get_type_symbol(rslt)
    sender_symbol = 'u' if rslt.get('sender', '') == 'user' else 'a'
    rslt['id'] = f'{str(task.id)}-{current_step+len(prev_steps) if current_step else current_step}-0-{type_symbol}-{sender_symbol}' if type != 'planUpdate' else '0'
    if (chunkIdx := rslt.get('chunkIdx', None)) is not None: 
        rslt['id'] += f"-{chunkIdx}"
    task.question = task.context.get('question','')
    # tool_uses = task.context.get('tool_uses', [])
    if 'tool_uses' not in task.context:
        task.context['tool_uses'] = []
    if type == 'planUpdate': rslt.pop('current_tool', None)
    # if update_flag:
    tool_uses = rslt.pop('tool_uses', None)
    if rslt.get('translations', {}): 
        task.context['translations'] = rslt.pop('translations')
    #print(f"{type}, {saveChat}, {rewrite_question}, {rewrite_result}")
    if type and saveChat:
        if tool_uses:
            task.context['tool_uses'] = tool_uses
            
        if editable is not None:
            task.context['editable'] = editable
        else:
            task.context.pop('editable', None)
        
        # save rewrite question and history messages.
        if rewrite_question:
            task.context['rewrite_question'] = rewrite_question
        elif rewrite_result:
            if 'rewrite_results' not in task.context:
                task.context['rewrite_results'] = []
            task.context['rewrite_results'].append(rewrite_result)

        if 'attachments_key' in rslt and rslt['attachments_key']:
            task.context['attachments_key'] = rslt['attachments_key']
        if (not_fields:=rslt.get('current_tool', {}).get('params', {}).get('apply_not_fields', [])):
            for not_field in not_fields:
                if not_field in (params:=rslt.get('current_tool', {}).get('params', {})):
                    if not params[not_field]:
                        continue
                    rslt['current_tool']['params'][not_field] = {"logic": "not", "data": rslt['current_tool']['params'][not_field]}
        events = task.result_json.get('events', [])
        events.append(rslt)
        task.result_json['events'] = events
        if type == 'statusUpdate' and rslt.get('agentStatus', '') == 'stopped':
            task.time_finished = datetime.utcnow()
            task.task_status = EnumTaskStatus.Complete.value
        await sync_to_async(task.save)()
    if rslt.get('message', ''):
        rslt.get('current_tool',{}).pop('result', '')
        
def get_type_symbol(rslt):
    type_dict = {
        'planUpdate': 'p',
        'statusUpdate': 's',
        'chat': 'c',
        'thought': 't',
        'reference': 'r',
        'unknown': 'u',
    }
    symbol1 = type_dict.get(rslt.get('type', 'unknown'), 'u')
    return symbol1

def replace_download_link(content):
    if isinstance(content, str):
        # Replace Huawei OBS links with Azure Blob Storage links
        pattern = r'https://([^.]+)\.obs\.cn-south-1\.myhuaweicloud\.com/([^)]+)\.zip'
        content = re.sub(pattern, r'https://noahdata.blob.core.windows.net/nudata/\2.zip', content)
        return content
    return False