import os
import uuid
from django.db import models
from django.contrib.auth.models import Group

from utils.fieldenum import (
    EnumTaskStatus,
    EnumTaskMode,
    EnumTaskComment
)
from Account.models import User

from .storage import AliOssStorage
from Dashboard.module_view_reusables import *
from API.posthog import posthog
from django.conf import settings


# Create your models here.


def path_and_rename(instance, filename):
    # https://stackoverflow.com/questions/********/django-imagefield-change-file-name-on-upload
    path = 'uploads'

    ext = filename.split('.')[-1]
    instance.oldname = filename
    # get filename
    if instance.pk:
        filename = '{}.{}'.format(instance.pk, ext)
    else:
        # set filename as random string
        filename = '{}.{}'.format(uuid.uuid4().hex, ext)
        # return the whole path to the file
    return os.path.join(path, filename)


def rename_uuid(instance, filename):
    ext = filename.split('.')[-1]
    instance.oldname = filename
    # get filename
    if instance.pk:
        filename = '{}.{}'.format(instance.pk, ext)
    else:
        # set filename as random string
        filename = '{}.{}'.format(uuid.uuid4().hex, ext)
        # return the whole path to the file
    return filename


class DownloadFile(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    download = models.FileField()


class Agent(models.Model):
    id = models.AutoField(primary_key=True)
    agent_name = models.CharField(
        default='',
        max_length=100,
        unique=True
    )
    agent_description = models.CharField(default="", max_length=2000)
    group = models.ForeignKey(
        Group,
        on_delete=models.CASCADE,
        blank=True,
        null=True
    )

    def __str__(self):
        return 'Agent {}: {}'.format(self.id, self.agent_name)


class Thread(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    thread_name = models.CharField(default="", max_length=500)
    time_created = models.DateTimeField(auto_now_add=True)
    time_updated = models.DateTimeField(auto_now=True, null=True, blank=True)
    time_active = models.DateTimeField(null=True, blank=True)
    context = models.JSONField(blank=True, null=True, default=dict)
    owner = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        blank=True,
        null=True
    )
    ip = models.CharField(default="", max_length=100, null=True, blank=True)
    agent_name = models.CharField(default="", max_length=100, null=True, blank=True)
    agent = models.ForeignKey(
        Agent,
        on_delete=models.SET_NULL,
        blank=True,
        null=True
    )
    task_count = models.IntegerField(blank=True, null=True)
    messages = models.JSONField(blank=True, null=True)
    
    @property
    def tasks(self):
        from django.forms.models import model_to_dict

        res = []
        for i in Task.objects.filter(thread_id=self.id):
            res.append(model_to_dict(i))

        return res

    class Meta:
        ordering = ['-time_active']
    
class Task(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    owner = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        blank=True,
        null=True
    )
    context = models.JSONField(blank=True, null=True, default=dict)
    ip = models.CharField(default="", max_length=100, null=True, blank=True)
    thread = models.ForeignKey(
        Thread,
        on_delete=models.CASCADE,
        blank=True,
        null=True
    )
    idx = models.IntegerField(null=True, blank=True)
    type = models.CharField(default="", max_length=100, null=True, blank=True)
    parent_task = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        blank=True,
        null=True
    )
    agent = models.ForeignKey(
        Agent,
        on_delete=models.SET_NULL,
        blank=True,
        null=True
    )
    agent_name = models.CharField(default="", max_length=100, null=True, blank=True)
    time_created = models.DateTimeField(auto_now_add=True)
    time_updated = models.DateTimeField(auto_now=True, null=True, blank=True)
    time_finished = models.DateTimeField(null=True, blank=True)
    task_status = models.CharField(
        choices=EnumTaskStatus.choices,
        default=EnumTaskStatus.Submitted.value,
        max_length=20
    )
    task_mode = models.CharField(
        choices=EnumTaskMode.choices,
        default=EnumTaskMode.NoahLLM.value,
        max_length=20
    )
    question = models.CharField(default="", max_length=50000)
    parameter_json = models.JSONField(null=True, blank=True, default=dict)
    
    response = models.CharField(default="", blank=True, max_length=200000)
    thought = models.CharField(null=True, blank=True, max_length=200000)
    result_link = models.URLField(null=True, blank=True, max_length=200)
    result_json = models.JSONField(null=True, blank=True, default=dict)
    result_file = models.ForeignKey(
        DownloadFile,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
    )
    task_comment = models.IntegerField(
        choices=EnumTaskComment.choices,
        default=EnumTaskComment.Null.value
    )
    class Meta:
        ordering = ['-time_created']
        
    @property
    def messages(self):
        from django.forms.models import model_to_dict

        res = []
        for i in Message.objects.filter(task_id=self.id):
            res.append(model_to_dict(i))

        return res

    class Meta:
        ordering = ['-time_created']
    
    
    def save(self, *args, **kwargs):
        if (not self.pk or not Task.objects.filter(pk=self.pk).exists()) and 'www.noahai.co' in settings.HOST and self.owner and self.owner.is_authenticated:
            # name = f"{self.user.first_name} {self.user.last_name}" if self.user.first_name and self.user.last_name else None
            
            reference_type = get_reference(self)
            unknown_reference = (reference_type == '未知')
            # Only track analytics for new task objects, not updates
            posthog.capture(
                'api_call',
                distinct_id=self.owner.email,
                properties={
                    'api_name': "Chat",
                    'host': settings.HOST,
                    'url': '/ws/chat/',
                    'type': get_task_type(self,save=False) if unknown_reference else reference_type,
                    'ip': self.ip,
                    'json_body': self.parameter_json,
                    'context': self.thread.context if self.thread else {},
                }
            )
        super().save(*args, **kwargs)

class Message(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    thread = models.ForeignKey(
        Thread,
        on_delete=models.CASCADE,
        blank=True,
        null=True
    )
    task = models.ForeignKey(
        Task,
        on_delete=models.CASCADE,
    )
    owner = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        blank=True,
        null=True
    )
    time_created = models.DateTimeField(auto_now_add=True)
    content = models.TextField(default="", max_length=65535)
    ROLE_CHOICES = [
        ('system', 'System'),
        ('user', 'User'),
        ('assistant', 'Assistant'),
        ('tool', 'Tool'),
    ]
    role = models.CharField(choices=ROLE_CHOICES, max_length=64)

    def __str__(self):
        return 'Message {}: {}'.format(self.id, self.content)
    
    class Meta:
        ordering = ['-time_created']

class UploadFile(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    upload = models.FileField(upload_to=rename_uuid, storage=AliOssStorage())
    oldname = models.CharField(default='', max_length=1000)
    owner = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        blank=True,
        null=True
    )
    thread = models.ForeignKey(
        Thread,
        related_name="upload_files",
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
    )
    task = models.ForeignKey(
        Task,
        related_name="upload_files",
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
    )
    
class SharedLink(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    owner = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        blank=True,
        null=True
    )
    thread = models.ForeignKey(
        Thread,
        related_name="shared_links",
        on_delete=models.CASCADE,
        blank=True,
        null=True,
    )
    idx = models.IntegerField(null=True, blank=True)
    task_idx = models.IntegerField(null=True, blank=True)
    time_created = models.DateTimeField(auto_now_add=True)