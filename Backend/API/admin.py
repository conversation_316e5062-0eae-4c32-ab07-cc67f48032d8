import os
import json
import traceback
from django import forms
from django.contrib import admin
from django.db.models.signals import pre_delete
from django.dispatch.dispatcher import receiver
from django.utils.html import format_html
from import_export.admin import ImportExportMixin, ExportActionMixin

from API.admin_extras import delete_sub_tasks

from .models import Agent, SharedLink, Thread, Task, UploadFile, Message
from django.db import models

# Register your models here.
# admin.site.register(Agent)

class PrettyJSONEncoder(json.JSONEncoder):
    def __init__(self, *args, indent, sort_keys, **kwargs):
        super().__init__(*args, indent=5, sort_keys=True, **kwargs)

class PrettyJSONForm(forms.ModelForm):
    class Meta:
        fields = '__all__'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        for field_name, field in self.fields.items():
            if isinstance(field, forms.JSONField):
                field.encoder = PrettyJSONEncoder

class ExcludeRoleFilter(admin.SimpleListFilter):
    title = 'exclude role'
    parameter_name = 'exclude_role'

    def lookups(self, request, model_admin):
        roles = set(
            [message.role for message in model_admin.model.objects.all()])
        return [(role, role.capitalize()) for role in roles]

    def queryset(self, request, queryset):
        if self.value():
            return queryset.exclude(role=self.value())
        return queryset



# class HandlerFilter(admin.SimpleListFilter):
#     title = 'Handlers'
#     parameter_name = 'handler_staff'
    
#     def lookups(self, request, model_admin):
#         staff_handlers = User.objects.filter(
#             is_staff=True, 
#         )
#         return [(user.id, user.username) for user in staff_handlers]
    
#     def queryset(self, request, queryset):
#         if self.value():
#             return queryset.filter(handler__id=self.value(), handler__is_staff=True)
#         return queryset

#     def has_add_permission(self, request):
#         return True

@admin.register(Thread)
class NoahThreadAdmin(ImportExportMixin, ExportActionMixin, admin.ModelAdmin):
    search_fields = ['id', 'thread_name', 'owner__email']
    list_display = ['short_id', 'owner', 'link', 
                    'task_list', 'thread_name', 'agent_name', 'time_updated', 'time_created']
    list_filter = ['agent_name']
    readonly_fields = ['id', 'task_list', 'time_created', 'time_updated', 'thread_name', 'task_count']
    list_per_page = 20
    ordering = ['-time_created']
    fields = [
        'id', 'thread_name', 'owner', 'agent_name', 'task_count',
        'time_created', 'time_updated', 'task_list'
    ]
    form = PrettyJSONForm
    
    def link(self, obj):
        try:
            return format_html(f'<a href="/hitl/{obj.pk}/">View Thread</a>')
        except:
            traceback.print_exc()
            return "Error"
    

    def task_list(self, obj):
        try:
            return format_html("".join([f'<div><a href="/api/management/admin/API/task/{task.pk}/change/"> {task.question[:35]}</a></div>' for task in Task.objects.filter(thread=obj).order_by('time_created')[:15][::-1]]))
        except:
            traceback.print_exc()

    def short_id(self, obj):
        return str(obj.id)[:6] + '...'

    # message_preview.short_description = "messages"
    short_id.short_description = "id"


@admin.register(Task)
class NoahTaskAdmin(ImportExportMixin, ExportActionMixin, admin.ModelAdmin):
    search_fields = ['id', 'thread__id', 'thread__thread_name', 'owner__email', 'result_json']
    list_display = ['short_id', 'owner',
                    'thread',  'agent_name', 'time_updated', 'time_created', 'time_finished']
    list_filter = ['agent_name']
    link_display_links = ['thread']
    fields = [
        'id', 'thread', 'owner', 'context', 'question', 'response', 'idx',
        'thought', 'result_json', 
        'task_status', 'parameter_json', 'time_created', 'time_updated', 'time_finished'
        ]
    actions = [delete_sub_tasks]
    readonly_fields = ['time_created', 'time_updated', 'time_finished', 'id', 'thread']
    list_per_page = 30
    ordering = ['-time_created']
    form = PrettyJSONForm
    
    formfield_overrides = {
        models.TextField: {
            'widget': forms.Textarea(attrs={'rows': 25, 'cols': 60, 'readonly': True})
        },
        models.CharField: {
            'widget': forms.Textarea()
        },
        # TextFieldEditable: {
        #     'widget': forms.Textarea(attrs={'rows': 3, 'cols': 30})
        # },
    }
    
    def short_id(self, obj):
        return str(obj.id)[:6] + '...'

    short_id.short_description = "id"


@admin.register(Message)
class NoahMessageAdmin(admin.ModelAdmin):
    search_fields = ['thread__id', 'task__id', 'thread__thread_name', 'owner__email']
    list_display = ['short_id', 'thread_id', 'task_id', 'content', 'role', 'owner', 'time_created']
    list_filter = ['role', ExcludeRoleFilter]
    link_display_links = ['thread', 'task']
    readonly_fields = ['id', 'thread', 'task', 'owner', 'time_created']
    list_per_page = 30
    form = PrettyJSONForm
    

    def short_id(self, obj):
        return str(obj.id)[:6] + '...'

    def short_content(self, obj):
        return obj.content[:100] + '...' if len(obj.content) > 100 else obj.content

    short_id.short_description = "id"


# @admin.register(UploadFile)
class NoahUploadFileAdmin(admin.ModelAdmin):
    search_fields = ['thread__id', 'task__id', 'owner__email']
    list_display = ['id', 'owner', 'thread', 'task', 'oldname', 'format']

    @admin.display(empty_value='')
    def format(self, obj):
        try:
            ext = os.path.splitext(str(obj.oldname))[-1].lower()
        except IndexError:
            ext = ''
        return ext


# @receiver(pre_delete, sender=UploadFile)
def uploadfile_file_delete(sender, instance, **kwargs):
    instance.upload.delete(False)


@admin.register(SharedLink)
class SharedLinkAdmin(ImportExportMixin, admin.ModelAdmin):
    list_display = ['owner', 'thread', 'idx', 'task_idx', 'time_created']
    