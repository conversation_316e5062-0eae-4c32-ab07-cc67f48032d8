import asyncio
from concurrent.futures import Thr<PERSON><PERSON>ool<PERSON>xecutor
from datetime import datetime, timedelta
import json
import os
import logging
import random
from django.conf import settings
from django.contrib.auth.models import AnonymousUser
from django.forms import model_to_dict
from django.shortcuts import get_object_or_404
import httpx
from asgiref.sync import sync_to_async
import requests
from rest_framework.response import Response
from rest_framework.authentication import TokenAuthentication
from rest_framework import (
    generics,
    views,
    viewsets,
    permissions,
    throttling,
    response
)
import tempfile
import ast

from API.serializers import (
    HistoryWorkflowTaskSerializer,
    MessageFullSerializer,
    TaskListSerializer,
    UploadFileSerializer,
    ThreadInfoSerializer,
    HistoryThreadSerializer,
    TaskSerializer, TaskDetailedSerializer,
    TaskCommentSerializer,
    UserSerializer,
    WorkflowTaskSerializer,
    MessageSerializer
)

from API.models import (
    Agent, Message, SharedLink, Thread, Task,
    UploadFile
)
from API.chat_former import hitl_task_formater
from Account.models import User
from API.throttle import ExtendedUserRateThrottle
from API.utils import replace_download_link
from Workflow.base_views import SaveAPIAddOn
from Workflow.decorators import save_call
from Workflow.models import WorkflowTask
from utils.fieldenum import EnumPricePlan, EnumTaskStatus
from django.db.models import Q
import utils.permissions as perm
import utils.throttling as thro
from django.utils import timezone
from Account.utils import credit_mapping

logger = logging.getLogger(__name__)

class ThreadListCreate(generics.CreateAPIView):
    """
    List threads or create a new thread.
    """
    
    authentication_classes = [TokenAuthentication]
    serializer_class = ThreadInfoSerializer
    
    class SpecificUserRateThrottle(ExtendedUserRateThrottle):
        rate = '60/min'
        
    throttle_classes = [SpecificUserRateThrottle]
        

    def get_permissions(self):
        # return permissions
        if self.request.method == 'POST':
            # create will need the HasAgentPermission
            # return [permissions.IsAuthenticated()]
            return [permissions.IsAuthenticated()]
        else:
            # other actions need IsOwnerOrAdmin permission
            return [permissions.IsAuthenticated(), 
                    perm.IsOwnerOrAdmin()]
            # return []
            
    def create(self, request, *args, **kwargs):
        if request.user.is_authenticated:
            owner = request.user
            owner.last_login = datetime.utcnow().astimezone()
            owner.save()
        else:
            owner = None
        validated_data = request.data
        validated_data['owner'] = owner
        try:
            if 'context' in validated_data and 'report' in validated_data['context'].get('reference', ""):
                validated_data['context']['status'] = 'init'
        except Exception as e:
            logger.error(e) 
            
        if len(validated_data['thread_name']) > 495:
            validated_data['thread_name'] = validated_data['thread_name'][:495] + "..." 
        agent = Agent.objects.filter(
            agent_name=validated_data['agent']
        )
        if (agent_name:=validated_data.get('agent', None)) and not validated_data.get('agent_name', None):
            validated_data['agent_name'] = agent_name
        if validated_data.get('agent_name', None) in credit_mapping:
            required_credits = credit_mapping[validated_data['agent_name']]
            if self.request.user.credits < required_credits:
                return Response({'error': "Insufficient credits."}, status=400)
        if agent.exists():
            agent = agent.first()
            validated_data['agent'] = agent
        else:
            validated_data['agent_name'] = validated_data.pop('agent')
        try: validated_data["ip"] = self.request.META.get('REMOTE_ADDR')
        except Exception as e : print(e)
        # create thread
        thread = Thread(**validated_data)
        thread.save()

        resp = model_to_dict(thread)
        resp['agent'] = resp.pop('agent_name') if 'agent_name' in validated_data else agent.agent_name
        resp['time_active'] = thread.time_updated
        resp['time_created'] = thread.time_created
        resp['owner'] = owner.username if owner else None
        if 'messages' in resp: resp.pop('messages')
        return Response(resp, status=200)

class ThreadHistory(generics.ListAPIView):
    """
    List all the tasks of a thread.
    """
    serializer_class = TaskDetailedSerializer
    permission_classes = [
        # permissions.IsAuthenticated,
        # perm.IsOwnerOrAdmin
        ]

    def get_queryset(self):
        if type(self.request.user) == AnonymousUser:
            Q_filter = Q(owner__isnull=True)
        elif self.request.user.is_superuser:
            Q_filter = Q()
        else:
            Q_filter = Q(owner__isnull=True) | Q(owner=self.request.user)
        threads = Thread.objects.filter(
        Q_filter,
        ).order_by('-time_created')
        
        thread = get_object_or_404(threads, pk=self.kwargs['thread_id'])
        # self.check_object_permissions(self.request, thread)
        history = list(thread.task_set.order_by('time_created'))
        if history:
            latest_task = history[-1]
            if latest_task.task_status == EnumTaskStatus.Submitted.value and latest_task.time_created > timezone.now() - timedelta(minutes=3):
                latest_task.task_status = EnumTaskStatus.Failed.value
        
        if thread.context and 'report' in thread.context.get('reference', ''):
            non_empty_tasks = [task for task in history if task.task_status == EnumTaskStatus.Complete.value and task.response]
            return non_empty_tasks if non_empty_tasks else history[:1]
        
        # Format mindsearch tasks for HITL response style when isHitl parameter is True
        if history and self.request.GET.get('isHitl', False):
            history = [
                hitl_task_formater(item) if item.agent_name.startswith('mindsearch') else item
                for item in history
            ]
        first_task_events = []
        first_task_plan = []
        current_step_no = 0
        for _, item in enumerate(history[:-1]):
            if item.result_json:
                events = item.result_json.pop('events', [])
                for event in events:
                    event_id_parts = event['id'].split('-')
                    if len(event_id_parts) < 6:
                        for idx, step in enumerate(event.get('plan', [])):
                            step['id'] = f'step_{str(idx+1).zfill(3)}'
                        continue
                    if event_id_parts[5] == '0':
                        continue
                    event_id_number = int(event_id_parts[5]) + current_step_no
                    event_id_parts[5] = str(event_id_number)
                    event['id'] = f"{'-'.join(event_id_parts)}"
                newest_plan = [obj for obj in item.context.pop('plan', []) if obj.get('status', 'todo') != 'todo']
                for idx, step in enumerate(newest_plan):
                    step['id'] =  f'step_{str(idx+1).zfill(3)}'
                    first_task_plan.append(step)
                
                first_task_events += events
        
        if history:
            # for event in first_task_events:
            #     event.pop('current_tool', None)
            latest_task_events = latest_task.result_json.get('events', [])
            history[0].result_json['events'] = first_task_events + latest_task_events
            for event in history[0].result_json['events']:
                if event.get('type', '') == 'statusUpdate':
                    if (not_fields:=event.get('current_tool', {}).get('params', {}).get('apply_not_fields', [])):
                        for not_field in not_fields:
                            not_field_data = event['current_tool']['params'][not_field]
                            if not not_field_data or type(not_field_data) == dict and not not_field_data.get('data', []):
                                event['current_tool']['params'].pop(not_field, None)
                                continue
                            if type(not_field_data) == list:
                                event['current_tool']['params'][not_field] = {"logic": "not", "data": not_field_data}
                            elif type(not_field_data) == dict:
                                not_field_data['logic'] = 'not'
                                event['current_tool']['params'][not_field] = not_field_data
                if event.get('current_tool', {}).get('tool', '') == 'Generate-Summary' and event.get('type', '') == 'chat':
                    replaced = replace_download_link(event.get('message',''))
                    if replaced:
                        event['message'] = replaced
            
            if thread.agent_name == "planning":
                history = history[:1]
            for task in history:
                if task.thought:
                    try:
                        json.loads(task.thought)
                    except json.JSONDecodeError:
                        try:
                            task.thought = json.dumps(ast.literal_eval(task.thought))
                        except Exception as e:
                            logger.info(f"Failed to process task thought: {e}")
            
        return history


class ThreadInfo(generics.RetrieveUpdateDestroyAPIView):
    """
    View or change information (mainly thread_name) of a thread.
    """
    serializer_class = ThreadInfoSerializer
    permission_classes = [
        # permissions.IsAuthenticated,
        # perm.IsOwnerOrAdmin
        ]
    authentication_classes = [TokenAuthentication]

    def get_queryset(self):
        qs = None
        if type(self.request.user) == AnonymousUser:
            Q_filter = Q(owner__isnull=True)
        elif self.request.user.is_superuser:
            Q_filter = Q()  # No filter for superuser - they can see all threads
        else:
            Q_filter = Q(owner__isnull=True) | Q(owner=self.request.user)
        qs = Thread.objects.filter(
        Q_filter,
        ).order_by('-time_created')
        return qs


class TaskInfo(generics.RetrieveUpdateDestroyAPIView):
    """
    View or change information of a task.
    """

    serializer_class = TaskSerializer
    permission_classes = [permissions.IsAuthenticated,
                          perm.IsOwnerOrAdmin]

    def get_queryset(self):
        qs = Task.objects.filter(owner=self.request.user)
        return qs


class TaskComment(generics.RetrieveUpdateAPIView):
    serializer_class = TaskCommentSerializer
    permission_classes = [permissions.IsAuthenticated,
                          perm.IsOwnerOrAdmin]

    def get_queryset(self):
        qs = Task.objects.filter(owner=self.request.user)
        return qs


class UploadFileView(viewsets.ModelViewSet):
    serializer_class = UploadFileSerializer
    permission_classes = [permissions.IsAuthenticated]
    queryset = UploadFile.objects.all()

    def get_throttles(self):
        """
        Instantiates and returns the list of throttles that this view uses.
        """
        throttles = list()
        throttles.append(thro.PricePlanThrottleClassDict['upload']())
        return throttles                                     

class HistoryListView(generics.ListAPIView):
    serializer_class_Thread = HistoryThreadSerializer
    serializer_class_Workflow = HistoryWorkflowTaskSerializer
    pagination_class = None
    authentication_classes = [TokenAuthentication]

    def get_permissions(self):
        # return permissions
        if self.request.method == 'POST':
            # create will need the HasAgentPermission
            return [permissions.IsAuthenticated(),
                    perm.IsOwnerOrAdmin()]
        else:
            # other actions need IsOwnerOrAdmin permission
            return []
            
    def get_queryset_Thread(self,limit,offset):
        return Thread.objects.filter(owner=self.request.user).order_by('-time_updated')[:limit+offset]

    def get_queryset_Workflow(self,limit,offset):
        return WorkflowTask.objects.filter(owner=self.request.user).order_by('-time_updated')[:limit+offset]

    def list(self, request, *args, **kwargs):
        if type(request.user) == AnonymousUser:
            return Response({
                "data": []
            })
       
        query_params = self.request.query_params
        item_type = query_params.get('type',[])
        threads, workflows = [], []
        limit = int(query_params.get('limit', 10))  # Default limit is 10
        offset = int(query_params.get('offset', 0))  # Default offset is 0
        

        if 'thread' in item_type and 'workflow' in item_type or not item_type:
            with ThreadPoolExecutor() as executor:
                future_threads = executor.submit(self.get_queryset_Thread, limit, offset)
                future_workflows = executor.submit(self.get_queryset_Workflow, limit, offset)
                # print("future_threads.result()", len(future_threads.result()))
                threads = self.serializer_class_Thread(future_threads.result(), many=True).data
                workflows = self.serializer_class_Workflow(future_workflows.result(), many=True).data
        elif 'thread' in item_type:
            threads = self.serializer_class_Thread(self.get_queryset_Thread(limit, offset), many=True).data
        elif 'workflow' in item_type:
            workflows = self.serializer_class_Workflow(self.get_queryset_Workflow(limit, offset), many=True).data
        qs = sorted(workflows + threads, key=lambda x: x['time_updated'], reverse=True)
        # Implement limit and offset

    
        return Response({
            "data": qs[offset:offset + limit]
        })
        
class WorkflowInfo(generics.RetrieveAPIView):
    serializer_class = WorkflowTaskSerializer
    permission_classes = [permissions.IsAuthenticated,
                          perm.IsOwnerOrAdmin]

    def get_queryset(self):
        qs = WorkflowTask.objects.filter(owner=self.request.user, id=self.kwargs['pk'])
        return qs
    
class MessageHistory(generics.ListAPIView):
    pagination_class = None
    serializer_class = MessageSerializer
    permission_classes = [
        # permissions.IsAuthenticated,
        # perm.IsOwnerOrAdmin
        ]

    def get_queryset(self):
        if type(self.request.user) == AnonymousUser:
            Q_filter = Q(owner__isnull=True)
        else:
            Q_filter = Q(owner__isnull=True) | Q(owner=self.request.user)
        thread_messages = Message.objects.filter(
        Q_filter,
        thread_id=self.kwargs['thread_id'],
        ).order_by('-time_created')
        if len(thread_messages) > 20:
            return thread_messages[0] + thread_messages[-19:]
        return thread_messages

class UserMessageHistory(generics.ListAPIView):
    pagination_class = None
    serializer_class = MessageFullSerializer
    permission_classes = [
        permissions.IsAuthenticated,
        perm.IsAdmin
        ]

    def get_queryset(self):
        thread_messages = Message.objects.filter(
        owner=self.kwargs['user_id'],
        ).order_by('-time_created')
        return thread_messages


class UserTaskHistory(generics.ListAPIView):
    pagination_class = None
    serializer_class = TaskListSerializer
    permission_classes = [
        permissions.IsAuthenticated,
        perm.IsAdmin
        ]

    def get_queryset(self):
        task_list = Task.objects.filter(
        owner=self.kwargs['user_id'],
        ).order_by('-time_created')
        return task_list


class UserList(generics.ListAPIView):
    pagination_class = None
    serializer_class = UserSerializer
    permission_classes = [
        permissions.IsAuthenticated,
        perm.IsAdmin
        ]

    def get_queryset(self):
        user_list = User.objects.all().order_by('-date_joined')
        return user_list
    
class ClaudeUsage(generics.GenericAPIView):
    permission_classes = [
        perm.IsTrialUserOrAdmin
        ]
    
    def post(self, request, *args, **kwargs):
        from django.http import StreamingHttpResponse
        noah_agent_url = f"{settings.EXTERNAL_API['NOAH_AGENT_HOST']}/claude"
        body = request.data

        async def event_stream():
            async with httpx.AsyncClient() as client:
                async with client.stream('POST', noah_agent_url, data=json.dumps(body), timeout=30, follow_redirects=True) as r:
                    async for chunk in r.aiter_text():  # or, for line in r.iter_lines():
                        yield chunk
        return StreamingHttpResponse(event_stream(), content_type='text/event-stream')
    
class ToolTest(generics.GenericAPIView):
    permission_classes = [
        perm.IsTrialUser
        ]
    
    def post(self, request, *args, **kwargs):
        noah_agent_url = f"{settings.EXTERNAL_API['NOAH_AGENT_HOST']}/tool_test"
        body = request.data
        headers = request.headers
        response = requests.post(noah_agent_url, data=json.dumps(body), headers=headers, timeout=1200, allow_redirects=True)
        try:
            data = response.json()
        except:
            data = response.text

        return Response(data=data)

            
# class ActiveTaskCount(generics.ListAPIView):
#     """
#     List all the tasks of a thread.
#     """
#     serializer_class = TaskListSerializer
#     permission_classes = [
#         perm.IsAdmin
#         ]
#     def get(self, request, *args, **kwargs):
#         # Get all keys in cache
#         from redis import Redis

#         redis = Redis("127.0.0.1",63791, db=10)
#         keys=redis.keys('*')
#         print(f"Number of keys in Redis cache: {len(keys)}")
        
#         return Response({
#             "task_count": len(keys)
#         })
        
class ShareThread(SaveAPIAddOn, generics.GenericAPIView):
    permission_classes = [
        permissions.IsAuthenticated,
        ]
    
    @save_call
    def post(self, request, *args, **kwargs):
        try:
            data = request.data
            
            thread = get_object_or_404(Thread, id=data['thread_id'])
            user = request.user
            if not user.is_superuser and thread.owner and thread.owner != request.user:
                return Response({'status': 'failed'}, status=403)
            
            args = {'owner': thread.owner, 
                    'thread': thread}
            def get_type(thread):
                if thread.context and 'report' in thread.context.get('reference', ''): return 'report'
                if thread.agent_name == 'planning': return 'hitl'
                return 'ask'
            thread_type = get_type(thread)
            end_idx = None
            if thread_type == 'hitl':
                try:
                    task = thread.task_set.order_by('time_created').first()
                    end_idx = len(task.result_json.get('events', []))
                except Exception as e:
                    logger.error(e)
            if end_idx:
                args['idx'] = end_idx
                
            args['task_idx'] = thread.task_set.count()
                
            slink_queryset = SharedLink.objects.filter(**args)
            if slink_queryset:
                slink = slink_queryset.first()
            else:
                slink = SharedLink.objects.create(**args)
            res = model_to_dict(slink)
            res['idx'] = end_idx
            res['owner'] = request.user.username if request.user.is_authenticated else None
            res['link'] = f"{request.get_host()}/shared/{thread_type}/{res['id']}"
            return Response({'status': 'success', 'data': res}, status=200)
        except Exception as e:
            logger.error(e)
            return Response({'status': 'failed'}, status=500)
        
class ViewSharedLink(SaveAPIAddOn, generics.GenericAPIView):
    permission_classes = []
    serializer_class = TaskDetailedSerializer
    
    @save_call
    def get(self, request, *args, **kwargs):
        shared_link_id = kwargs.get('pk')
        shared_link = get_object_or_404(SharedLink, id=shared_link_id)
        thread = shared_link.thread
        
        # Check if thread exists
        if not thread:
            return Response({'status': 'Thread not found'}, status=404)
        
        history = list(thread.task_set.order_by('time_created'))
        if history:
            latest_task = history[-1]
            if latest_task.task_status == EnumTaskStatus.Submitted.value and latest_task.time_created > timezone.now() - timedelta(minutes=3):
                latest_task.task_status = EnumTaskStatus.Failed.value
                latest_task.save()
        
        if thread.context and 'report' in thread.context.get('reference', ''):
            non_empty_tasks = [task for task in history if task.task_status == EnumTaskStatus.Complete.value and task.response]
            tasks = non_empty_tasks if non_empty_tasks else history[:1]
        else:
            tasks = history
            
        if thread.agent_name == "planning":
            end_idx = shared_link.idx or 1
            for task in tasks:
                if task.result_json.get('events', None):
                    task.result_json['events'] = task.result_json['events'][:end_idx]
        elif history and self.request.GET.get('isHitl', False):
             # format mindsearch task for hitl response style
            history = [
                hitl_task_formater(item) if item.agent_name.startswith('mindsearch') else item
                for item in history
            ]

        first_task_events = []
        first_task_plan = []
        current_step_no = 0
        task_idx = shared_link.task_idx or len(history)
        for _, item in enumerate(history[:task_idx]):
            if item.result_json:
                events = item.result_json.pop('events', [])
                for event in events:
                    event_id_parts = event['id'].split('-')
                    if len(event_id_parts) < 6:
                        for idx, step in enumerate(event.get('plan', [])):
                            step['id'] = f'step_{str(idx+1).zfill(3)}'
                        continue
                    if event_id_parts[5] == '0':
                        continue
                    event_id_number = int(event_id_parts[5]) + current_step_no
                    event_id_parts[5] = str(event_id_number)
                    event['id'] = f"{'-'.join(event_id_parts)}"
                newest_plan = [obj for obj in item.context.pop('plan', []) if obj.get('status', 'todo') != 'todo']
                for idx, step in enumerate(newest_plan):
                    step['id'] =  f'step_{str(idx+1).zfill(3)}'
                    first_task_plan.append(step)
                
                first_task_events += events
        
        if history:
            # for event in first_task_events:
            #     event.pop('current_tool', None)
            # latest_task_events = latest_task.result_json.get('events', [])
            # if shared_link.idx:
            #     latest_task_events = latest_task_events[:shared_link.idx]
            history[0].result_json['events'] = first_task_events 
            for event in history[0].result_json['events']:
                if event.get('type', '') == 'statusUpdate':
                    if (not_fields:=event.get('current_tool', {}).get('params', {}).get('apply_not_fields', [])):
                        for not_field in not_fields:
                            not_field_data = event['current_tool']['params'][not_field]
                            if not not_field_data or type(not_field_data) == dict and not not_field_data.get('data', []):
                                event['current_tool']['params'].pop(not_field, None)
                                continue
                            if type(not_field_data) == list:
                                event['current_tool']['params'][not_field] = {"logic": "not", "data": not_field_data}
                            elif type(not_field_data) == dict:
                                not_field_data['logic'] = 'not'
                                event['current_tool']['params'][not_field] = not_field_data
                if event.get('current_tool', {}).get('tool', '') == 'Generate-Summary' and event.get('type', '') == 'chat':
                    replaced = replace_download_link(event.get('message',''))
                    if replaced:
                        event['message'] = replaced

            
            if thread.agent_name == "planning":
                history = history[:1]
            for task in history:
                if task.thought:
                    try:
                        json.loads(task.thought)
                    except json.JSONDecodeError:
                        try:
                            task.thought = json.dumps(ast.literal_eval(task.thought))
                        except Exception as e:
                            logger.info(f"Failed to process task thought: {e}")
            
        serializer = self.get_serializer(history, many=True)
        return Response(serializer.data)