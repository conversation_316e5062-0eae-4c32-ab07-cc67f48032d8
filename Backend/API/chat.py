import re
import copy
import json
import logging
import time
import uuid
import httpx
import traceback
import tik<PERSON>en
import asyncio

from datetime import datetime, timedelta, timezone
from django.conf import settings
from django.contrib.auth.models import AnonymousUser
from django.http import HttpRequest
from asgiref.sync import sync_to_async
from channels.db import database_sync_to_async
from channels.generic.websocket import (
    AsyncWebsocketConsumer
)
from channels.exceptions import DenyConnection
from django_guid.middleware import process_incoming_request
from django_guid import get_guid

from rest_framework import status
from Account.models import User
from API.models import Thread, Task, Message
from Account.utils import add_back_credits, deduct_credits
from Workflow.testsense import search_conference_by_id
from Config.models import ChatHistoryMessageLimit
from Discover.models import DiscoverArticle
from Workflow.models import WorkflowTask
from Workflow.utils import get_catalyst_info, get_clinical_results, get_drug_info
from utils.fieldenum import (
    EnumTaskMode, EnumTaskStatus
)
from Workflow.methods import process_workflow_message
from Workflow.methods_legacy import process_workflow_message as process_workflow_message_legacy
from Workflow.utils import get_clinical_guideline_background

from API.utils import get_dict_repr_task_and_sub_tasks, get_params_from_latest_task, process_planning_task, validate_planning_task
from API.chat_former import chat_formatter, last_package_formatter, stopped_formatter
from API.throttle import *
from django.core.cache import caches, cache as cache_default
cache = caches['active_task_db'] if 'active_task_db' in caches.settings.keys() else cache_default


logger = logging.getLogger(__name__)

class WSConsumer(AsyncWebsocketConsumer):
    
    async def connect(self):
        request = HttpRequest()
        request.META = {key.decode(): value.decode() for key, value in self.scope['headers']}
        process_incoming_request(request=request)
        # print('scope', self.scope)
        headers_dict = dict((k.lower(), v) for k,v in self.scope['headers'])
        thread_id_bytes = headers_dict.get(b'thread', b'')
        thread_id = thread_id_bytes.decode() if thread_id_bytes else ""
        self.user = self.scope['user']
        self.group_name = '' if not thread_id else 'WS-{}-{}'.format(
            self.scope['user-token'], thread_id
        )
        # await self.channel_layer.group_add(
        #     self.group_name,
        #     self.channel_name
        # )
        self.main_async_task = None
        self.sub_async_tasks = []
        
        if isinstance(self.user, AnonymousUser) and 'error' in self.scope:
            raise DenyConnection(
                '{}: user not authenticated'.format(
                    status.HTTP_401_UNAUTHORIZED
                )
            )
        await self.accept()
        if thread_id:
            await self.channel_layer.group_add(
                self.group_name,
                self.channel_name
            )

    async def disconnect(self, code):
        try:
            if self.group_name:
                await self.channel_layer.group_discard(
                    self.group_name, self.channel_name
                )
        except:
            pass
        if self.main_async_task:
            self.main_async_task.cancel()
            # if hasattr(self, 'thread_id'):
            #     cache.delete(self.thread_id)
            self.channel_layer
        for task in self.sub_async_tasks:
            task.cancel()
        await self.close(code=code)
    
    async def switch_group(self, text_data_json):
        for field_name in ['thread_id', 'workflow_id', 'id']:
            if field_name in text_data_json:
                _id = text_data_json[field_name]
                break
        if not isinstance(self.user, AnonymousUser):
            self.group_name = 'WS-{}-{}'.format(
                self.scope['user-token'], _id
            )
            await self.channel_layer.group_add(
                self.group_name,
                self.channel_name
            )
            
    def get_throttle(self, type):
        """
        Instantiates and returns the throttle that this view uses.
        """
        throttle = None
        if self.user.is_superuser:
            throttle = None
        elif self.user.is_authenticated:
            if type == 'thread':
                throttle = UserChat
            elif type == 'workflow':
                throttle = UserWorkflow
        else:
            if type == 'thread':
                throttle = AnonChat
            elif type == 'workflow':
                throttle = AnonWorkflow
        return throttle
    
    async def message(self, event):
        await self.send(text_data=event['text'])
        
    async def _send(self, text_data):
        if self.group_name:
            await self.channel_layer.group_send(
                self.group_name,
                {"type": "message", "text": text_data},
            )
        else:
            await self.send(text_data=text_data)

    async def receive(self, text_data):
        retry = False
        try:
            if len(text_data) <= 1:
                await self.send(text_data="1")
                return
            
            text_data_json = json.loads(text_data)
            if text_data_json.get('type', '') == 'ping':
                await self.send(text_data="1")
                return
            if isinstance(self.user, AnonymousUser) or 'error' in self.scope:
                await self.send(text_data=json.dumps({
                    'message': 'user not authenticated', 'type': 'error'
                }))
                await self.disconnect(code=4000)
                return
            
            thread_id = text_data_json.get('thread_id', None)
            
            if (type:=text_data_json.get('type', '')):
                if type == 'retry':
                    retry = True
                    if not 'thread_id' in text_data_json:
                        await self.send(text_data=json.dumps({
                            'message': 'missing thread id', 'type': 'error'
                        }))
                        await self.disconnect(code=4000)
                        return
                elif type == 'reconnect':
                    await self.resend_latest_events(text_data_json.get('thread_id', ''))
                    await self.switch_group(text_data_json)
                    return
                elif type in ['stop-auto-run', 'stop']:
                    if type == 'stop-auto-run' and not (planning_task:=text_data_json.get('planning_task', '')):
                        await self.send(text_data=json.dumps({
                            'message': 'missing task id', 'type': 'error'
                        }))
                        await self.disconnect(code=4000)
                        return
                    if thread_id:
                        cache_default.set(f"{thread_id}-{type}", True, timeout=5)
                    # Let the client know we've stopped the auto run
                    msg = 'Stopping auto run' if type == 'stop-auto-run' else 'Stopping'
                    await self.send(text_data=json.dumps(msg))
                    return
                elif type == 'edit':
                    event_id = text_data_json.get('event_id', '')
                    if not thread_id or not event_id:
                        await self.send(text_data=json.dumps({
                            'message': f'missing {'thread' if not thread_id else 'event'} id' , 
                            'type': 'error'
                        }))
                        await self.disconnect(code=4000)
                        return
                    newest_task = await sync_to_async(lambda: Task.objects.filter(thread_id=text_data_json['thread_id']).order_by('-time_created').first())()
                    if newest_task.result_json and (events:=newest_task.result_json.get('events', [])):
                        for event in events:
                            if event.get('id', '') == event_id:
                                if event.get('editable', '') == True:
                                    event['editable'] = False
                                    feedback = text_data_json.get('feedback', '') or text_data_json.get('message', '')
                                    if feedback is not True:
                                        event['message'] = event['rewrite_question'] = feedback
                                        rewrite_results = newest_task.context.get('rewrite_results', [])
                                        rewrite_results.append(event['message'])
                                        newest_task.context['rewrite_results'] = rewrite_results
                                        newest_task.context['rewrite_question'] = event['rewrite_question']
                                    newest_task.context['editable'] = False
                                    await self.send(text_data=json.dumps(event))
                                    break
                        await sync_to_async(newest_task.save)()
                    text_data_json = {
                        "type": "confirmTool",
                        "approve": True,
                        "feedback": text_data_json.get('feedback', ''),
                        "agent": "planning",
                        "thread_id": thread_id,
                        "planning_task": newest_task.id
                        }
                                
            else:
                text_data_json['type'] = 'chat'
        except Exception as e:
            traceback.print_exc()
            await self.send(text_data=json.dumps({
                        'message': str(e), 'type': 'error'
                    }))
            await self.disconnect(code=4000)
            return
        if (thread_id:=text_data_json.get('thread_id', '')):
            if cache.get(thread_id):
                planning_task = text_data_json.get('planning_task', None)
                if not planning_task:
                    await self.send(text_data=json.dumps({
                        'message': 'active task, please wait for completion', 'type': 'error'
                        }))
                    await self.disconnect(code=4000)
                    return
                elif text_data_json.get('agent', '') == 'planning':
                    cache_default.set(thread_id+'-stop-auto-run', False, timeout=60*10)
                return
            self.thread_id = thread_id
        
        if text_data_json.get('agent', '') == 'planning':
            if not text_data_json.get('planning_task','') and not text_data_json.get('question',''):
                await self.send(text_data=json.dumps({
                        'message': 'missing question', 'type': 'error'
                    }))
                await self.disconnect(code=4000)
            if text_data_json.get('mock', False):
                self.main_async_task = asyncio.create_task(self.mock_planning(text_data_json))
                return
            self.main_async_task = asyncio.create_task(self.process_chat_message(text_data_json))
        elif 'question' in text_data_json or retry:
            self.main_async_task = asyncio.create_task(self.process_chat_message(text_data_json))
        else:
            await self.send(text_data=json.dumps({
                        'message': 'unkwown message', 'type': 'error'
                    }))
            await self.disconnect(code=4000)
    
    async def process_chat_message(self, text_data_json):
        # restrict question length
        throttle = self.get_throttle('thread')
        if throttle:
            throttle = throttle()
            if not throttle.allow_call(self.user, self.scope):
                print("chat rate limit exceeded")
                err_msg = 'Rate limit exceeded'
                await self.send(text_data=json.dumps({
                    'message': err_msg, 'type': 'error'
                }))
                if not self.user.is_authenticated:
                    await self.disconnect(code=4029)
                return
        thread_id = text_data_json.get('thread_id', "")
        type = text_data_json.get('type', 'chat')
        focused_task = None
        if not isinstance(self.user, AnonymousUser):
            self.group_name = 'WS-{}-{}'.format(
                self.scope['user-token'], thread_id
            )
            await self.channel_layer.group_add(
                self.group_name,
                self.channel_name
            )
        try:
            if type == 'retry':
                question, agent_name, owner, thread, focused_task, error = await get_params_from_latest_task(thread_id)
                if not question:
                    await self.send(text_data=json.dumps({
                        'message': error, 'type': 'error'
                    }))
                    await self.disconnect(code=4000)
                    return
            
            else:
                thread = None
                question = ''
                if 'planning_task' in text_data_json and text_data_json['planning_task']:
                    task_id = text_data_json['planning_task']
                    focused_task = await sync_to_async(Task.objects.get)(id=task_id)
                    # thread = await sync_to_async(lambda: focused_task.thread)()
                    question, error = await validate_planning_task(text_data_json)
                    if error:
                        await self.send(text_data=json.dumps({
                            'message': error, 'type': 'error'
                        }))
                        await self.disconnect(code=4000)
                        return
                thread, owner, agent_name = await self.get_thread_owner_agent(thread_id)
                if not question: question = text_data_json.get('question','')[:Task.question.field.max_length]
                try: agent_name = text_data_json['agent']
                except: pass
            if owner and owner != self.user:
                await self.send(text_data=json.dumps({
                    'message': 'access denied', 'type': 'error'
                }))                
                await self.disconnect(code=4001)
                return
            async def check_report_owner_and_status(owner, thread: Thread):
                if not thread.context:
                    return ""
                is_report = 'report' in thread.context.get('reference', '')
                if not is_report:
                    return ""
                if not owner:
                    return "thread is public" 
                status = thread.context.get('status', '')
                if status and status != 'init':
                    if status == 'complete':
                        return "completed, cannot retry"
                    if status != 'failed':
                        return "not in failed state, cannot retry" 
                    timed_out = (thread.time_active or thread.time_created) + timedelta(minutes=15) < datetime.now(timezone.utc)
                    if not timed_out:
                        return "not timed out, cannot retry"
                thread.context['status'] = 'processing'
                await sync_to_async(thread.save)()
                return ""
            error = await check_report_owner_and_status(owner, thread)
            if error:
                await self.send(text_data=json.dumps({
                    'message': error, 'type': 'error'
                }))                
                await self.disconnect(code=4001)
                return
                
        except Thread.DoesNotExist as e:
            await self.send(text_data=json.dumps({
                'message': str(e), 'type': 'error'
            }))
            await self.disconnect(code=4002)
            return
        except Exception as e:
            # DEBUG ONLY
            traceback.print_exc()
            await self.send(text_data=json.dumps({
                'message': str(e), 'type': 'error'
            }))
            await self.disconnect(code=4002)
            return
        try:
            context = text_data_json.get("context", {})
            context = context if context else {}
            reference_ids = context.get("ids", [])
            reference = (thread.context or {}).get("reference", "")
            reference_type = (thread.context or {}).get("reference_type", "")
            max_refer_len = {"drug-compete": 100, "clinical-result": 10, "catalyst": 10}
            max_type_len = {"conference": 10}
            if not context and thread and thread.context:
                reference = thread.context.get("reference", "") if not reference else ""
                reference_type = thread.context.get("reference_type", "") if not reference_type else ""
            if reference and reference in max_refer_len and len(reference_ids) > max_refer_len[reference]:
                await self.send(text_data=json.dumps({
                    'message': f'too many references, please select at most {max_refer_len[reference]}', 'type': 'error'
                }))
                await self.disconnect(code=4002)
                return
            if reference_type and reference_type in max_type_len and len(reference_ids) > max_type_len[reference_type]:
                await self.send(text_data=json.dumps({
                    'message': f'too many references, please select at most {max_type_len[reference_type]}', 'type': 'error'
                }))
                await self.disconnect(code=4002)
                return

            params = {
                'email': self.user.email if self.user.is_authenticated else '',
                'channel_name': self.channel_name,
                'group_name': self.group_name,
                'question': question,
                'thread_id': thread_id,
                "thread": thread,
                "task": focused_task,
                "retry": type == 'retry',
                "context": context,
                "reference_ids": reference_ids,
                "query": context.get("query", {}),
                # mindsearch agent params
                'agent_name': agent_name,
                'params': text_data_json,
            }
            
            logger.info(f"Chat input params: {params}")
            cache.set(thread_id, True, timeout=60*20)
            await asyncio.shield(self.run_chat_task(params))
            cache.delete(thread_id)
        except Exception as e:
            traceback.print_exc()
            if hasattr(self, 'thread_id'):
                cache.delete(self.thread_id)
            await self.send(text_data=json.dumps({
                'message': str(e), 'type': 'error'
            }))
            await self.disconnect(code=4003)

    @database_sync_to_async
    def get_thread_owner_agent(self, thread_id):
        thread = Thread.objects.get(id=thread_id)
        return thread, thread.owner, thread.agent_name

    @database_sync_to_async
    def get_task_set(self, thread):
        return list(thread.task_set.all().order_by('time_created'))

    async def run_chat_task(self, event):
        deducted = False
        is_followup = False
        try:
            noah_agent_url, data, message_id, msg_relations, thread, task, headers, task_created = await self._get_agent_input(event=event)
            is_followup = (thread.task_count > 1)
            deducted = await deduct_credits(self.user, agent_name=event['agent_name'], is_followup=is_followup)
            rslt = None
            prev_steps = []
            task_set = await self.get_task_set(thread)
            if task_set:
                for _task in task_set:
                    if _task == task:
                        continue
                    try:
                        plan = _task.context.get('plan', [])
                        prev_steps += [item for item in plan if item.get('status', 'todo') != 'todo']
                    except:
                        pass
            # rslt, msg, json_chunk = 
            buffer_obj = {}
            coroutine = self.call_agent(noah_agent_url, data, headers, message_id, msg_relations, event, 
                                  task, prev_steps, buffer_obj)
            # async for _ in self._task_with_heartbeat(coroutine, interval=1, thread_id=str(thread.id)):
            coroutine_task = asyncio.create_task(coroutine)
            while not coroutine_task.done():
                if cache_default.get(f"{str(thread.id)}-stop"):
                    
                    buffer_obj['stop'] = True
                    coroutine_task.cancel()
                    cache_default.delete(f"{str(thread.id)}-stop")
                    break
                await asyncio.sleep(0.3)
            rslt, msg, json_chunk = buffer_obj.get('rslt', None), buffer_obj.get('msg', None), buffer_obj.get('json_chunk', None)
            if buffer_obj.get('stop', False):
                if rslt and rslt.get('agent', None) == 'planning':
                    plan = task.context.get('plan', [])
                    current_step = rslt.get('current_step', 0) or task.context.get('current_step', 0)
                    for i, step in enumerate(plan):
                        if step.get('status', 'done') != 'done':
                            plan[i]['status'] = 'error'
                            _rslt = {   
                                "saveChat": True,
                                "agent": "planning",
                                "id": "0",
                                "plan": plan,
                                "sender": "assistant",
                                "thread_id": self.thread_id,
                                "type": "planUpdate"
                            }
                            await process_planning_task(_rslt, task, prev_steps)
                            rslt_text = json.dumps(_rslt, ensure_ascii=False)
                            await self._send(text_data=rslt_text)
                            break
                    if rslt.get('type', '') == 'chat':
                        rslt['saveChat'] = True
                        rslt['message'] += '\n\nUser interruption'
                        rslt['current_step'] = current_step
                        _rslt = rslt
                    else:
                        _rslt = {   
                            "saveChat": True,
                            "agent": "planning",
                            "sender": "assistant",
                            "message": "User interruption",
                            "current_step": current_step,
                            "thread_id": self.thread_id,
                            "type": "chat",
                            "startedAt": int(time.time())
                        }
                    await process_planning_task(_rslt, task, prev_steps)
                    rslt_text = json.dumps(_rslt, ensure_ascii=False)
                    await self._send(text_data=rslt_text)
                    _rslt = {   
                        "saveChat": True,
                        "agent": "planning",
                        "sender": "assistant",
                        "current_step": current_step,
                        "thread_id": self.thread_id,
                        "agentStatus": "running",
                        "type": "statusUpdate"
                    }
                    await process_planning_task(_rslt, task, prev_steps)
                    rslt_text = json.dumps(_rslt, ensure_ascii=False)
                    await self._send(text_data=rslt_text)
                    cache.delete(str(thread.id))
                    return
                
            if rslt is None:
                rslt = {"error": "No response received from the server"}
                
            if thread:
                thread.time_active = datetime.utcnow()
                if not thread.context:
                    thread.context = {}
                if (attachments:=rslt.get('attachments_key', {})):
                    thread.context['attachments_key'] = attachments
                if 'report' in thread.context.get('reference', ''):
                    thread.context['status'] = ('failed' if 'error' in rslt else 'complete')
                await sync_to_async(thread.save)()
                
            planning = (rslt.get('agent', None) == 'planning')
            # save message
            if task_created:
                try:
                    if msg: await sync_to_async(msg.save)()
                except Exception as e:
                    logger.warning('save message failed', e)
                # update task status
                if planning:
                    cache.delete(str(thread.id))
                    if buffer_obj.get('stop', False):
                        await self._send(text_data=json.dumps("Stopped", ensure_ascii=False))
                    return
                try:
                    if json_chunk: await self._update_task(json_chunk, task)
                except Exception as e:
                    logger.warning('update task failed', e)

            # update task status
            if 'error' in rslt and rslt['error']:
                task.task_status = EnumTaskStatus.Error.value
                task.time_finished = datetime.utcnow()
                await sync_to_async(task.save)()
                if planning:
                    cache.delete(str(thread.id))
                raise Exception(rslt['error'])
            if planning:
                cache.delete(str(thread.id))
                if buffer_obj.get('stop', False):
                    await self._send(text_data=json.dumps("Stopped", ensure_ascii=False))
                    task.task_status = EnumTaskStatus.Abandon.value
                    task.time_finished = datetime.utcnow()
                await sync_to_async(task.save)()
                return
            
            # send DONE status
            await self._send(text_data=last_package_formatter(rslt))
            await self._send(text_data=stopped_formatter(rslt, task))
            task.time_finished = datetime.utcnow()
            task.task_status = EnumTaskStatus.Complete.value
            await sync_to_async(task.save)()
            cache.delete(str(thread.id))
            # thread.messages = full_messages_json
        except Exception as exc:
            # traceback.print_exc()
            # DEBUG ONLY
            # await self._send(text_data=json.dumps({
            #     'error': str(e),
            # }))
            # update task status as failed
            try:
                if task is not None and task.task_status != EnumTaskStatus.Complete.value:
                    task.task_status = EnumTaskStatus.Failed
                    await sync_to_async(task.save)()
            except Exception as e:
                print("attempt to set task to fail failed", e)
            if hasattr(self, 'thread_id'):
                cache.delete(self.thread_id)
            if deducted:
                await add_back_credits(self.user, credits=deducted, agent_name=event['agent_name'], is_followup=is_followup)
            raise exc
        

    @database_sync_to_async
    def create_task(self, email, parent, question, agent_name):
        try:
            user = User.objects.get(email=email)
            ip = self.scope['client'][0]
        except:
            user = None
            ip = None
        task_obj = {
            "owner": user,
            "ip": ip,
            "question": question,
            "agent_name": agent_name,
            "context": self.event.get('context', {}),
        }
        if agent_name == 'investment_report': 
            task_obj['question'] = f"Investment Analysis for {question}"
        task_count = 0
        if type(parent) == Task:
            task_obj["parent_task"] = parent
        elif type(parent) == Thread:
            task_obj["thread"] = parent
            task_count = parent.task_count or 0
            parent.task_count = task_count + 1
        query = self.event.get('query', {})
        parameter_json = self.event.get('params', {})
        if query: parameter_json['query'] = query
        if parameter_json:
            task_obj['parameter_json'] = parameter_json
        parent.save()
        return Task.objects.create(
            **task_obj, idx = task_count
        )
    
    async def _get_agent_input(self, event):
        # get init params
        retry = event['retry']
        thread = event['thread']
        email = event['email']
        question = event['question']
        task = event['task']
        agent_name = event['agent_name']
        thread_agent_name = thread.agent_name or ''
        parent =  thread if retry else thread or task
        noah_agent_url = f"{settings.EXTERNAL_API['NOAH_AGENT_HOST']}/chat"
        message_id = str(uuid.uuid4())
        task_created = False

        # init global context
        self.event = event
        self.agent_name = agent_name
        
        # Create task under thread
        if not retry and thread and not task:
            task = await self.create_task(email, thread, question, agent_name)
            task_created = True

        # init input params for request context, i.e. language, model, background
        event_params = event.get('params', {})
        params = {
            'language': event_params.get('language', 'EN'),
            'model': event_params.get('model', ''),
            'enable_rag': event_params.get('enable_rag', True),
        }

        # get history messages and background
        history_messages = await self._get_history_messages(agent_name, parent)
        background = await self._get_reference(agent_name, params, event, parent)
        params['background'] = background

        # agent input data
        data = {
            "user_prompt": question,
            "history_messages":history_messages,
            "agent": agent_name,
            "params": params,
            "is_hitl": event_params.get('isHitl', False),
        }
        if agent_name == 'investment_report':
            data['user'] = self.user.email if self.user.is_authenticated else 'anon'
        tasks = await database_sync_to_async(lambda: list(thread.task_set.order_by('-time_created')))()
        limit = 3
        if thread_agent_name == 'planning':
            data['is_hitl'] = True
            for field in ['current_tool', 'hitl_mode']:
                if field in event['params'] and event['params'][field]:
                    data[field] = event['params'][field]
            if agent_name == 'planning':
                data.update(event_params)
                data.pop('params')
                data['planning_task'] = task.context or {}
                data['planning_task']['id'] = str(task.id)
                data['planning_task']['thread_id'] = str(thread.id)
                data['planning_task']['user'] = self.user.email if self.user.is_authenticated else 'anon'
                data['planning_task']['taskStart'] = int(task.time_created.timestamp())
                tool_uses = data['planning_task'].get('tool_uses', [])
                if not tool_uses:
                    data['planning_task']['tool_uses'] = await self._get_tool_uses(tasks, limit)
            else:
                data['history_messages'] = await self._get_history_messages_v2(tasks, limit*2)
        else:
            data['history_messages'] = await self._get_history_messages_v2(tasks, limit*2)
                
        logger.info(f"chat input agent {agent_name} and data {self.truncate_dict_values(data)}")

        # save message's task
        msg_relations = {
            "thread": thread,
            "task": task,
            "owner": self.user if self.user.is_authenticated else None
        }
        msg_params = {"role": "user", "content": question}
        if not retry and task_created:
            msg = Message(**msg_params, **msg_relations)
            try:
                await sync_to_async(msg.save)()
            except Exception as e:
                print(e)
        #logger.info(f'chat input data {data}')

        headers = {
            'X-Correlation-ID': get_guid(),
        }
        logger.info(f"Current: {agent_name} thread: {thread.id}")

        return noah_agent_url, data, message_id, msg_relations, thread, task, headers, task_created


    def _truncate_buffer(self, buffer: str) -> tuple[str, str]:
        open_braces = 0
        json_start = -1
        
        for i, char in enumerate(buffer):
            if char in '{':
                if open_braces == 0:
                    json_start = i
                open_braces += 1
            elif char in '}':
                open_braces -= 1
            
            if open_braces == 0 and json_start != -1:
                json_str = buffer[json_start:i + 1]  # 提取完整的 JSON 字符串
                buffer = buffer[i + 1:]  # 更新 buffer，去掉已提取的部分
                return buffer, json_str
        
        return buffer, ''


    def _process_chunk(self, message_id, str_chunk, msg_relations, event) -> tuple[dict, Message, dict]:
        msg_params = {
            'id': message_id
        }
        json_chunk = json.loads(str_chunk)
        if 'role' in json_chunk and 'content' in json_chunk:
            if 'tool_calls' in json_chunk and json_chunk['tool_calls']:
                msg_params['role'] = 'tool'
                msg_params['content'] = json_chunk['tool_calls']
                msg = Message(**msg_params, **msg_relations)
            else:
                msg_params['role'] = json_chunk['role']
                msg_params['content'] = json_chunk['content']
                msg = Message(**msg_params, **msg_relations)
        else:
                msg_params['role'] = 'tool'
                msg_params['content'] = json_chunk
                msg = Message(**msg_params, **msg_relations)
        if 'error' in json_chunk and json_chunk['error']:
            rslt = {"error": json_chunk['error']}
            return rslt, msg, {}
        rslt = chat_formatter(event, msg, json_chunk, msg_params)
        return rslt, msg, json_chunk
    
    async def _get_history_messages(self, agent_name: str, parent):
        history_message_limit = 5 if not await sync_to_async(ChatHistoryMessageLimit.objects.exists)() else await sync_to_async(lambda: ChatHistoryMessageLimit.objects.first().limit)()
        # we have to use -time_created to get the latest messages
        if type(parent) == Task: thread = await sync_to_async(lambda: parent.thread)()
        else: thread = parent
        history_messages_objs = await sync_to_async(lambda: 
                                                    list(Message.objects.filter(thread=thread).exclude(role='tool').order_by('-time_created')[:history_message_limit]))()
        history_messages_objs.reverse()
        history_messages = [{"role": obj.role, "content": obj.content} for obj in history_messages_objs]
        # make sure the first message role is user, to avoid llm hallucination
        user_start = next((i for i, msg in enumerate(history_messages) if msg['role'] == 'user'), None)
        history_messages = history_messages[user_start:] if user_start is not None else history_messages
        logger.info(f"Chat fetch history messages agent_name: {agent_name} parent_context: {parent.context} history_messages_count: {len(history_messages)}")
        return history_messages
    
    async def _get_tool_uses(self, tasks: list, limit):
        # thread.task_set.
        if not tasks:
            return []
        tool_uses = tasks[0].context.get('tool_uses', []) if tasks[0].context else []
        tool_uses = tool_uses[::-1]
        for t in tasks[1:]:
            if len(tool_uses) >= limit:
                break
            if t.agent_name == 'planning' and t.context:
                question = t.context.get('rewrite_question', '') or t.context.get('question', '')
                if (task_tool_uses:=t.context.get('tool_uses', None)) is not None:
                    rem = limit - len(tool_uses)
                    tool_uses += task_tool_uses[-rem::-1]
                    if question:
                        user_question_tool_use = {"tool": "User-Question", "question": question}
                        tool_uses.append(user_question_tool_use)
            elif t.agent_name != 'planning' and t.response and limit - len(tool_uses) > 0:
                search_tool_use = {"tool": "Web-Search", "result": t.response}
                tool_uses.append(search_tool_use)
                if t.question:
                    user_question_tool_use = {"tool": "User-Question", "question": t.question}
                    tool_uses.append(user_question_tool_use)
        return tool_uses[::-1]
                    
    async def _get_history_messages_v2(self, tasks: list, limit):
        history_messages = []
        for t in tasks:
            if t.agent_name == 'planning':
                question = t.context.get('rewrite_question', '') or t.context.get('question', '')
                if (tool_uses:=t.context.get('tool_uses', None)) is not None:
                    if len(history_messages) >= limit:
                        break
                    rem = limit - len(history_messages)
                    # data['planning_task']['tool_uses'] = tool_uses[-rem:] + data['planning_task']['tool_uses']
                    for tool_use in (rem_tools:=tool_uses[-rem:]):
                        content = tool_use.get('result', '')
                        if type(content) == dict:
                            content = content.get('content', str(content))
                        history_messages.append({
                            "role": "assistant",
                            "content": content,
                        })
                    if question and rem_tools and rem_tools[0].get('tool', '') != 'User-Question':
                        user_question_tool_use = {"role": "user", "content": question}
                        history_messages.append(user_question_tool_use)
            else:
                if t.response and limit - len(history_messages) > 0:
                    response_msg = {"role": "assistant", "content": t.response or ""}
                    history_messages.append(response_msg)
                    if t.question:
                        user_question_msg = {"role": "user", "content": t.question}
                        history_messages.append(user_question_msg)
        return history_messages[::-1]
    
    async def _get_reference(self, agent_name:str, params: dict, event: dict, parent) -> str:
        if not agent_name or not agent_name.startswith('mindsearch'):
            return ''

        if type(parent) == Thread:
            if parent.context is None or 'reference' not in parent.context or 'reference_type' not in parent.context:
                return ''
            reference = parent.context['reference']
            reference_type = parent.context['reference_type']
        else: # type(parent) == Task
            if not event['context']:
                return ''
            try: reference = event['context']['reference']
            except: pass
            try: reference_type = event['context']['reference_type']
            except: pass
        
        params['reference'] = reference
        params['reference_type'] = reference_type

        if reference_type == 'tool':
            reference_ids = event.get('reference_ids', [])
            if reference == 'drug-compete':
                
                data = {'filters': {'unique_key': reference_ids}, 'limit': len(reference_ids)}
                try:
                    drug_info = get_drug_info(data)
                except Exception as e:
                    #traceback.print_exc()
                    logger.warn(f"Query clinical_results failed: {str(e)}", exc_info=True)
                    return None
                return f"Selected drug data: {drug_info}"
            elif reference == 'clinical-result':
                data = {'filters': {'id': reference_ids}, 'limit': len(reference_ids)}
                try:
                    clinical_results = get_clinical_results(data, include_events=True)
                    for result in clinical_results:
                        if 'id' in result:
                            result.pop('id')
                except Exception as e:
                    #traceback.print_exc()
                    logger.warn(f"Query clinical_results failed: {str(e)}", exc_info=True)
                    return ''
                return f"Selected clinical trial result data: {clinical_results}"
            elif reference == 'catalyst':
                data = {'filters': {'id': reference_ids}, 'limit': len(reference_ids)}
                try:
                    catalyst_info = get_catalyst_info(data)
                    for result in catalyst_info:
                        if 'id' in result:
                            result.pop('id')
                except Exception as e:
                    #traceback.print_exc()
                    logger.warn(f"Query catalyst_info failed: {str(e)}", exc_info=True)
                    return ''
                return f"Selected catalyst data: {catalyst_info}"
            try:
                workflow = await sync_to_async(WorkflowTask.objects.get)(id=reference)
            except Exception as exc:
                traceback.print_exc()
                logger.warn(f"[Chat] query workflow failed and meet issue {exc}")
            else:
                truncated, attachments = self._check_token_length(self._dict_to_xml_recurse(workflow.attachments, 'worflow_trail'))
                if truncated:
                    attachments = []
                    for attachment in workflow.attachments[0]['data']:
                        attachment.pop('events', None)
                        attachments.append(attachment)
                    _, attachments = self._check_token_length(self._dict_to_xml_recurse(attachments, 'worflow_trail'))
                return f"Workflow trail sheet result: {attachments}"
        elif reference_type == 'discover' or reference_type == 'discoverArticle':
            try:
                discover_artical = await sync_to_async(DiscoverArticle.objects.get)(id=reference)
                outlines = await sync_to_async(lambda: discover_artical.outlines)()
            except Exception as exc:
                traceback.print_exc()
                logger.warn(f"[Chat] query discover articals failed and meet issue {exc}")
            else:
                outline_contents = [f"{outline.get('title', '')}\n{outline.get('content', '')}\n\n" for outline in outlines]
                artical = f"Title: {discover_artical.title},\n Abstract: {discover_artical.abstract},\n Content:  {' '.join(outline_contents)}"
                reference_link = [f"""id: {reference.get('id', '')} title: {reference.get('title', '')} url: {reference.get('link', '')} summary: {reference.get('summary', '')}\n"""
                                  for reference in discover_artical.references]
                artical += f"# Reference\n {reference_link}"
                _, artical = self._check_token_length(artical)
                return f"Selected article content: {artical}"
        elif reference_type == 'conference':
            try:
                reference_ids = event.get('reference_ids', [])
                attachments = search_conference_by_id(reference_ids, reference)
                print(f"Selected conference data: {attachments}")
                return f"Selected conference data: {attachments}"
            except Exception as e:
                traceback.print_exc()
        elif reference_type == 'clinical-guideline':
            try:
                reference_ids = event.get('reference_ids', [])
                guideline_detail = get_clinical_guideline_background(reference, reference_ids)
                guideline_detail_str = json.dumps(guideline_detail)
                logger.info(f"Selected clinical guideline data:  {guideline_detail_str[:500]}")
                return guideline_detail_str
            except Exception as e:
                traceback.print_exc()
        return ''
    
    def _dict_to_xml_recurse(self, data, parent_tag='root'):
        xml = f'<{parent_tag}>'
        if isinstance(data, dict):
            for key, value in data.items():
                if isinstance(value, dict):
                    xml += self._dict_to_xml_recurse(value, key)
                elif isinstance(value, list):
                    for item in value:
                        if isinstance(item, dict):
                            xml += self._dict_to_xml_recurse(item, key)
                        else:
                            xml += f'<{key}>{item}</{key}>'
                else:
                    xml += f'<{key}>{value}</{key}>'
        elif isinstance(data, list):
            for item in data:
                if isinstance(item, dict):
                    xml += self._dict_to_xml_recurse(item, 'item')
                else:
                    xml += f'<item>{item}</item>'
        else:
            xml += str(data)
        
        xml += f'</{parent_tag}>'
        return xml

    def _check_token_length(self, message: str) -> tuple[bool, str]:
        encoding = tiktoken.encoding_for_model("gpt-4")
        tokens = encoding.encode(message)
        max_tokens = 64 * 1024
        if len(tokens) >= max_tokens:
            truncated_tokens = tokens[:max_tokens]
            return True, encoding.decode(truncated_tokens)
        return False, message
        
        
    async def _update_task(self, rslt: dict, task: Task):
        task.task_status = EnumTaskStatus.Complete
        task.response = rslt.get('content', '')
        task.thought = rslt.get('thought', '')
        if not task.thought:
            search_graph = rslt.get('search_graph', {})
            task.thought = json.dumps(search_graph) if search_graph else ''
        task.result_json.update(rslt)
        if 'followup_questions' in rslt:
            task.result_json['followup_questions'] = rslt.get('followup_questions')
        await sync_to_async(task.save)()

    def truncate_dict_values(self, d, max_length=300, truncation_suffix="..."):
        if isinstance(d, dict):
            truncated_dict = {}
            for key, value in d.items():
                truncated_dict[key] = self.truncate_dict_values(value, max_length, truncation_suffix)
            return truncated_dict
        elif isinstance(d, list):
            return [self.truncate_dict_values(item, max_length, truncation_suffix) for item in d]
        elif isinstance(d, str) and len(d) > max_length:
            return d[:max_length] + truncation_suffix
        else: 
            return d

    async def mock_planning(self, text_data_json):
        text_data_json['approve'] = True
        await self.process_chat_message(text_data_json)
        task = await sync_to_async(lambda: Task.objects.filter(thread_id=text_data_json['thread_id']).order_by('-time_created').first())()
        text_data_json.pop('question', '')
        text_data_json['planning_task'] = str(task.id)
        plan = task.context.get('plan', [])
        text_data_json.pop('feedback', '')
        for _ in range(len(plan)):
            await asyncio.sleep(1)
            await self.process_chat_message(text_data_json)
        
        cache.delete(text_data_json.get('thread_id', ''))
        
    async def resend_latest_events(self, thread_id):
        if thread_id and await database_sync_to_async(lambda: Task.objects.filter(thread_id=thread_id).exists())():
            latest_task = await sync_to_async(lambda: Task.objects.filter(thread_id=thread_id).order_by('-time_created').first())()
            if latest_task and latest_task.result_json:
                latest_tasks = latest_task.result_json.get('events', [])
                if latest_tasks:
                    for event in latest_tasks:
                        await self.send(text_data=json.dumps(event))
                        await asyncio.sleep(0.01)

    async def call_agent(self, noah_agent_url, data, headers, message_id, msg_relations, event, task, prev_steps, buffer_obj):
        buffer = ""
        rslt = {}
        async with httpx.AsyncClient() as client:
            async with client.stream('POST', noah_agent_url, data=json.dumps(data), headers=headers, timeout=900) as r:
                async for chunk in r.aiter_lines():  # or, for line in r.iter_lines():
                    if buffer_obj.get('stop', False):
                        break
                    buffer += chunk
                    try:
                        json.loads(buffer)
                        buffer, json_str = '', buffer
                    except:
                        buffer, json_str = self._truncate_buffer(buffer)
                    if json_str == '':
                        continue
                    try:
                        rslt, msg, json_chunk = self._process_chunk(message_id, json_str, msg_relations, event)
                        buffer_obj['rslt'] = rslt
                        buffer_obj['msg'] = msg
                        buffer_obj['json_chunk'] = json_chunk
                        # Create sub tasks for planning task
                        if 'error' in rslt and rslt['error']:
                            buffer_obj['rslt'] = rslt = {"error": rslt['error']}
                            break
                        # Planning/HITL related sub task creation
                        if rslt.get('agent', None) == 'planning':
                            await process_planning_task(rslt, task, prev_steps)
                            if rslt.get('hide', False):
                                continue
                        if rslt:
                            rslt_text = json.dumps(rslt, ensure_ascii=False)
                            await self._send(text_data=rslt_text)
                    except json.decoder.JSONDecodeError as exc:
                        traceback.print_exc()
                        continue
                    except Exception as exc:
                        traceback.print_exc()
                        continue
        