import json
import time
from API.models import Message, Task

def planning_formatter(event:dict, msg: Message, json_chunk: dict, msg_params:dict, **kwargs) -> dict:
        
    rslt = {
        'agent': 'planning',
        'question': event['question'],
        'thread_id': event['thread_id'],
        **json_chunk
    }
    rslt['startedAt'] = rslt.get('startedAt', int(time.time()) - 1)
    
    if 'error' in json_chunk:
        rslt['error'] = json_chunk['error'] 

    return rslt

def mindsearch_formatter(event:dict, msg: Message, json_chunk: dict, msg_params:dict, **kwargs) -> dict:
    rslt = {
        'agent': event['agent_name'],
        'id': msg.id,
        'task_id': str(msg.task.id),
        'question': event['question'],
        'response': json_chunk.get("content", ""),
        'status': 'CONTINUE',
        'thread_id': event['thread_id'],
        'type': 'CONTENT',
        'upload_files': [],
        'followup_questions': json_chunk.get("followup_questions", []),
    }
    if 'search_graph' in json_chunk and json_chunk['search_graph']:
        rslt['thought'] = json.dumps(json_chunk['search_graph'], ensure_ascii=False)
        if json_chunk['search_graph'].get("attachments_key", ""):
            rslt['attachments_key'] = json_chunk['search_graph']['attachments_key']
    # if json_chunk.get("saveChat", False):
    #     rslt['saveChat'] = True

    return rslt

def content_only_formatter(event:dict, msg: Message, json_chunk: dict, msg_params:dict, **kwargs) -> dict:
    return {
        'agent': event['agent_name'],
        'id': msg.id,
        'task_id': str(msg.task.id),
        'question': event['question'],
        'response': json_chunk.get("content", ""),
        'status': 'CONTINUE',
        'thread_id': event['thread_id'],
        'type': 'CONTENT',
    }

def hitl_formatter(event:dict, msg: Message, json_chunk: dict, msg_params:dict, **kwargs) -> dict:
    
    startedAt = int(time.time())
    message = event['question']

    type = 'chat'
    id = f'{msg.id}-0-q'
    chunk_id = 0
    sender = 'assistant'
    if json_chunk['processing_type'] == 0:
        chunk_id = 1
        type = 'chat'
        sender = 'user'
        message = event['question']

    if 'search_graph' in json_chunk and json_chunk['search_graph']:
        message = json.dumps(json_chunk, ensure_ascii=False)
        thought_process = json_chunk['search_graph'].get('thought_process', '')
        if thought_process:
            type = 'thought'
            id = f'{msg.id}-0-t'
            if chunk_id == 1:
                startedAt = int(time.time())
            chunk_id = 2

    if 'content' in json_chunk and json_chunk['content']:
        type = 'chat'
        id = f'{msg.id}-0-c'
        message = json_chunk['content']
        if chunk_id == 2:
            startedAt = int(time.time())
        chunk_id = 3

    # TODO remove this part
    if json_chunk['processing_type'] == 7 \
        and 'search_graph' in json_chunk \
        and 'source' in json_chunk['search_graph'] \
        and len(json_chunk['search_graph']['source']) > 0:
        type = 'reference'
        id = f'{msg.id}-0-r'
        message = json.dumps(json_chunk['search_graph']['source'], ensure_ascii=False)
        if chunk_id == 3:
            startedAt = int(time.time())
        chunk_id = 4

    if json_chunk['processing_type'] == 8 \
        and 'followup_questions' in json_chunk \
        and len(json_chunk['followup_questions']) > 0:
        type = 'followupQuestion'
        id = f'{msg.id}-0-f'
        message = json.dumps(json_chunk['followup_questions'], ensure_ascii=False)
        if chunk_id == 4:
            startedAt = int(time.time())
        chunk_id = 5

    rslt = {
        'agent': event['agent_name'],
        'thread_id': event['thread_id'],
        'type': type, 
        'hitl_mode': 'always', # no used by frontend
        'sender': sender,
        'translations': {}, # no used by frontend
        'chunkIdx': chunk_id,
        'message': message,
        'id': id,
        'startedAt': startedAt,
    }

    return rslt

def hitl_task_formater(task: Task):
    json_chunk = task.result_json
    events = []
    # user question
    events.append({
        'agent': task.agent_name,
        'thread_id': task.thread.id,
        'type': 'chat', 
        'hitl_mode': 'always', # no used by frontend
        'sender': 'user',
        'translations': {}, # no used by frontend
        'chunkIdx': 0,
        'message': task.question,
        'id': f'{task.id}-0-q',
        'startedAt': task.time_created,
    })
    # search graph
    search_graph = json_chunk.get('search_graph', {})
    if search_graph.get('thought_process', ''):
        message = json.dumps({'search_graph': search_graph}, ensure_ascii=False)
        events.append({
            'agent': task.agent_name,
            'thread_id': task.thread.id,
            'type': 'thought', 
            'hitl_mode': 'always', # no used by frontend
            'sender': 'assistant',
            'translations': {}, # no used by frontend
            'chunkIdx': 1,
            'message': message,
            'id': f'{task.id}-0-t',
            'startedAt': task.time_created,
        })
    # content
    events.append({
        'agent': task.agent_name,
        'thread_id': task.thread.id,
        'type': 'chat', 
        'hitl_mode': 'always', # no used by frontend
        'sender': 'assistant',
        'translations': {}, # no used by frontend
        'chunkIdx': 2,
        'message': task.response,
        'id': f'{task.id}-0-c',
        'startedAt': task.time_created,
    })
    # reference
    if len(search_graph.get('source', [])) > 0:
        source = json.dumps(search_graph['source'], ensure_ascii=False)
        events.append({
            'agent': task.agent_name,
            'thread_id': task.thread.id,
            'type': 'reference', 
            'hitl_mode': 'always', # no used by frontend
            'sender': 'assistant',
            'translations': {}, # no used by frontend
            'chunkIdx': 3,
            'message': source,
            'id': f'{task.id}-0-r',
            'startedAt': task.time_created,            
        })
    # followup question
    if len(task.result_json.get('followup_questions', [])) > 0:
        followup_questions = json.dumps(task.result_json['followup_questions'], ensure_ascii=False)
        events.append({
            'agent': task.agent_name,
            'thread_id': task.thread.id,
            'type': 'followupQuestion', 
            'hitl_mode': 'always', # no used by frontend
            'sender': 'assistant',
            'translations': {}, # no used by frontend
            'chunkIdx': 4,
            'message': followup_questions,
            'id': f'{task.id}-0-f',
            'startedAt': task.time_created,            
        })
    
    if task.task_status == 'complete':
        events.append({
            'agent': task.agent_name,
            'thread_id': task.thread.id,
            'type': 'statusUpdate', 
            'hitl_mode': 'always',
            'sender': 'assistant',
            'translations': {},
            'chunkIdx': 5,
            'agentStatus': 'stopped',
            'id': f'{task.id}-0-d',
            'startedAt': int(task.time_updated.timestamp()),
            'taskStart': int(task.time_created.timestamp())
        })
    
    task.result_json = {
        'events': events
    }

    return task

router = {
    "mindsearch": mindsearch_formatter,
    "mindsearchrefer": mindsearch_formatter,
    "mindsearchrefernorag": mindsearch_formatter,
    "mindsearchofficialsite": mindsearch_formatter,
    "mindsearchofficialsitenorag": mindsearch_formatter,
    "mindsearchworkflowrefer": mindsearch_formatter,
    "mindsearchworkflowrefernorag": mindsearch_formatter,
    "mindsearchclinicalguideline": mindsearch_formatter,
    "mindsearchpubmed": mindsearch_formatter,
    "mindsearchdoublecheck": mindsearch_formatter,
    "email": content_only_formatter,
    "investment_report": mindsearch_formatter,
}

def chat_formatter(event:dict, msg: Message, json_chunk: dict, msg_params:dict, **kwargs) -> dict:
    if json_chunk.get("agent", "") == "planning":
        return planning_formatter(event, msg, json_chunk, msg_params, **kwargs)
    agent_name = event.get("agent_name", "")
    # TODO delete later just for hitl formatting
    is_hitl = event.get('params', {}).get('isHitl', False)
    if is_hitl and agent_name.startswith('mindsearch'):
        return hitl_formatter(event, msg, json_chunk, msg_params, **kwargs)

    if agent_name not in router:
        msg_params['task_id'] = str(msg.task.id)
        return msg_params
    return router[agent_name](event, msg, json_chunk, msg_params, **kwargs)

def last_package_formatter(rslt: dict) -> str:
    rslt['status'] = 'DONE'
    if 'task_mode' in rslt and rslt['task_mode'] == 'planning':
        rslt['running_task']['status'] = 'DONE'
    return json.dumps(rslt, ensure_ascii=False)

def stopped_formatter(rslt: dict, task: Task) -> str:
    rslt['taskStart'] =  int(task.time_created.timestamp())
    rslt['agentStatus'] = 'stopped'
    rslt['type'] = 'statusUpdate'
    rslt['startedAt'] = int(time.time())
    rslt['id'] = rslt['id'] + '-a'
    rslt.pop('message', None)
    return json.dumps(rslt, ensure_ascii=False)
    
