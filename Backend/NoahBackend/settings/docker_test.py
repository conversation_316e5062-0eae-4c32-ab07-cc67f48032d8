import json
import sys
from .base import *

# read settings

with open(BASE_DIR / "api.json", "r") as f:
    EXTERNAL_API = json.load(f)

with open(BASE_DIR / "settings.json", "r") as f:
    setting_json = json.load(f)


# Redis

REDIS_HOST = "noah-redis"
REDIS_PORT = 6379

# Elasticsearch

ES_URL = "http://noah-elastic:9200"
ES_USERNAME = "elastic"
ES_PASSWORD = "elasticnoah"

ES_CONTEXT_URL = "http://noah-escontext:9200"
ES_CONTEXT_USERNAME = "elastic"
ES_CONTEXT_PASSWORD = "elasticnoah"

# PG table

PG_URL = "noah-pgtable:5432"
PG_USERNAME = "noah"
PG_PASSWORD = "postgres"

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = setting_json['SECRET_KEY']

# SECURITY WARNING: don"t run with debug turned on in production!
DEBUG = True

# HTTP headers

ALLOWED_HOSTS = [
    "localhost",
    "noah-backend",
    # "0.0.0.0",
    "127.0.0.1"
] + setting_json["ALLOWED_HOSTS"]

# CSRF

CSRF_TRUSTED_ORIGINS = setting_json["CSRF_TRUSTED_ORIGINS"]

# CORS

CORS_ALLOWED_ORIGINS = setting_json["CORS_ALLOWED_ORIGINS"]

# CHANNELS

CHANNEL_LAYERS = {
    "default": {
        "BACKEND": "channels_redis.pubsub.RedisPubSubChannelLayer",
        "CONFIG": {
            "hosts": [(REDIS_HOST, REDIS_PORT)],
            "capacity": 10000,
            "expiry": 60,
        },
    },
}

# REST Framework

REST_FRAMEWORK = {
    # default authentication
    "DEFAULT_AUTHENTICATION_CLASSES": [
        # "rest_framework.authentication.TokenAuthentication",
        "Account.authentication.ExpiringTokenAuthentication"
    ],
    "EXCEPTION_HANDLER": "middleware.custom_exception_handler",
    # pagination
    "DEFAULT_PAGINATION_CLASS": "rest_framework.pagination.LimitOffsetPagination",
    "PAGE_SIZE": 20,
    "DEFAULT_RENDERER_CLASSES": [
        "rest_framework.renderers.JSONRenderer",
    ],
    # throttling
    "DEFAULT_THROTTLE_CLASSES": setting_json["REST_FRAMEWORK"]["DEFAULT_THROTTLE_CLASSES"],
    "DEFAULT_THROTTLE_RATES": setting_json["REST_FRAMEWORK"]["DEFAULT_THROTTLE_RATES"],
    "DEFAULT_FILTER_BACKENDS": [
        "django_filters.rest_framework.DjangoFilterBackend",
    ],
}

if (perms:=setting_json["REST_FRAMEWORK"].get("DEFAULT_PERMISSION_CLASSES", [])):
    REST_FRAMEWORK["DEFAULT_PERMISSION_CLASSES"] = perms

# LOGGING

DJANGO_GUID = {
    'GUID_HEADER_NAME': 'Correlation-ID',
    'VALIDATE_GUID': True,
    'RETURN_HEADER': True,
    'EXPOSE_HEADER': True,
    'INTEGRATIONS': [],
    'IGNORE_URLS': [],
    'UUID_LENGTH': 32,
}

LOG_FORMAT = "[%(asctime)s] [%(correlation_id)s] [%(name)s] [%(levelname)s] %(message)s"

# Ensure log directories exist
import os
log_dir = BASE_DIR / "log" / "django_log"
os.makedirs(log_dir, exist_ok=True)

LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "standard": {
            "format": LOG_FORMAT,
        },
    },
    "filters": {
        "correlation_id": {
            "()": "django_guid.log_filters.CorrelationId"
        },
        "is_agent_status": {
            "()": "utils.log.IsAgentStatus"
        },
    },
    "handlers": {
        "console": {
            "level": "INFO",
            "class": "logging.StreamHandler",
            "stream": sys.stdout,
            "filters": ["correlation_id"],
            "formatter": "standard",
        },
        "file": {
            "level": "INFO",
            "class": "logging.handlers.TimedRotatingFileHandler",
            "filename": BASE_DIR / "log" / "django_log" / "basic.log",
            "when": "midnight",
            "backupCount": 30,
            "encoding": "utf-8",
            "filters": ["correlation_id"],
            "formatter": "standard",
        },
        # the current log file will name as "django.log" and after time interval
        # it will rename
        "api_log": {
            "level": "DEBUG",
            "class": "logging.handlers.TimedRotatingFileHandler",
            "filename": BASE_DIR / "log" / "django_log" / "api.log",
            "when": "midnight",
            "backupCount": 30,
            "encoding": "utf-8",
            "filters": ["correlation_id"],
            "formatter": "standard",
        },
        "workflow_log": {
            "level": "DEBUG",
            "class": "logging.handlers.TimedRotatingFileHandler",
            "filename": BASE_DIR / "log" / "django_log" / "workflow.log",
            "when": "midnight",
            "backupCount": 30,
            "encoding": "utf-8",
            "filters": ["correlation_id"],
            "formatter": "standard",
        },
        "django_debug": {
            "level": "DEBUG",
            "class": "logging.handlers.TimedRotatingFileHandler",
            "filename": BASE_DIR / "log" / "django_log" / "django.debug",
            "when": "midnight",
            "backupCount": 30,
            "encoding": "utf-8",
            "filters": ["correlation_id"],
            "formatter": "standard",
        },
        "django_warning": {
            "level": "WARNING",
            "class": "logging.handlers.TimedRotatingFileHandler",
            "filename": BASE_DIR / "log" / "django_log" / "django.warning",
            "when": "midnight",
            "backupCount": 30,
            "encoding": "utf-8",
            "filters": ["correlation_id"],
            "formatter": "standard",
        },
        "django_error": {
            "level": "ERROR",
            "class": "logging.handlers.TimedRotatingFileHandler",
            "filename": BASE_DIR / "log" / "django_log" / "django.error",
            "when": "midnight",
            "backupCount": 30,
            "encoding": "utf-8",
            "filters": ["correlation_id"],
            "formatter": "standard",
        },
        "django_channels_debug": {
            "level": "DEBUG",
            "class": "logging.handlers.TimedRotatingFileHandler",
            "filename": BASE_DIR / "log" / "django_log" / "channels.debug",
            "when": "midnight",
            "backupCount": 30,
            "encoding": "utf-8",
            "filters": ["correlation_id"],
            "formatter": "standard",
        },
        "django_channels_warning": {
            "level": "WARNING",
            "class": "logging.handlers.TimedRotatingFileHandler",
            "filename": BASE_DIR / "log" / "django_log" / "channels.warning",
            "when": "midnight",
            "backupCount": 30,
            "encoding": "utf-8",
            "filters": ["correlation_id"],
            "formatter": "standard",
        },
        "django_channels_error": {
            "level": "ERROR",
            "class": "logging.handlers.TimedRotatingFileHandler",
            "filename": BASE_DIR / "log" / "django_log" / "channels.error",
            "when": "midnight",
            "backupCount": 30,
            "encoding": "utf-8",
            "filters": ["correlation_id"],
            "formatter": "standard",
        },
        "django_request_debug": {
            "level": "DEBUG",
            "class": "logging.handlers.TimedRotatingFileHandler",
            "filename": BASE_DIR / "log" / "django_log" / "request.debug",
            "when": "midnight",
            "backupCount": 30,
            "encoding": "utf-8",
            "filters": ["correlation_id"],
            "formatter": "standard",
        },
        "django_request_warning": {
            "level": "WARNING",
            "class": "logging.handlers.TimedRotatingFileHandler",
            "filename": BASE_DIR / "log" / "django_log" / "request.warning",
            "when": "midnight",
            "backupCount": 30,
            "encoding": "utf-8",
            "filters": ["correlation_id"],
            "formatter": "standard",
        },
        "django_request_error": {
            "level": "ERROR",
            "class": "logging.handlers.TimedRotatingFileHandler",
            "filename": BASE_DIR / "log" / "django_log" / "request.error",
            "when": "midnight",
            "backupCount": 30,
            "encoding": "utf-8",
            "filters": ["correlation_id"],
            "formatter": "standard",
        },
        "daphne_debug": {
            "level": "DEBUG",
            "class": "logging.handlers.TimedRotatingFileHandler",
            "filename": BASE_DIR / "log" / "django_log" / "daphne.debug",
            "when": "midnight",
            "backupCount": 30,
            "encoding": "utf-8",
            "filters": ["correlation_id"],
            "formatter": "standard",
        },
        "daphne_warning": {
            "level": "WARNING",
            "class": "logging.handlers.TimedRotatingFileHandler",
            "filename": BASE_DIR / "log" / "django_log" / "daphne.warning",
            "when": "midnight",
            "backupCount": 30,
            "encoding": "utf-8",
            "filters": ["correlation_id"],
            "formatter": "standard",
        },
        "daphne_error": {
            "level": "ERROR",
            "class": "logging.handlers.TimedRotatingFileHandler",
            "filename": BASE_DIR / "log" / "django_log" / "daphne.error",
            "when": "midnight",
            "backupCount": 30,
            "encoding": "utf-8",
            "filters": ["correlation_id"],
            "formatter": "standard",
        },
        "modules_debug": {
            "level": "DEBUG",
            "class": "logging.handlers.TimedRotatingFileHandler",
            "filename": BASE_DIR / "log" / "django_log" / "modules.debug",
            "when": "midnight",
            "backupCount": 30,
            "encoding": "utf-8",
            "filters": ["correlation_id"],
            "formatter": "standard",
        },
        "modules_warning": {
            "level": "WARNING",
            "class": "logging.handlers.TimedRotatingFileHandler",
            "filename": BASE_DIR / "log" / "django_log" / "modules.warning",
            "when": "midnight",
            "backupCount": 30,
            "encoding": "utf-8",
            "filters": ["correlation_id"],
            "formatter": "standard",
        },
        "modules_error": {
            "level": "ERROR",
            "class": "logging.handlers.TimedRotatingFileHandler",
            "filename": BASE_DIR / "log" / "django_log" / "modules.error",
            "when": "midnight",
            "backupCount": 30,
            "encoding": "utf-8",
            "filters": ["correlation_id"],
            "formatter": "standard",
        }
    },
    "root": {
        "handlers": ["console", "file"],
        "level": "INFO",
    },
    "loggers": {
        "API": {
            "handlers": ["api_log"],
            "level": "DEBUG",
            "propagate": True,
        },
        "API.chat": {
            "handlers": ["api_log"],
            "level": "DEBUG",
            "propagate": True,
        },
        "Workflow": {
            "handlers": ["workflow_log"],
            "level": "DEBUG",
            "propagate": True,
        },
        "Modules": {
            "handlers": [
                "modules_debug",
                "modules_warning",
                "modules_error"
            ],
            "level": "DEBUG",
            "propagate": True,
        },
        "django": {
            "handlers": [
                "django_debug",
                "django_warning",
                "django_error"
            ],
            "level": "DEBUG",
            "propagate": True,
        },
        "django.channels": {
            "handlers": [
                "django_channels_debug",
                "django_channels_warning",
                "django_channels_error"
            ],
            "level": "DEBUG",
            "propagate": True,
        },
        "django.request": {
            "handlers": [
                "django_request_debug",
                "django_request_warning",
                "django_request_error"
            ],
            "level": "DEBUG",
            "propagate": True,
        },
        "daphne": {
            "handlers": [
                "daphne_debug",
                "daphne_warning",
                "daphne_error"
            ],
            "level": "DEBUG",
            "propagate": True,
        },
        'django_guid': {
            'handlers': ['console'],
            'level': 'WARNING'  # Set to a higher level like WARNING or ERROR to suppress most messages
        },
    },
}


# Database

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": "noah_postgres",
        "USER": "noah",
        "PASSWORD": "postgres",
        "HOST": "noah-postgres",
        "PORT": "5432",
        "TIME_ZONE": "UTC"
    },
    "prod": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": "noah_postgres",
        "USER": "noah",
        "PASSWORD": "postgres",
        "HOST": "*************",
        "PORT": "54321",
        "TIME_ZONE": "UTC"
    }
}
# Database connection pooling

DATABASES["default"]["CONN_MAX_AGE"] = 60  # Reuse database connections for 1 minute

if setting_json.get("default_db", {}):
    DATABASES["default"] = setting_json.get("default_db")


CACHES = {
    "default": {
        "BACKEND": "django.core.cache.backends.redis.RedisCache",
        "LOCATION": "redis://noah-redis:6379",
        "OPTIONS": {
            "db": "2",
        },
    },
    "active_task_db": {
        "BACKEND": "django.core.cache.backends.redis.RedisCache",
        "LOCATION": "redis://noah-redis:6379",
        "OPTIONS": {
            "db": EXTERNAL_API.get("REDIS_ACTIVE_TASK_DB","10"),
        },
    },
    "nine": {
        "BACKEND": "API.redis.RedisCache",
        "LOCATION": "redis://noah-redis:6379",
        "OPTIONS": {
            "db": "9",
        },  
    }
}

# Email

EMAIL_HOST = EXTERNAL_API["EMAIL_HOST"]
EMAIL_PORT = EXTERNAL_API["EMAIL_PORT"]
EMAIL_HOST_USER = EXTERNAL_API["EMAIL_HOST_USER"]
EMAIL_HOST_PASSWORD = EXTERNAL_API["EMAIL_HOST_PASSWORD"]

EMAIL_VERIFICATION_URL = setting_json["EMAIL_VERIFICATION_URL"]
EMAIL_VERIFICATION_SUBJECT = "Verify your Noah.AI account email address"
EMAIL_VERIFICATION_FROM_EMAIL = setting_json["EMAIL_VERIFICATION_FROM_EMAIL"]

RESET_PASSWORD_URL = setting_json["RESET_PASSWORD_URL"]
RESET_PASSWORD_SUBJECT = "Reset your Noah.AI account password"
RESET_PASSWORD_FROM_EMAIL = setting_json["RESET_PASSWORD_FROM_EMAIL"]

SECONDARY_ES_URL = setting_json['AZURE_ELASTICSEARCH_URL']
DATA_UPLOAD_MAX_NUMBER_FIELDS = 1500
HOST = 'https://test.noahai.co'
TIME_ZONE = setting_json.get("TIME_ZONE", "UTC")  # Default to UTC timezone

for key, value in setting_json.items():
    if key.isupper() and key not in globals():
        globals()[key] = value
