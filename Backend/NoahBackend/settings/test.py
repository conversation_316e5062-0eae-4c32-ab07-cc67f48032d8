import json
from .base import *
import sys
import psycopg2
from psycopg2 import OperationalError
from django.db import connections
from django.db.utils import OperationalError

# read settings

with open(BASE_DIR / "api.json", "r") as f:
    EXTERNAL_API = json.load(f)

with open(BASE_DIR / "setting_test.json", "r") as f:
    setting_json = json.load(f)

# Redis

REDIS_HOST = "localhost"
REDIS_PORT = 63791

CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": f"redis://{REDIS_HOST}:{REDIS_PORT}/1",
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
        }
    }
}

# PG table

PG_URL = "localhost:6001"
PG_USERNAME = "noah"
PG_PASSWORD = "postgres"

# Elasticsearch

ES_URL = "http://localhost:6002"
ES_USERNAME = "elastic"
ES_PASSWORD = "elasticnoah"

ES_CONTEXT_URL = "http://localhost:6004"
ES_CONTEXT_USERNAME = "elastic"
ES_CONTEXT_PASSWORD = "elasticnoah"

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = setting_json['SECRET_KEY']

# SECURITY WARNING: don"t run with debug turned on in production!
DEBUG = True

# HTTP headers

ALLOWED_HOSTS = [
    "localhost",
    "noah-backend",
    "0.0.0.0",
    "127.0.0.1"
] + setting_json["ALLOWED_HOSTS"]

# CSRF

CSRF_TRUSTED_ORIGINS = setting_json["CSRF_TRUSTED_ORIGINS"]

# CORS

CORS_ALLOWED_ORIGINS = setting_json["CORS_ALLOWED_ORIGINS"]

# CHANNELS

CHANNEL_LAYERS = {
    "default": {
        "BACKEND": "channels_redis.pubsub.RedisPubSubChannelLayer",
        "CONFIG": {
            "hosts": [(REDIS_HOST, REDIS_PORT)],
            "capacity": 10000,
            "expiry": 60,
        },
    },
}


# REST Framework

REST_FRAMEWORK = {
    # default authentication
    "DEFAULT_AUTHENTICATION_CLASSES": [
        # "rest_framework.authentication.SessionAuthentication",
        # "rest_framework.authentication.TokenAuthentication",
        'Account.authentication.ExpiringTokenAuthentication',
    ],
    # "EXCEPTION_HANDLER": "middleware.custom_exception_handler",
    # pagination
    "DEFAULT_PAGINATION_CLASS": "rest_framework.pagination.LimitOffsetPagination",
    "PAGE_SIZE": 20,
    # throttling
    "DEFAULT_THROTTLE_CLASSES": setting_json["REST_FRAMEWORK"]["DEFAULT_THROTTLE_CLASSES"],
    "DEFAULT_THROTTLE_RATES": setting_json["REST_FRAMEWORK"]["DEFAULT_THROTTLE_RATES"],
    "DEFAULT_FILTER_BACKENDS": [
        "django_filters.rest_framework.DjangoFilterBackend",
    ],
}

# Database
# https://docs.djangoproject.com/en/4.2/ref/settings/#databases

DATABASES = {
    # "default": {
    #     "ENGINE": "django.db.backends.sqlite3",
    #     "NAME": BASE_DIR / "db.sqlite3",
    # },
    "default": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": "noah_postgres",
        "USER": "noah",
        "PASSWORD": "postgres",
        "HOST": "localhost",
        "PORT": "54321",
        "TIME_ZONE": "UTC",
        # "MAX_CONN_AGE": 600,
        # "CONN_HEALTH_CHECKS": True,
        # "OPTIONS": {
        #     "pool": True,
        # },
    }
}
# DATABASES["default"]["CONN_MAX_AGE"] = 2400  # Reuse database connections for 10 minutes


# Email

EMAIL_HOST = EXTERNAL_API["EMAIL_HOST"]
EMAIL_PORT = EXTERNAL_API["EMAIL_PORT"]
EMAIL_HOST_USER = EXTERNAL_API["EMAIL_HOST_USER"]
EMAIL_HOST_PASSWORD = EXTERNAL_API["EMAIL_HOST_PASSWORD"]

EMAIL_VERIFICATION_URL = setting_json["EMAIL_VERIFICATION_URL"]
EMAIL_VERIFICATION_SUBJECT = "Verify your Noah.AI account email address"
EMAIL_VERIFICATION_FROM_EMAIL = setting_json["EMAIL_VERIFICATION_FROM_EMAIL"]

RESET_PASSWORD_URL = setting_json["RESET_PASSWORD_URL"]
RESET_PASSWORD_SUBJECT = "Reset your Noah.AI account password"
RESET_PASSWORD_FROM_EMAIL = setting_json["RESET_PASSWORD_FROM_EMAIL"]

SECONDARY_ES_URL = setting_json['AZURE_ELASTICSEARCH_URL']

ELASTICSEARCH_DSL={
    'default': {
        'hosts': 'http://**************:6004',
        'http_auth': ('elastic', 'dataesdb')
    },
    'secondary': {
        'hosts': SECONDARY_ES_URL,
        'http_auth': ('elastic', 'elasticnoah'),
    }
}

# LOGGING

DJANGO_GUID = {
    'GUID_HEADER_NAME': 'Correlation-ID',
    'VALIDATE_GUID': True,
    'RETURN_HEADER': True,
    'EXPOSE_HEADER': True,
    'INTEGRATIONS': [],
    'IGNORE_URLS': [],
    'UUID_LENGTH': 32,
    'UUID_FORMAT': 'hex',
}

LOG_FORMAT = "[%(asctime)s] [%(correlation_id)s] [%(name)s] [%(levelname)s] %(message)s"

LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "filters": {
        "correlation_id": {
            "()": "django_guid.log_filters.CorrelationId"
        },
    },
    "formatters": {
        "standard": {
            "format": LOG_FORMAT,
        },
    },
    "handlers": {
        "console": {
            "level": "INFO",
            "class": "logging.StreamHandler",
            "stream": sys.stdout,
            "filters": ["correlation_id"],
            "formatter": "standard",
        },
        "file": {
            "level": "INFO",
            "class": "logging.handlers.TimedRotatingFileHandler",
            "filename": BASE_DIR / "log" / "django_log" / "basic.log",
            "when": "midnight",
            "backupCount": 30,
            "encoding": "utf-8",
            "filters": ["correlation_id"],
            "formatter": "standard",
        },
        # the current log file will name as "django.log" and after time interval
        # it will rename
        "api_log": {
            "level": "DEBUG",
            "class": "logging.handlers.TimedRotatingFileHandler",
            "filename": BASE_DIR / "log" / "django_log" / "api.log",
            "when": "midnight",
            "backupCount": 30,
            "encoding": "utf-8",
            "filters": ["correlation_id"],
            "formatter": "standard",
        },
        "workflow_log": {
            "level": "DEBUG",
            "class": "logging.handlers.TimedRotatingFileHandler",
            "filename": BASE_DIR / "log" / "django_log" / "workflow.log",
            "when": "midnight",
            "backupCount": 30,
            "encoding": "utf-8",
            "filters": ["correlation_id"],
            "formatter": "standard",
        },
        "django_debug": {
            "level": "DEBUG",
            "class": "logging.handlers.TimedRotatingFileHandler",
            "filename": BASE_DIR / "log" / "django_log" / "django.debug",
            "when": "midnight",
            "backupCount": 30,
            "encoding": "utf-8",
            "filters": ["correlation_id"],
            "formatter": "standard",
        },
        "django_warning": {
            "level": "WARNING",
            "class": "logging.handlers.TimedRotatingFileHandler",
            "filename": BASE_DIR / "log" / "django_log" / "django.warning",
            "when": "midnight",
            "backupCount": 30,
            "encoding": "utf-8",
            "filters": ["correlation_id"],
            "formatter": "standard",
        },
        "django_error": {
            "level": "ERROR",
            "class": "logging.handlers.TimedRotatingFileHandler",
            "filename": BASE_DIR / "log" / "django_log" / "django.error",
            "when": "midnight",
            "backupCount": 30,
            "encoding": "utf-8",
            "filters": ["correlation_id"],
            "formatter": "standard",
        },
        "django_channels_debug": {
            "level": "DEBUG",
            "class": "logging.handlers.TimedRotatingFileHandler",
            "filename": BASE_DIR / "log" / "django_log" / "channels.debug",
            "when": "midnight",
            "backupCount": 30,
            "encoding": "utf-8",
            "filters": ["correlation_id"],
            "formatter": "standard",
        },
        "django_channels_warning": {
            "level": "WARNING",
            "class": "logging.handlers.TimedRotatingFileHandler",
            "filename": BASE_DIR / "log" / "django_log" / "channels.warning",
            "when": "midnight",
            "backupCount": 30,
            "encoding": "utf-8",
            "filters": ["correlation_id"],
            "formatter": "standard",
        },
        "django_channels_error": {
            "level": "ERROR",
            "class": "logging.handlers.TimedRotatingFileHandler",
            "filename": BASE_DIR / "log" / "django_log" / "channels.error",
            "when": "midnight",
            "backupCount": 30,
            "encoding": "utf-8",
            "filters": ["correlation_id"],
            "formatter": "standard",
        },
        "django_request_debug": {
            "level": "DEBUG",
            "class": "logging.handlers.TimedRotatingFileHandler",
            "filename": BASE_DIR / "log" / "django_log" / "request.debug",
            "when": "midnight",
            "backupCount": 30,
            "encoding": "utf-8",
            "filters": ["correlation_id"],
            "formatter": "standard",
        },
        "django_request_warning": {
            "level": "WARNING",
            "class": "logging.handlers.TimedRotatingFileHandler",
            "filename": BASE_DIR / "log" / "django_log" / "request.warning",
            "when": "midnight",
            "backupCount": 30,
            "encoding": "utf-8",
            "filters": ["correlation_id"],
            "formatter": "standard",
        },
        "django_request_error": {
            "level": "ERROR",
            "class": "logging.handlers.TimedRotatingFileHandler",
            "filename": BASE_DIR / "log" / "django_log" / "request.error",
            "when": "midnight",
            "backupCount": 30,
            "encoding": "utf-8",
            "filters": ["correlation_id"],
            "formatter": "standard",
        },
        "daphne_debug": {
            "level": "DEBUG",
            "class": "logging.handlers.TimedRotatingFileHandler",
            "filename": BASE_DIR / "log" / "django_log" / "daphne.debug",
            "when": "midnight",
            "backupCount": 30,
            "encoding": "utf-8",
            "filters": ["correlation_id"],
            "formatter": "standard",
        },
        "daphne_warning": {
            "level": "WARNING",
            "class": "logging.handlers.TimedRotatingFileHandler",
            "filename": BASE_DIR / "log" / "django_log" / "daphne.warning",
            "when": "midnight",
            "backupCount": 30,
            "encoding": "utf-8",
            "filters": ["correlation_id"],
            "formatter": "standard",
        },
        "daphne_error": {
            "level": "ERROR",
            "class": "logging.handlers.TimedRotatingFileHandler",
            "filename": BASE_DIR / "log" / "django_log" / "daphne.error",
            "when": "midnight",
            "backupCount": 30,
            "encoding": "utf-8",
            "filters": ["correlation_id"],
            "formatter": "standard",
        },
        "modules_debug": {
            "level": "DEBUG",
            "class": "logging.handlers.TimedRotatingFileHandler",
            "filename": BASE_DIR / "log" / "django_log" / "modules.debug",
            "when": "midnight",
            "backupCount": 30,
            "encoding": "utf-8",
            "filters": ["correlation_id"],
            "formatter": "standard",
        },
        "modules_warning": {
            "level": "WARNING",
            "class": "logging.handlers.TimedRotatingFileHandler",
            "filename": BASE_DIR / "log" / "django_log" / "modules.warning",
            "when": "midnight",
            "backupCount": 30,
            "encoding": "utf-8",
            "filters": ["correlation_id"],
            "formatter": "standard",
        },
        "modules_error": {
            "level": "ERROR",
            "class": "logging.handlers.TimedRotatingFileHandler",
            "filename": BASE_DIR / "log" / "django_log" / "modules.error",
            "when": "midnight",
            "backupCount": 30,
            "encoding": "utf-8",
            "filters": ["correlation_id"],
            "formatter": "standard",
        }
    },
    "root": {
        "handlers": ["console", "file"],
        "level": "INFO",
    },
    "loggers": {
        "API": {
            "handlers": ["api_log"],
            "level": "DEBUG",
            "propagate": True,
        },
        "API.chat": {
            "handlers": ["api_log"],
            "level": "DEBUG",
            "propagate": True,
        },
        "Workflow": {
            "handlers": ["workflow_log"],
            "level": "DEBUG",
            "propagate": True,
        },
        "Modules": {
            "handlers": [
                "modules_debug",
                "modules_warning",
                "modules_error"
            ],
            "level": "DEBUG",
            "propagate": True,
        },
        "django": {
            "handlers": [
                "django_debug",
                "django_warning",
                "django_error"
            ],
            "level": "DEBUG",
            "propagate": True,
        },
        "django.channels": {
            "handlers": [
                "django_channels_debug",
                "django_channels_warning",
                "django_channels_error"
            ],
            "level": "DEBUG",
            "propagate": True,
        },
        "django.request": {
            "handlers": [
                "django_request_debug",
                "django_request_warning",
                "django_request_error"
            ],
            "level": "DEBUG",
            "propagate": True,
        },
        "daphne": {
            "handlers": [
                "daphne_debug",
                "daphne_warning",
                "daphne_error"
            ],
            "level": "DEBUG",
            "propagate": True,
        },
        'django_guid': {
            'handlers': ['console'],
            'level': 'WARNING'  # Set to a higher level like WARNING or ERROR to suppress most messages
        },
    },
}

# TYPESENSE_CONFIG = {
#     "API_KEY": setting_json["TYPESENSE_API_KEY"],
#     "HOST": setting_json["TYPESENSE_HOST"],
#     "PORT": setting_json["TYPESENSE_PORT"],
# }
# # Load additional settings from setting_json

for key, value in setting_json.items():
    if key.isupper() and key not in globals():
        globals()[key] = value

def test_db_connection():
    db_conn = connections['default']
    try:
        db_conn.cursor()
        print("Connection to PostgreSQL DB successful")
    except OperationalError as e:
        print(f"The error '{e}' occurred")
        sys.exit(1)
        
HOST = 'localhost'
TIME_ZONE = 'Asia/Shanghai'  # Beijing timezone

test_db_connection()