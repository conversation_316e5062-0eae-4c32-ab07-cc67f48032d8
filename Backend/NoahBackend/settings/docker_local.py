import json
import sys
from .base import *

# read settings

with open(BASE_DIR / "api.json", "r") as f:
    EXTERNAL_API = json.load(f)

with open(BASE_DIR / "settings.json", "r") as f:
    setting_json = json.load(f)

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": "noah_postgres",
        "USER": "noah",
        "PASSWORD": "postgres",
        "HOST": "localhost",
        "PORT": "54321",
        "TIME_ZONE": "UTC"
    }
}

# Redis

REDIS_HOST = "localhost"
REDIS_PORT = 63791

# Elasticsearch

ES_URL = "http://noah-elastic:9200"
ES_USERNAME = "elastic"
ES_PASSWORD = "elasticnoah"

ES_CONTEXT_URL = "http://noah-escontext:9200"
ES_CONTEXT_USERNAME = "elastic"
ES_CONTEXT_PASSWORD = "elasticnoah"

# PG table

PG_URL = "noah-pgtable:5432"
PG_USERNAME = "noah"
PG_PASSWORD = "postgres"

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = setting_json['SECRET_KEY']

# SECURITY WARNING: don"t run with debug turned on in production!
DEBUG = True

# HTTP headers

ALLOWED_HOSTS = [
    "localhost",
    "host.docker.internal",
    "noah-backend",
    "0.0.0.0",
    "127.0.0.1"
] + setting_json["ALLOWED_HOSTS"]

# CSRF

CSRF_TRUSTED_ORIGINS = setting_json["CSRF_TRUSTED_ORIGINS"]

# CORS

CORS_ALLOWED_ORIGINS = setting_json["CORS_ALLOWED_ORIGINS"]

# CHANNELS

CHANNEL_LAYERS = {
    "default": {
        "BACKEND": "channels_redis.pubsub.RedisPubSubChannelLayer",
        "CONFIG": {
            "hosts": [(REDIS_HOST, REDIS_PORT)],
            "capacity": 10000,
            "expiry": 60,
        },
    },
}

# REST Framework

REST_FRAMEWORK = {
    # default authentication
    "DEFAULT_AUTHENTICATION_CLASSES": [
        "rest_framework.authentication.TokenAuthentication",
    ],
    "EXCEPTION_HANDLER": "middleware.custom_exception_handler",
    # pagination
    "DEFAULT_PAGINATION_CLASS": "rest_framework.pagination.LimitOffsetPagination",
    "PAGE_SIZE": 20,
    # throttling
    "DEFAULT_THROTTLE_CLASSES": setting_json["REST_FRAMEWORK"]["DEFAULT_THROTTLE_CLASSES"],
    "DEFAULT_THROTTLE_RATES": setting_json["REST_FRAMEWORK"]["DEFAULT_THROTTLE_RATES"],
    "DEFAULT_FILTER_BACKENDS": [
        "django_filters.rest_framework.DjangoFilterBackend",
    ],
}


# LOGGING
DJANGO_GUID = {
    'GUID_HEADER_NAME': 'Correlation-ID',
    'VALIDATE_GUID': True,
    'RETURN_HEADER': True,
    'EXPOSE_HEADER': True,
    'INTEGRATIONS': [],
    'IGNORE_URLS': [],
    'UUID_LENGTH': 32,
}

LOG_FORMAT = "[%(asctime)s] [%(correlation_id)s] [%(name)s] [%(levelname)s] %(message)s"

LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "standard": {
            "format": LOG_FORMAT,
        },
    },
    "filters": {
        "correlation_id": {
            "()": "django_guid.log_filters.CorrelationId"
        },
        "is_agent_status": {
            "()": "utils.log.IsAgentStatus"
        },
    },
    "handlers": {
        "console": {
            "level": "INFO",
            "class": "logging.StreamHandler",
            "filters": ["correlation_id"],
            "stream": sys.stderr,
            "formatter": "standard",
        },
    },
    "root": {
        "handlers": ["console"],
        "level": "INFO",
    },
}


# Database

# Database connection pooling

# DATABASES["default"]["CONN_MAX_AGE"] = 2400  # Reuse database connections for 10 minutes

# Email

EMAIL_HOST = EXTERNAL_API["EMAIL_HOST"]
EMAIL_PORT = EXTERNAL_API["EMAIL_PORT"]
EMAIL_HOST_USER = EXTERNAL_API["EMAIL_HOST_USER"]
EMAIL_HOST_PASSWORD = EXTERNAL_API["EMAIL_HOST_PASSWORD"]

EMAIL_VERIFICATION_URL = setting_json["EMAIL_VERIFICATION_URL"]
EMAIL_VERIFICATION_SUBJECT = "Verify your Noah.AI account email address"
EMAIL_VERIFICATION_FROM_EMAIL = setting_json["EMAIL_VERIFICATION_FROM_EMAIL"]

RESET_PASSWORD_URL = setting_json["RESET_PASSWORD_URL"]
RESET_PASSWORD_SUBJECT = "Reset your Noah.AI account password"
RESET_PASSWORD_FROM_EMAIL = setting_json["RESET_PASSWORD_FROM_EMAIL"]

for key, value in setting_json.items():
    if key.isupper() and key not in globals():
        globals()[key] = value
        
EXTERNAL_API["NOAH_AGENT_HOST"] = f"http://localhost:8012"

CACHES = {
    "default": {
        "BACKEND": "django.core.cache.backends.redis.RedisCache",
        "LOCATION": "redis://127.0.0.1:63791",
        "OPTIONS": {
            "db": "2",
            # "MAX_ENTRIES": 3000,
        },
    },
    "active_task_db": {
        "BACKEND": "django.core.cache.backends.redis.RedisCache",
        "LOCATION": "redis://127.0.0.1:63791",
        "OPTIONS": {
            "db": "10",
        },
    }
}