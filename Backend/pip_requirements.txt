# server
django>=5.1
daphne==4.1.0
channels[daphne]==4.0.0
channels_redis==4.2.0
djangorestframework==3.15.0
django-cors-headers==4.3.1
django-dynamic-preferences==1.16.0
django-redis==5.4.0
django-rq==2.10.1
django-oauth-toolkit==2.3.0
oss2==2.18.4

# other
numpy==1.26.3
pandas==2.1.4
biopython==1.82
python-docx==1.1.0
pygtrans==1.5.3
elasticsearch==8.12.0
psycopg==3.1.12
psycopg2-binary==2.9.9
yfinance==0.2.36
tiktoken==0.5.2
glom==23.5.0
scikit-learn==1.5.0
python-pptx==0.6.23
docx2txt==0.8
django_elasticsearch_dsl>=8.0
elasticsearch==8.12.0
elasticsearch-dsl==8.17.1
django_reverse_admin==2.9.6
httpx
django-dynamic-admin-forms
json2table==1.1.5
django-filter==24.3
aiohttp
twisted[tls,http2]
psycopg[pool]
typesense
pytest-asyncio
pytest-docker
stripe
marko
google-auth
django-guid
esdk-obs-python
dotenv
psutil
django-import-export
tablib[xlsx]
posthog