from django.db import models
from Account.models import User
from Dashboard.module_view_reusables import *
from API.posthog import posthog
class DiscoverBuzz(models.Model):
    author = models.TextField(blank=True, null=True)
    long_title = models.TextField()
    summary = models.TextField(blank=True, null=True)
    category = models.TextField(blank=True, null=True)
    cover_urls = models.TextField(blank=True, null=True)
    prompt_static = models.TextField(blank=True, null=True)
    
    hotspot_id = models.TextField()
    language = models.TextField(blank=True, null=True)
    doc_type = models.TextField()
    published_date = models.TextField(blank=True, null=True)
    sources_published_date = models.TextField(blank=True, null=True)
    
    created = models.DateTimeField(auto_now_add=True)
    archived = models.BooleanField(default=False)
    class Meta:
        ordering = ['-published_date']
        unique_together = ('published_date', 'long_title', 'doc_type')
    
class DiscoverArticle(models.Model):
    title = models.TextField()
    abstract = models.TextField(blank=True, null=True)
    conclusion = models.TextField(blank=True, null=True)
    doc_type = models.TextField(blank=False, null=False)
    
    outlines = models.JSONField(blank=True,null=True, default=list)
    references = models.JSONField(blank=True,null=True, default=list)

    hotspot_id = models.TextField()
    shared_id = models.CharField(blank=True, null=True)
    language = models.TextField(blank=True, null=True)

    published_date = models.TextField()
    sources_published_date = models.TextField(blank=True, null=True)
    author = models.TextField(blank=True, null=True)
    cover_urls = models.TextField(blank=True, null=True)
    followup_questions = models.JSONField(blank=True,null=True,default=list)
    
    hide = models.BooleanField(default=False)

    
    class Meta:
        ordering = ['-published_date']
        unique_together = (('published_date', 'title', 'doc_type'), ('hotspot_id', 'doc_type'))
    
# class Outline(models.Model):
#     idx = models.IntegerField()
#     title = models.TextField(blank=True,null=True)
#     content = models.TextField(blank=True,null=True)
#     article = models.ForeignKey(DiscoverArticle, related_name='outlines', on_delete=models.CASCADE)
    
#     class Meta:
#         ordering = ['idx']
        
        

# class Reference(models.Model):
#     ref_id = models.BigIntegerField()
#     title = models.TextField(blank=True,null=True)
#     link = models.TextField(blank=True,null=True)
#     published = models.TextField(blank=True, null=True)
#     article = models.ForeignKey(DiscoverArticle, related_name='references', on_delete=models.CASCADE)
    
#     class Meta:
#         ordering = ['ref_id']
        
class APICall(models.Model):
    api_name = models.CharField(default="", max_length=100)
    host = models.CharField(default="", max_length=100, null=True, blank=True)
    url = models.CharField(default="", max_length=1000)
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        blank=True,
        null=True
    )
    sub_type = models.CharField(default="", max_length=100, null=True, blank=True)
    ip = models.CharField(default="", max_length=100)
    reference_id = models.TextField(max_length=100,blank=True, null=True)
    time = models.DateTimeField(auto_now_add=True)
    json_body = models.JSONField(blank=True, null=True)
    text_body = models.TextField(blank=True, null=True)
    query_params = models.JSONField(blank=True, null=True)
    
    def save(self, *args, **kwargs):
        if not self.pk and self.host in ['noahai.co', 'www.noahai.co'] and self.user and self.user.is_authenticated:
            # name = f"{self.user.first_name} {self.user.last_name}" if self.user.first_name and self.user.last_name else None
            type = api_name_translate.get(self.api_name, self.api_name) 
            if type not in ['分享页面', '查看分享链接', '尝试邀请码']:
                type += get_sub_type(self)
            posthog.capture(
                'api_call',
                distinct_id=self.user.email,
                properties={
                    'api_name': self.api_name,
                    'host': "https://"+self.host,
                    'url': self.url,
                    'type': type,
                    'ip': self.ip,
                    'reference_id': self.reference_id,
                    'json_body': self.json_body,
                    'text_body': self.text_body,
                    'query_params': self.query_params,
                }
            )
        super().save(*args, **kwargs)