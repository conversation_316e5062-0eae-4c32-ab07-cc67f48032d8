from django import forms
from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from django.utils.translation import gettext_lazy
from .models import CreditTransaction, User, Organization, PricePlan
from import_export.admin import ExportMixin
from import_export import resources, fields
from import_export.widgets import ForeignKeyWidget

# Register your models here.

# admin.site.register(PricePlan)
class HandlerFilter(admin.SimpleListFilter):
    title = 'Handlers'
    parameter_name = 'handler_staff'
    
    def lookups(self, request, model_admin):
        
        staff_handlers = User.objects.filter(
            is_staff=True, is_superuser=True, 
        )
        return [(user.id, user.username) for user in staff_handlers]
    
    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(handler__id=self.value(), handler__is_staff=True)
        return queryset

    def has_add_permission(self, request):
        return True

# @admin.register(Organization)
class NoahOrganization(admin.ModelAdmin):
    search_fields = ['name']
    list_display = ['id', 'name', 'price_plan', 'number_of_members']
    list_filter = ['price_plan']
    
    autocomplete_fields = ['members', 'managers']

    @admin.display(empty_value=0)
    def number_of_members(self, obj):
        return len(obj.members.all())


@admin.action(description="Change price plan of seleted users to pro")
def price_plan_pro(modeladmin, request, queryset):
    pro = PricePlan.objects.get(name='pro')
    queryset.update(price_plan=pro)


@admin.action(description="Change price plan of seleted users to free")
def price_plan_free(modeladmin, request, queryset):
    free = PricePlan.objects.get(name='free')
    queryset.update(price_plan=free)


class UserResource(resources.ModelResource):
    
    invited = fields.Field(
        column_name='invited',
        attribute='extra',
        widget=fields.widgets.CharWidget()
    )
    
    source = fields.Field(
        column_name='source',
        attribute='extra',
        widget=fields.widgets.CharWidget()
    )
    
    def dehydrate_invited(self, user):
        if not user.extra:
            return False
        return user.extra.get('invited', False)
    
    def dehydrate_source(self, user):
        if not user.extra:
            return 'Noah'
        return user.extra.get('source', 'Noah')
    
    class Meta:
        model = User
        fields = ('id', 'username', 'invited', 'source', 'extra', 'email', 'first_name', 'last_name', 'is_active', 
                 'is_staff', 'is_superuser', 'date_joined', 'trial_tier')
        export_order = ('id', 'username', 'invited', 'source', 'extra', 'email', 'first_name', 'last_name', 'is_active',
                       'is_staff', 'is_superuser', 'date_joined', 'trial_tier')

@admin.register(User)
class NoahUserAdmin(ExportMixin, UserAdmin):
    resource_class = UserResource    
    fieldsets = (
        (None,
         {'fields': ('username', 'password')}),
        (gettext_lazy('Personal info'),
         {'fields': ('first_name', 'last_name', 'email', 'invited', 'email_verified', 'trial_tier', 'price_plan', 'extra')}),
        (
            gettext_lazy('Permissions'),
            {
                'fields': (
                    'is_active',
                    'is_staff',
                    'is_superuser',
                    'groups',
                    'user_permissions',
                ),
            },
        ),
        (gettext_lazy('Important dates'), {'fields': ('last_login', 'date_joined')}),
    )
    list_display = (
        'username', 'email', 'invited', 'source', 'date_joined', 'first_name', 'last_name', 'is_staff', 'is_superuser', 'trial_tier',
        
    )
    list_filter = [
        'is_staff', 'is_superuser', 'is_active', 
        ('organizations', admin.RelatedOnlyFieldListFilter)
    ]
    actions = [price_plan_free, price_plan_pro]
    list_editable = ['trial_tier']
    readonly_fields = ['credits', 'invited', 'source']
    
    def invited(self, obj):
        if not obj.extra:
            return False
        return obj.extra.get('invited', False)
    
    def source(self, obj):
        if not obj.extra:
            return 'Noah'
        return obj.extra.get('source', 'Noah')

    def save_model(self, request, obj, form, change):
        obj.email = obj.username
        new = not bool(obj.pk)
        try:
            if not obj.price_plan:
                obj.price_plan = PricePlan.objects.get(name='free')
            if not obj.extra:
                obj.extra = {}
            if new:
                obj.extra['invited'] = True
        except Exception as e:
            print(f"Error in saving user: {e}")
            pass
        super().save_model(request, obj, form, change)
        if new:
            CreditTransaction.objects.create(
                user=obj,
                amount=2000,
                handler=request.user
            )

@admin.register(CreditTransaction)
class CreditTransactionAdmin(admin.ModelAdmin):
    list_display = ['user', 'amount', 'balance', 'type', 'handler', 'time_created', 'comment']
    list_filter = ['type', HandlerFilter]
    # list_editable = ['type', 'comment']
    search_fields = ['user__username', 'user__email', 'type', 'comment']
    readonly_fields = ['id', 'time_created', 'handler']
    ordering = ['-time_created']
    autocomplete_fields = ['user', 'handler']
    
    def has_add_permission(self, request):
        return False
    
    def has_change_permission(self, request, obj=None):
        return False
    
    fieldsets = (
        (None, {'fields': ('user', 'amount', 'type', 'comment')}),
    )
        
    def save_model(self, request, obj, form, change):
        if not obj.handler:
            obj.handler = request.user
        return super().save_model(request, obj, form, change)

class UserCredit(User):
    class Meta:
        proxy = True
        verbose_name = "用户积分结余"
        verbose_name_plural = "用户积分结余"

@admin.register(UserCredit)
class UserCreditAdmin(admin.ModelAdmin):
    list_display = ['username', 'invited', 'credits', 'credits_updated_at', 'trial_tier']
    search_fields = ['username' ]
    list_filter = ['credits']
    ordering = ['-credits_updated_at']
    readonly_fields = ['invited']
    
    def invited(self, obj):
        if not obj.extra:
            return False
        return obj.extra.get('invited', False)
    
    def has_delete_permission(self, request, obj=None):
        return False
    
    def has_add_permission(self, request):
        return False
    
    def has_change_permission(self, request, obj=None):
        return False
    
class HandledCreditTransaction(CreditTransaction):
    class Meta:
        proxy = True
        verbose_name = "积分管理记录"
        verbose_name_plural = "积分管理记录"
        

class HandledCTForm(forms.ModelForm):

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if 'amount' in self.fields:
            self.fields['amount'].label = '赠送积分'

    class Meta:
        fields = ('amount',)
        
@admin.register(HandledCreditTransaction)
class HandledCreditTransactionAdmin(admin.ModelAdmin):
    list_display = ['user', 'display_amount', 'balance', 'type', 'handler', 'comment', 'time_created']
    list_filter = ['type', HandlerFilter]
    list_editable = ['type', 'comment']
    search_fields = ['user__username', 'user__email', 'type', 'comment']
    readonly_fields = ['id', 'time_created', 'handler', 'balance']
    autocomplete_fields = ['user', 'handler']
    ordering = ['-time_created']
    form = HandledCTForm

    @admin.display(ordering="amount", description="赠送积分")
    def display_amount(self, obj):
        return obj.amount

    def get_queryset(self, request):
        return super().get_queryset(request).filter(handler__isnull=False)

    def has_change_permission(self, request, obj=None):
        if obj:
            return False
        return True
    
    def get_fieldsets(self, request, obj = ...):
        if obj and obj.pk:
            return ((None, {'fields': ('user', 'display_amount', 'type', 'comment')}),)
        else:
            return ((None, {'fields': ('user', 'amount', 'type', 'comment')}),)
        # return super().get_fieldsets(request, obj)
    
    

        
    def save_model(self, request, obj, form, change):
        if not obj.handler:
            obj.handler = request.user
        return super().save_model(request, obj, form, change)