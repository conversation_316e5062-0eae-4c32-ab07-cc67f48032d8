import json
import urllib
from django import urls
from django.core import signing
from django.core.mail import send_mail
from django.core.exceptions import ObjectDoesNotExist
from django.core.signing import SignatureExpired
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from utils.exceptions import EmailInvalidError
from smtplib import SMTPRecipientsRefused
from django.utils import timezone
from django.http import Http404
from django.shortcuts import render, redirect
from django.db import transaction
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.conf import settings
from rest_framework.authtoken.models import Token
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework import viewsets
from rest_framework import generics
from rest_framework import permissions, throttling, exceptions
from API.throttle import ExtendedAnonRateThrottle, ExtendedUserRateThrottle
from rest_framework.exceptions import AuthenticationFailed
from django.utils.translation import gettext_lazy as _
import hmac
import hashlib
import base64
from django.contrib.auth.hashers import make_password
from django.conf import settings
import logging

from Account.models import User
from Account.serializers import (
    OrgListCreateSerializer,
    OrgInfoSerializer,
    UserSerializer, UserPasswordSerializer, UserDetailSerializer
)
from Account.token import TokenManager
from Account.exceptions import InvalidToken, UserNotFound
from Account.authentication import token_expire_handler
import utils.permissions as perm
from rest_framework.authtoken.views import ObtainAuthToken

logger = logging.getLogger(__name__)


# Create your views here.

# add token for user if not exist
@receiver(post_save, sender=settings.AUTH_USER_MODEL)
def create_auth_token(sender, instance=None, created=False, **kwargs):
    if created:
        Token.objects.create(user=instance)
    else:
        pass

class TokenAuthView(ObtainAuthToken):
    authentication_classes = []
    
    def post(self, request, *args, **kwargs):
        if 'username' not in request.data or not request.data['username']:
            raise AuthenticationFailed("Username is required")
        try:
            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)
        except Exception as e:
            # raise AuthenticationFailed("Invalid credentials")
            request.data['username'] = request.data['username'].lower()
            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)
        user = serializer.validated_data['user']
        user.last_login = timezone.now()
        user.save()
        token, created = Token.objects.get_or_create(user=user)
        if user.trial_tier:
            is_expired, token = token_expire_handler(user, token)
            if is_expired:
                raise AuthenticationFailed("Trial expired")
        #     _, deleted = Token.objects.filter(user=user).delete()
        return Response({'token': token.key})

class SendVerificationEmailThrottle(ExtendedAnonRateThrottle):
    
    def get_cache_key(self, request, view):
        return self.cache_format % {
            'scope': self.scope,
            'ident': self.get_ident(request)
        }
        
    def parse_rate(self, rate):
        """
        Given the request rate string, return a two tuple of:
        <allowed number of requests>, <period of time in seconds>
        """
        if rate is None:
            return (None, None)
        num, period = rate.split('/')
        num_requests = int(num)
        duration = {'s': 1, 'm': 60, 't':120, 'h': 3600, 'd': 86400}[period[0]]
        return (num_requests, duration)
    
    rate = '1/two-mins'  # Customize the rate as needed

class UserViewSet(viewsets.ModelViewSet):
    """
    API endpoint that allows users to be viewed or edited.
    """
    lookup_field = 'username'
    
    class SpecificAnonRateThrottle(ExtendedAnonRateThrottle):
        rate = '40/min'  # Customize the rate as needed
        
    class SpecificUserRateThrottle(ExtendedUserRateThrottle):
        rate = '80/min'

    throttle_classes = [SpecificAnonRateThrottle, SpecificUserRateThrottle]
    
    def get_object(self):
        """
        Get the user object based on the username in the URL.
        """
        queryset = self.filter_queryset(self.get_queryset())
        filter_kwargs = {self.lookup_field: self.kwargs[self.lookup_field].lower()}
        obj = generics.get_object_or_404(queryset, **filter_kwargs)
        self.check_object_permissions(self.request, obj)
        return obj
    
    def update(self, request, *args, **kwargs):
        if 'email' in request.data:
            email = request.data['email'].lower()
            if not email:
                return Response(
                    {'error': 'Email is required.'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            if User.objects.filter(email=email).exclude(username=self.request.user.username).exists():
                return Response(
                    {'error': 'User with this email already exists.'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            request.data['email'] = email
        return super().update(request, *args, **kwargs)
    
    # def get_throttles(self):
    #     if self.action == 'create':
    #         return [SendVerificationEmailThrottle()]
    #     return super().get_throttles()

    def get_permissions(self):
        # return permissions
        if self.action == 'create':
            # allow create of users without permission
            return [permissions.AllowAny()]
        else:
            # other actions need IsOwnerOrAdmin permission
            return [perm.IsOwnerOrAdminForUserView()]

    def get_queryset(self):
        queryset = User.objects.filter(
            username=self.request.user.username
        )
        return queryset

    def get_serializer_class(self):
        # choose different serializer_class based on url requested
        url_name = urls.resolve(self.request.path_info).url_name
        serializer_class = None
        if url_name == 'user-create':
            serializer_class = UserSerializer
        elif url_name == 'user-password':
            serializer_class = UserPasswordSerializer
        elif url_name in ['user-detail', 'user-modify']:
            serializer_class = UserDetailSerializer
        else:
            pass
        return serializer_class
    
    def create(self, request, *args, **kwargs):
        """
        Create a new user.
        """
        validated_data = request.data.copy()
        validated_data['password'] = make_password(validated_data.pop('password'))
        validated_data['email'] = email = validated_data.get('email', '').lower()
        validated_data['username'] = validated_data.get('username', '').lower()
        if not email:
            return Response(
                {'error': 'Email is required.'},
                status=status.HTTP_400_BAD_REQUEST
            )
        if User.objects.filter(email=email).exists():
            return Response(
                {'error': 'User with this email already exists.'},
                status=status.HTTP_400_BAD_REQUEST
            )
        validated_data.pop('receive_email', None)
        secret = settings.SECRET_TESTING_KEY
        if not secret:
            raise ValueError("settings missing.")
        # email = validated_data.get('email', '')
        token_manager = TokenManager()
        # First encode the data
        encoded_data = token_manager.perform_encoding(json.dumps(validated_data))

        # Create an HMAC signature using the secret
        h = hmac.new(secret.encode(), encoded_data.encode(), hashlib.sha256)
        signature = base64.urlsafe_b64encode(h.digest()).decode('utf-8').rstrip('=')

        # Create the verification link with the token
        link = settings.EMAIL_VERIFICATION_URL.format(encoded_data, signature)
                
        msg = render_to_string(
            settings.EMAIL_VERIFICATION_MESSAGE_TEMPLATE,
            {"link": link}
        )
        try:
            send_mail(
                settings.EMAIL_VERIFICATION_SUBJECT,
                strip_tags(msg),
                from_email=settings.EMAIL_VERIFICATION_FROM_EMAIL,
                recipient_list=[email],
                html_message=msg
            )
            logger.info('email verification send: {}'.format(email))
        except SMTPRecipientsRefused as e:
            logger.error(e)
            raise EmailInvalidError
        except Exception as e:
            logger.error(e)
            
        return Response({'status': 'verification email sent'}, status=status.HTTP_201_CREATED)

    def throttled(self, request, wait):
        """
        If request is throttled, determine what kind of exception to raise.
        """
        raise CustomThrottled(wait)

class CustomThrottled(exceptions.Throttled):
    status_code = status.HTTP_429_TOO_MANY_REQUESTS
    default_detail = _('')
    extra_detail_singular = _('Retry in {wait} second.')
    extra_detail_plural = _('Retry in {wait} seconds.')
    default_code = 'throttled'

class OrgListCreate(generics.ListCreateAPIView):
    serializer_class = OrgListCreateSerializer
    permission_classes = [perm.HasOrganizationPermission]

    def get_queryset(self):
        user = self.request.user
        queryset = user.organizations.all()
        return queryset


class OrgInfo(generics.RetrieveUpdateDestroyAPIView):
    serializer_class = OrgInfoSerializer
    permission_classes = [perm.HasOrganizationPermission]

    def get_queryset(self):
        queryset = self.request.user.organizations.all()
        return queryset


class EmailVerification(APIView):
    permission_classes = [permissions.AllowAny]
    authentication_classes = []
    
    def post(self, request, userdata, userhash):
        token_manager = TokenManager()
        secret = settings.SECRET_TESTING_KEY
        if not secret:
            raise ValueError("settings missing.")
        try:
            with transaction.atomic():
                # Verify the signature
                h = hmac.new(secret.encode(), userdata.encode(), hashlib.sha256)
                expected_signature = base64.urlsafe_b64encode(h.digest()).decode('utf-8').rstrip('=')

                if not hmac.compare_digest(expected_signature, userhash):
                    raise InvalidToken("Signature does not match")

                validated_data = token_manager.perform_decoding(userdata)
                validated_data = json.loads(validated_data)
                try:
                    user = User.objects.create(**validated_data)
                except ValueError as e:
                    return Response(
                        "You have already verified your email! You are all set.", status=status.HTTP_400_BAD_REQUEST
                    )
                user.is_active = True
                user.email_verified = True
                user.save()
                token = token_manager.generate_token(user)
                return Response({'status': 'User verified and created successfully.', 'token': token}, status=status.HTTP_201_CREATED)
        except (ValueError, TypeError, base64.binascii.Error) as error:
            return Response(
                str(error), status=status.HTTP_400_BAD_REQUEST
            )
        except SignatureExpired:
            return Response(
                'The token expired, please request a new one.',
                status=status.HTTP_408_REQUEST_TIMEOUT
            )
        except InvalidToken as e:
            return Response(
                f'The token is invalid or used, please request a new one: {e}',
                status=status.HTTP_401_UNAUTHORIZED
            )
        except UserNotFound:
            return Response(
                'User not found.',
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                'Verification failed. Please check your email and try again.',
                status=status.HTTP_400_BAD_REQUEST
            )

class RequestResetPasswordLink(APIView):
    permission_classes = [permissions.AllowAny]
    
    # def get_throttles(self):
    #     return [SendVerificationEmailThrottle()]

    def post(self, request):
        token_manager = TokenManager()
        print(request.POST)
        if 'email' in request.data:
            request.data['email'] = useremail = request.data['email'].lower()
            try:
                user = User.objects.get(email=useremail)
                # generate link
                token = token_manager.generate_token(user)
                encoded_email = token_manager.perform_encoding(useremail)
                link = settings.RESET_PASSWORD_URL.format(
                    encoded_email, token, urllib.parse.quote(useremail)
                )
                # make message from template
                msg = render_to_string(
                    settings.RESET_PASSWORD_MESSAGE_TEMPLATE,
                    {"link": link},
                    request=request
                )
                send_mail(
                    settings.RESET_PASSWORD_SUBJECT,
                    strip_tags(msg),
                    from_email=settings.RESET_PASSWORD_FROM_EMAIL,
                    recipient_list=[useremail],
                    html_message=msg
                )
                response_info = 'The password-reset link has been sent to the email, please click the link within 24 hours.'
                return Response(
                    response_info, status=status.HTTP_200_OK
                )
            except ObjectDoesNotExist:
                response_info = 'The user with the email does not exist, please check the email address.'
                return Response(
                    response_info, status=status.HTTP_404_NOT_FOUND
                )
        else:
            return Response(
                '"email" keyword should be provided in the data.',
                status=status.HTTP_400_BAD_REQUEST
            )

    def throttled(self, request, wait):
        """
        If request is throttled, determine what kind of exception to raise.
        """
        raise CustomThrottled(wait)

class ResetPassword(APIView):
    permission_classes = [permissions.AllowAny]

    def post(self, request, useremail, usertoken):
        token_manager = TokenManager()
        try:
            user = request.user
            
            if user.is_authenticated and user.trial:
                return Response(
                    'Trial users cannot reset password.',
                    status=status.HTTP_400_BAD_REQUEST
                )
                # raise ValueError('Trial users cannot reset password.')
            if 'password' in request.data:
                verified, user = token_manager.verify(useremail, usertoken)
                if verified:
                    user.last_login = timezone.now()
                    password = request.data['password']
                    user.set_password(password)
                    user.save()
                    return Response('Reset succeeded.', status=status.HTTP_200_OK)
                else:
                    raise ValueError
            else:
                return Response(
                    '"password" should be provided in the POST data.',
                    status=status.HTTP_400_BAD_REQUEST
                )
        except (ValueError, TypeError) as error:
            return Response(
                error, status=status.HTTP_400_BAD_REQUEST
            )
        except SignatureExpired:
            return Response(
                'The token expired, please request a new one.',
                status=status.HTTP_408_REQUEST_TIMEOUT
            )
        except InvalidToken:
            return Response(
                'The token is invalid or used, please request a new one ',
                status=status.HTTP_401_UNAUTHORIZED
            )
        except UserNotFound:
            return Response(
                'User not found.',
                status=status.HTTP_404_NOT_FOUND
            )
