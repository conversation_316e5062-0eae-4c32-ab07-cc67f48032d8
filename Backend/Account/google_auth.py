import traceback
import urllib
from django import urls
from django.core.mail import send_mail
from django.core.exceptions import ObjectDoesNotExist
from django.core.signing import SignatureExpired
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from django.utils import timezone
from django.http import Http404
from django.shortcuts import render, redirect
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.conf import settings
from rest_framework.authtoken.models import Token
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import (
    generics,
    throttling,
    permissions)

from Account.models import User
from Account.token import TokenManager
from API.throttle import ExtendedAnonRateThrottle, ExtendedUserRateThrottle
import utils.permissions as perm
from rest_framework.authentication import TokenAuthentication
from Account.views import create_auth_token
from google.oauth2 import id_token
from google.auth.transport import requests


class GoogleLogin(generics.GenericAPIView):
    
    class SpecificAnonRateThrottle(ExtendedAnonRateThrottle):
        rate = '5/min'  # Customize the rate as needed
        
    class SpecificUserRateThrottle(ExtendedUserRateThrottle):
        rate = '5/min'
        
    authentication_classes = []
    permission_classes = [permissions.AllowAny]
    throttle_classes = [SpecificAnonRateThrottle, SpecificUserRateThrottle]
        
        
    def post(self, request, *args, **kwargs):
        try:
            if request.user.is_authenticated:
                return Response({'error': 'Already logged in'}, status=403)
            if 'google_token' not in request.data:
                return Response({'error': 'Token required'}, status=400)
            
            # csrf_token_cookie = self.request.cookies.get('g_csrf_token')
            # if not csrf_token_cookie:
            #     err_msg = 'No CSRF token in Cookie.'
            #     return Response({'error': err_msg}, status=400) 
            # csrf_token_body = self.request.get('g_csrf_token')
            # if not csrf_token_body:
            #     err_msg = 'No CSRF token in post body.'
            #     return Response({'error': err_msg}, status=400) 
            # if csrf_token_cookie != csrf_token_body:
            #     err_msg = 'Failed to verify double submit cookie.'
            #     return Response({'error': err_msg}, status=400) 
            try:
                # Specify the CLIENT_ID of the app that accesses the backend:
                google_token = request.data['google_token']
                idinfo = id_token.verify_oauth2_token(google_token, requests.Request(), settings.GOOGLE_AUTH_CLIENT_ID)
                # ID token is valid. Get the user's Google Account ID from the decoded token.
                userid = idinfo['sub']
                email = idinfo['email']
                user, created = User.objects.get_or_create(
                    defaults={
                    "username":email,
                    "first_name":idinfo.get('given_name', ''),
                    "last_name":idinfo.get('family_name', ''),
                    "extra":{
                        "source": "google", 
                        "google_uid": userid,
                        "google_idinfo": idinfo
                        }}, 
                    email=email)
                if not user.extra:
                    user.extra = {
                        "source": "google", 
                        "google_uid": userid,
                        "google_idinfo": idinfo
                    }
                if not user.extra.get('google_uid', None):
                    user.extra['google_uid'] = userid
                user.extra["google_idinfo"] = idinfo
                user.save()

                token_manager = TokenManager()
                token = token_manager.generate_token(user)
                token, _ = Token.objects.get_or_create(user=user)
                return Response({'token': token.key, 'email': email, 'is_new_user':created}, status=200)
            except ValueError:
                traceback.print_exc()
                print(f"Invalid token in GoogleLogin")
                return Response({'error': 'Invalid token'}, status=401)  
                
        except Exception as e:
            traceback.print_exc()
            print(f"Error in GoogleLogin")
            return Response({'error': str(e)}, status=500)  