import datetime
import pickle
import pandas as pd
import os
import io
import csv
from django.http import FileResponse
from django.utils import timezone
import base64
from API.models import Thread, Task
from Account.models import User
from Discover.models import APICall
from django.conf import settings

thread_objs = Thread.objects.using("prod") if "prod" in settings.DATABASES else Thread.objects
task_objs = Task.objects.using("prod") if "prod" in settings.DATABASES else Task.objects
user_objs = User.objects.using("prod") if "prod" in settings.DATABASES else User.objects
apicall_objs = APICall.objects.using("prod") if "prod" in settings.DATABASES else APICall.objects

# ------------------
# 图函数
# ------------------
  
def dataframe_csv_fileresponse(dataframe, filename):
    bytesio = io.BytesIO()
    dataframe.to_csv(bytesio, index=False, quoting=csv.QUOTE_ALL)
    bytesio.seek(0)
    response = FileResponse(bytesio, filename=filename)
    return response

def get_task_obj_type(task: dict, thread: Thread):
    context = thread.context
    type = 'explore'
    if context:
        type = context.get('reference_type', 'explore')
        if type == 'discoverArticle':
            type = 'discover'
        if type == 'workflow':
            type = context.get('reference', 'workflow')
            if len(type) >= 30 or any(char.isdigit() for char in type):
                type = 'workflow'
    # return type + '提问'
    if task.agent_name.startswith('mindsearchofficialsite'):
        type = '医学搜索'
    elif task.agent_name.startswith('mindsearchpubmed'):
        type = 'PubMed搜索'
    elif task.agent_name in ['mindsearch', 'mindsearchen', 'mindsearchcn', 'mindsearchjp'] or task.agent_name.startswith('mindsearchv2'):
        type = '全网搜索'
    elif task.agent_name == 'planning':
        type = 'Agent'
    if not task.idx:
         return type + '首问'
    return type + '追问'

def hasher(func_name):
    # anon_users = params.get('anon_users', False)
    # source = params.getlist('source', ['all'])
    # stat = params.get('stat', '按日期计总数')
    
    # try:
    #     start_date = datetime.date.fromisoformat(params.get('start_date', None))
    #     start_delta = (start_date - datetime.date(2024, 1, 1)).days
    # except Exception as e:
    #     start_delta = 0
    # try:
    #     end_date = datetime.date.fromisoformat(params.get('end_date', None))
    #     end_delta = (end_date - datetime.date(2024, 1, 1)).days
    # except Exception as e:
    #     end_delta = 10000
    
    # start_delta = max(0, min(10000, start_delta))
    # end_delta = max(0, min(10000, end_delta))
    # p2_str = f'{start_delta:016b}{end_delta:016b}'
    
    # hash_bits = '1' if anon_users else '0'
    # p3 = ['buzz每日快讯', 'discover热点文章','临床结果', '临床结果追问','explore首次问', 'explore追问', 'buzz首次问', 'buzz追问', 'discover首次问', 'discover追问', 'workflow追问']
    # p3_str = ''.join(['1' if item in source else '0' for item in p3])
    
    p4 = [None, 'bar_explore_tasks', 'bar_workflow_tasks', 'bar_discover_reads']
    try: func_idx = p4.index(func_name)
    except: func_idx = 0
    p4_str = f'{func_idx:004b}'
        
    p4_str = ''.join(['1' if func_name == item else '0' for item in p4])
    
    hash_bits = '01'+ p4_str + '10'
    
    # print("hash_bits", hash_bits, p2_str, p3_str, p4_str)

    # Convert binary string to bytes and then to base64
    hash_bytes = int(hash_bits, 2).to_bytes((len(hash_bits) + 7) // 8, byteorder='big')
    hash_result = base64.b64encode(hash_bytes).decode('utf-8')
    # print("hash_result", hash_result)
    return datetime.datetime.now().strftime('%y%m%d') + '_' + hash_result

def piccolo(func):
    def wrapper(*args, **kwargs):
        params = args[0]
        data_file = f'cached/{hasher(func.__name__)}_dashb.pkl'
        if 'no_cache' not in params or not params['no_cache']:
            try:
                if os.path.exists(data_file):
                    data = pickle.load(open(data_file, 'rb'))
                    return data
            except Exception as e:
                print('failed to load cache', e)
        res = func(*args, **kwargs)
        try:
            pickle.dump(res, open(data_file, 'wb'))
        except Exception as e:
            print('failed to cache', e)
        return res
    return wrapper

def filter_data_by_params(data, params):
    stat = params.get('stat', ['按日期计总数'])
    if type(stat) == list and stat:
        stat = stat[0]
    if data.empty:
        return data
    else:
        # Filter Logic here:
        anon_users = params.get('anon_users', False)
        if type(anon_users) == list and anon_users:
            anon_users = anon_users[0]
        source = params.get('source', ['all'])
        if type(source) == list and source:
            source = source[0]
        user_email = params.get('user_email', [''])
        if type(user_email) == list and user_email:
            user_email = user_email[0]
        
            
        # if 'all' not in source:
        #     data = data[data['type'].isin(source)]
        try: start_date = datetime.date.fromisoformat(params.get('start_date', [None])[0])
        except: start_date = None
        try: end_date = datetime.date.fromisoformat(params.get('end_date', [None])[0])
        except: end_date = None
            
        if user_email:
            data = data[data['user'].str.contains(user_email)]
        if not anon_users:
            data = data[data['anon'] == False]
        if 'all' not in source:
            data = data[data['type'].isin(source)]
        if start_date:
            data = data[data['date'] >= start_date]
        if end_date:
            data = data[data['date'] < end_date + datetime.timedelta(days=1)]

        by = []
        if stat.startswith('按日期'):
            by += ['date']
            if '总数' not in stat: by += ['user']
            by += ['type']
        elif stat == '按活跃用户': by = ['user','type']
        elif stat == '按查询口径': 
            # data = data[data['type'] == '临床结果']
            by = [col for col in data.columns if col not in ['date', 'user', 'anon', 'type']] + ['date', 'user', 'type']
        else: by = ['user','type']

        return data.groupby(by)[by[0]].count().unstack(fill_value=0).reset_index(drop=False).drop('anon', errors='ignore', axis=1)
