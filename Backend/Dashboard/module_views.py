import pandas as pd
from django.urls import reverse
from django.http import FileResponse, JsonResponse, Http404
from django.contrib.admin.views.decorators import staff_member_required

from Dashboard.utils import dataframe_csv_fileresponse, filter_data_by_params, piccolo, thread_objs, task_objs, user_objs, apicall_objs
from Dashboard.module_view_reusables import *
from django.db.models import Q, CharField
from django.db.models.functions import Length

CharField.register_lookup(Length, 'length')

def create_module_stat_set_list(stat_set_name=None):
    base_url = reverse('dashboard:index')
    stat_set_list = [
        {
            'name': 'Modules',
            'stats': [
                {
                    'name': 'exploreNewTasks',
                    'label': 'Explore tasks',
                    'description': chart_desc,
                    'json_url': reverse('dashboard:bar_new_explore_tasks_echart_json'),
                    'csv_url': reverse('dashboard:bar_new_explore_tasks_csv'),
                    'fields': [('anon_users', '包含匿名IP', 'toggle'), 
                               ('source', '来源', 'multi', 
                                ['explore首次问', 'explore追问', 'buzz首次问', 'buzz追问', 'discover首次问', 'discover追问', 'workflow追问']), 
                                ('start_date', '统计起始日期', 'date'),
                                ('end_date', '统计结束日期', 'date'),
                                ('brackets','区间数量'),
                                ('stat', '统计方式', 'single', ['按日期计总数', '按日期计平均数', '按日期计中位数', '按活跃用户', '按区间计用户']),
                                ]
                },
                {
                    'name': 'discoverClicks',
                    'label': 'Discover reads',
                    'description': chart_desc,
                    'json_url': reverse('dashboard:bar_new_discover_reads_echart_json'),
                    'csv_url': reverse('dashboard:bar_new_discover_reads_csv'),
                    'fields': [('anon_users', '包含匿名IP', 'toggle'), 
                               ('source', '来源', 'multi', ['buzz每日快讯', 'discover热点文章',]), 
                                ('start_date', '统计起始日期', 'date'),
                                ('end_date', '统计结束日期', 'date'),
                                ('brackets','区间数量'),
                               ('stat', '统计方式', 'single', ['按日期计总数', '按日期计平均数', '按日期计中位数', '按活跃用户', '按区间计用户']),
                               ]
                },
                {
                    'name': 'workflowTasks',
                    'label': 'Workflow tasks',
                    'description': chart_desc,
                    'json_url': reverse('dashboard:bar_new_workflow_tasks_echart_json'),
                    'csv_url': reverse('dashboard:bar_new_workflow_tasks_csv'),
                    'fields': [('anon_users', '包含匿名IP', 'toggle'), 
                               ('source', '来源', 'multi', ['临床结果', '临床结果追问', '会议', '会议追问', '药品竞争', '药品竞争追问']), 
                                ('start_date', '统计起始日期', 'date'),
                                ('end_date', '统计结束日期', 'date'),
                                ('brackets','区间数量'),
                               ('stat', '统计方式', 'single', ['按日期计总数', '按日期计平均数', '按日期计中位数', '按活跃用户', '按区间计用户', '按查询口径']),
                               ]
                },
            ]
        }
    ]
    for stat in stat_set_list[0]['stats']:
        stat['fields'].append(('user_email', '用户邮箱'))
        # stat['fields'].append(('no_cache', '强制刷新', 'toggle'))

    for i in range(len(stat_set_list)):
        stat_set_list[i]['url'] = base_url + stat_set_list[i]['name'] + '/'
        for j in range(len(stat_set_list[i]['stats'])):
            stat_set_list[i]['stats'][j]['url'] = '{}{}/'.format(
                stat_set_list[i]['url'],
                stat_set_list[i]['stats'][j]['name']
            )

    if stat_set_name:
        return [s for s in stat_set_list if s['name'] == stat_set_name]
    else:
        return stat_set_list

@staff_member_required
def bar_new_explore_tasks_csv(request):
    task_stat = bar_explore_tasks(request.POST)
    return dataframe_csv_fileresponse(task_stat, filename="bar_explore_tasks.csv")
    
@piccolo
def bar_explore_tasks(params):
    tasks = [
        {
            'user': task.owner.email if task.owner else (task.ip if task.ip else '匿名用户'),
            'anon': False if task.owner else True,
            'type': get_task_type(task) if (not task.type or task.type.startswith('explore')) else task.type,
            'date': task.time_created.date()
        } for task in task_objs.filter(
            thread__isnull=False, 
            time_created__gte='2025-01-01'
        ).exclude(
            type__startswith='buzz'
        ).exclude(
            type__startswith='workflow'
        ).select_related('owner')
    ]
    if not tasks:
        return pd.DataFrame()
    data = pd.DataFrame(tasks)
    # Pickle data for caching
    return data 



@staff_member_required
def bar_new_explore_tasks_echart_json(request):
    params = dict(request.POST)
    if params:
        params['no_cache'] = 'on'
    data = bar_explore_tasks(params)
    reads = filter_data_by_params(data, params)
    option = get_options_with_data(reads, request)
    return JsonResponse({'option': option})

@staff_member_required
def bar_new_discover_reads_csv(request):
    call_stat = bar_discover_reads(request.POST)
    return dataframe_csv_fileresponse(call_stat, filename="bar_discover_reads.csv")

@piccolo
def bar_discover_reads(params):
    apicall_filters = {'api_name': 'GetDiscoverArticleDetailsView'}
    thread_filters = {'context__reference_type': 'buzz'}
    calls = [
            {
                'user': call.user.email if call.user else (call.ip if call.ip else '匿名用户'),
                'anon': False if call.user else True,
                'date': call.time.date(),
                'type': 'discover热点文章'
            } for call in apicall_objs.filter(**apicall_filters)
        ] + [
            {
                'user': thread.owner.email if thread.owner else (thread.ip if thread.ip else '匿名用户'),
                'anon': False if thread.owner else True,
                'date': thread.time_created.date(),
                'type': 'buzz每日快讯'
            } for thread in thread_objs.filter(**thread_filters)
        ]
    if not calls:
        return pd.DataFrame()
    data = pd.DataFrame(calls)
    return data

@staff_member_required
def bar_new_discover_reads_echart_json(request):
    params = dict(request.POST)
    if params:
        params['no_cache'] = 'on'
    data = bar_discover_reads(params)
    reads = filter_data_by_params(data, params)
    option = get_options_with_data(reads, request)
    return JsonResponse({'option': option})

@staff_member_required
def bar_new_workflow_tasks_csv(request):
    call_stat = bar_workflow_tasks(request.POST)
    return dataframe_csv_fileresponse(call_stat, filename="bar_workflow_tasks.csv")

@piccolo
def bar_workflow_tasks(params):
                
    # workflow_filters = {'workflow': 'clinical-result'}
    context_filters = {'context__reference_type__in': ['conference','workflow'], 'idx__isnull': False, 'time_created__gte': '2025-01-01'}
    thread_filters = {'thread__context__reference_type__in': ['conference','workflow'], 'thread__context__reference_title__isnull':True}
    combined_filters = Q(**context_filters) | Q(**thread_filters) 
    calls = [
            {
                'user': task.owner.email if task.owner else (task.ip if task.ip else '匿名用户'),
                'anon': False if task.owner else True,
                'date': task.time_created.date(),
                'type': ref_type,
                **get_params(task.parameter_json)
            } for task in task_objs.filter(combined_filters).select_related('owner', 'thread') if (ref_type:=get_reference(task)) != '未知'
        ]
    calls += [
            {
                'user': call.user.email if call.user else (call.ip if call.ip else '匿名用户'),
                'anon': False if call.user else True,
                'date': call.time.date(),
                'type': api_name_translate.get(call.api_name, call.api_name) + get_sub_type(call),
            } for call in apicall_objs.filter(time__gte='2025-01-01').exclude(api_name__contains='Discover').exclude(api_name='ConferenceTypeDetails').select_related('user')
    ]
    if not calls:
        return pd.DataFrame()
    data = pd.DataFrame(calls)
    data.fillna(0, inplace=True)
    return data

@staff_member_required
def bar_new_workflow_tasks_echart_json(request):
    params = dict(request.POST)
    if params:
        params['no_cache'] = 'on'
    data = bar_workflow_tasks(params)
    reads = filter_data_by_params(data, params)
    option = get_options_with_data(reads, request)
    return JsonResponse({'option': option})
