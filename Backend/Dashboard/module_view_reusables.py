import datetime
import pandas as pd

chart_desc = '''<li>统计匿名IP 会将单一IP视作一个匿名用户进行统计</li>
                <li>按日期计总数/平均数/中位数 会按照日期范围统计有相关操作的活跃用户的计量值</li>
                <li>按查询口径 会按照日期范围统计workflow查询口径数量总数和占比</li>
                <li>按活跃用户 会按照起止日期范围统计有相关操作的活跃用户的计量值，x轴显示各个用户计量值</li>
                <li>按区间计用户 会按照起止日期范围统计有相关操作的活跃用户的计量值，x轴显示各个计量值区间的用户数量，可以设置区间数量</li>'''
                # <li>强制刷新 会忽略缓存重新用最新数据绘制图表，用户邮箱 会针对用户邮箱进行字符串匹配，只显示这批用户数据'''
                
cur_workflows = ['临床结果', '临床结果追问', '会议', '会议追问', '药品竞争', '药品竞争追问']

api_name_translate = {
    "CatalystQuery": "催化剂事件",
    "ClinicalGuidelines": "临床指南",
    "ClinicalGuidelineQuery": "临床指南主树",
    "ClinicalGuidelineSubTreeQuery": "临床指南子树",
    "ClinicalResultQuery": "临床结果",
    "ClinicalResultQueryDetails": "临床结果详情页",
    "ConferenceQuery": "会议",
    "ConferenceQueryDetails": "会议详情",
    "DrugCompeteQuery": "药品竞争",
    "DrugCompeteQueryDetails": "药品竞争详情",
    "ConferenceTypeList": "会议类型列表",
    # "ConferenceTypeDetails": "会议类型详情",
    "WorkflowTypeDetails": "工作流类型详情",
    "GetDiscoverArticleDetailsView": "新闻详情",
    "GetDiscoverArticleListView": "新闻列表",
    "ShareThread": "分享页面",
    "ViewSharedLink": "查看分享链接",
    "ReferralClaim": "尝试邀请码",
    "WaitlistViewSet": "Waitlist",
}
    
baseOption_by_interval = {
    'baseOption': {
        'timeline': {
            'axisType': 'category',
            'data': ['日', '周', '月', '年']
        },
        'toolbox': {
            'show': True,
            'feature': {
                'mark': { 'show': False },
                'dataView': { 'show': True, 'readOnly': True },
                'magicType': { 'show': True, 'type': ['line', 'bar'] },
                'restore': { 'show': True },
                'saveAsImage': { 'show': True }
            }
        },
        'legend': {
            'orient': 'vertical',
            'right': '2%',
            'top': '10%',
            'textStyle': {
                'fontSize': 12
            },
            'itemWidth': 15,
            'itemHeight': 10,
            'itemGap': 2
        },
        'tooltip': {
            'trigger': 'axis',
            'axisPointer': {
                'type': 'shadow'
            }
        },
        'grid': {
            'bottom': '20%',
            'right': '25%'
        },
        'dataZoom': [
            {
                'type': 'inside',
                'xAxisIndex': [0, 1],
                'start': 0,
                'end': 100
            },
            {
                'show': True,
                'xAxisIndex': [0, 1],
                'type': 'slider',
                'bottom': '10%',
                'start': 0,
                'end': 100
            }
        ],
        'calculable': True,
        'xAxis': {
            'type': 'time',
            'name': 'Date',
            'nameGap': 15,
        },
        'yAxis': [{
            'type': 'value',
            'name': 'No. of instances',
        }]
    }
}

baseOption_by_user = {
    'baseOption': {
        'timeline': {
            'axisType': 'category',
            'data': ['日期范围内']
        },
        'toolbox': {
            'show': True,
            'feature': {
                'mark': { 'show': False },
                'dataView': { 'show': True, 'readOnly': True },
                'magicType': { 'show': True, 'type': ['line', 'bar'] },
                'restore': { 'show': True },
                'saveAsImage': { 'show': True }
            }
        },
        'legend': {
            'orient': 'vertical',
            'right': '2%',
            'top': '10%',
            'textStyle': {
                'fontSize': 12
            },
            'itemWidth': 15,
            'itemHeight': 10,
            'itemGap': 2
        },
        'tooltip': {
            'trigger': 'axis',
            'axisPointer': {
                'type': 'shadow'
            }
        },
        'grid': {
            'bottom': '20%'
        },
        'calculable': True,
        'xAxis': {
            'data': [],
        },
        'yAxis': [{
            'type': 'value',
            'name': 'No. of instances',
        },
        {
            'type': 'value',
            'name': 'Percentage',
        }]
    }
}

def get_data_by_interval_count(data, types):
    if data.empty:
        return []
    
    data = data.assign(
        year=data.date.map(lambda a: a.year),
        yearmonth=data.date.map(lambda a: a.month),
        yearweek=data.date.map(lambda a: a.isocalendar().week)
    )
    data = data.assign(
        month=data.apply(
            lambda a: datetime.date(
                year=a['year'], month=a['yearmonth'], day=1
            ),
            axis=1
        ),
        week=data.apply(
            lambda a: datetime.date.fromisocalendar(
                year=a['year'],
                week=a['yearweek'],
                day=7
            ),
            axis=1
        )
    )
    data_lists = [{data_type: [
            [str(row[interval]), int(row[data_type])]
            for (ri, row) in data[[interval, data_type]].groupby([interval]).agg({data_type: 'sum'}).reset_index().iterrows()
        ] for data_type in types} for interval in ['date', 'week', 'month', 'year']]
    if 'user' in data.columns:
        for i, interval in enumerate(['date', 'week', 'month', 'year']):
            data_lists[i]['count'] = [[str(date), cnt]
            for (date, cnt) in data.groupby([interval]).count()['user'].to_dict().items()]
    return data_lists

def get_data_by_interval_agg(data, types, agg='mean'):
    if data.empty:
        return []
    
    data = data.assign(
        total=sum(data[type] for type in types),
    )
    
    data = data.assign(
        year=data.date.map(lambda a: a.year),
    )
    data = data.assign(
        month=data.apply(
            lambda a: datetime.date(
                year=a['year'], month=a['date'].month, day=1
            ),
            axis=1
        ),
        week=data.apply(
            lambda a: datetime.date.fromisocalendar(
                year=a['year'],
                week=a['date'].isocalendar().week,
                day=7
            ),
            axis=1
        )
    )
    data_lists = [{agg: [
            (k, v) for k,v in
            data.groupby([interval])['total'].agg(agg).to_dict().items()
        ]} for interval in ['date', 'week', 'month', 'year']]
    if 'user' in data.columns:
        for i, interval in enumerate(['date', 'week', 'month', 'year']):
            data_lists[i]['用户数量'] = [(date, cnt)
            for (date, cnt) in data.groupby([interval]).count()['user'].to_dict().items()]
    return data_lists

def get_data_by_user(data, types):
    if data.empty:
        return [], {}
    agg_stats = {}
    data = data.assign(
        total=sum(data[type] for type in types),
    )
    plot_data_user = {
        data_type: [
            [str(row['user']),  int(row[data_type])]
            for ri, row in data.iterrows()
        ] for data_type in types
    }
    agg_stats['中位数'] = round(data.total.median(), 2)
    agg_stats['平均数'] = round(data.total.mean(), 2)
    agg_stats['最大值'] = round(data.total.max(), 2)
    agg_stats['最小值'] = round(data.total.min(), 2)
    return [plot_data_user], agg_stats

def get_data_by_brackets(data, types, n):
    if data.empty:
        return [], []
    
    data = data.assign(
        total=sum(data[type] for type in types),
    )
    max_total = data.total.max()+1
    min_total = 0
    _range = max_total - min_total
    brackets = sorted(set(min_total + _range * i // n for i in range(n+1)))
    n = len(brackets)
    brackets_low = [brackets[i] for i in range(n-1)]
    brackets_high = [brackets[i] for i in range(1,n)]
    
    # Create a new dataframe for bracket counts
    bracket_counts = {f"{low}-{high}": 0 for low, high in zip(brackets_low, brackets_high)}
    for total in data.total:
        for low, high in zip(brackets_low, brackets_high):
            if low <= total < high:
                bracket_counts[f"{low}-{high}"] += 1
                break
    
    plot_data_brackets = {
        'total': [
            [bracket, count]
            for bracket, count in bracket_counts.items()
        ]
    }
    
    return [plot_data_brackets], list(bracket_counts.keys())

def get_params(param_json):
    non_empty = {}
    if not param_json:
        return non_empty
    for key, value in param_json.items():
        if isinstance(value, list):
            if value:
                non_empty[key] = 1
        elif isinstance(value, dict):
            if 'data' in value and value['data']:
                non_empty[key] = 1
    return non_empty
            
def get_data_by_param_type(data, types: list):
    if data.empty:
        return []
    data = data.assign(
        total=sum(data[type] for type in types if type not in cur_workflows),
    )
    # types = list(types) + ['临床结果']
    # total = len(data)
    data = data.assign(
        year=data.date.map(lambda a: a.year),
        yearmonth=data.date.map(lambda a: a.month),
        yearweek=data.date.map(lambda a: a.isocalendar().week)
    )
    data = data.assign(
        month=data.apply(
            lambda a: datetime.date(
                year=a['year'], month=a['yearmonth'], day=1
            ),
            axis=1
        ),
        week=data.apply(
            lambda a: datetime.date.fromisocalendar(
                year=a['year'],
                week=a['yearweek'],
                day=7
            ),
            axis=1
        )
    )
    data_lists = [{data_type: [
            [str(row[interval]), int(row[data_type])] 
            for (ri, row) in data[[interval, data_type]].groupby([interval]).agg({data_type: 'sum'}).reset_index().iterrows() 
        ] for data_type in types} for interval in ['date', 'week', 'month', 'year']]
    
    for i, interval in enumerate(['date', 'week', 'month', 'year']):
        grouped = data.groupby([interval]).sum(numeric_only=True)
        columns = grouped.columns
        grouped.reset_index(drop=False, inplace=True)
        for type in types:
            if type in cur_workflows:
                continue
            if type == interval:
                continue
            if type not in columns:
                continue
            if not any(row[type] > 0 for _, row in grouped.iterrows()):
                types.remove(type)
                continue
            # else:
                
            data_lists[i][type+'占比'] = [[str(row[interval]), str(round(row[type]/row['total']*100,2))] for _, row in grouped.iterrows() if row['total'] > 0]
            # else:
            #     types.remove(type)
    return data_lists

def get_options_with_data(reads, request):
    stat = request.POST.get('stat', '按日期计总数')
    stack_by = 'date' if stat.startswith('按日期') or stat == '按查询口径' else 'user'
    option = baseOption_by_interval if stack_by == 'date' else baseOption_by_user
    option['baseOption']['xAxis']['nameGap'] = 15
    types = list(reads.columns.drop('date', errors='ignore').drop('user', errors='ignore').drop('type', errors='ignore'))
    option['baseOption']['yAxis'] = option['baseOption']['yAxis'][:1]
    try: brackets = max(int(request.POST.get('brackets')),1)
    except: brackets = 10
    if stat == '按区间计用户':
        data_list, brackets = get_data_by_brackets(reads, types, brackets)
        types = ['total']
        option['baseOption']['xAxis'] = {'data': brackets}
    elif stat == '按活跃用户':
        data_list, agg_stats = get_data_by_user(reads, types)
        if 'user' in reads.columns:
            users = reads['user'].unique().tolist()
            option['baseOption']['xAxis'] = {'data': users}
        option['baseOption']['title'] = {
            'text': '\n'.join(f"{k}: {v}" for k,v in agg_stats.items()),
            'right': '0%',
            'top': 'center',
            # 'show': True,
        }
    elif stat == '按日期计总数': 
        data_list = get_data_by_interval_count(reads, types)
    elif stat.endswith('平均数'): 
        data_list = get_data_by_interval_agg(reads, types, 'mean')
        types = ['mean', '用户数量']
    elif stat.endswith('中位数'): 
        data_list = get_data_by_interval_agg(reads, types, 'median')
        types = ['median', '用户数量']
    elif stat == '按查询口径':
        data_list = get_data_by_param_type(reads, types)
        if not data_list:
            types = []
        types = [type for type in types if type not in cur_workflows]
        types.extend([type+'占比' for type in types if type not in cur_workflows])
        option['baseOption']['xAxis']['nameGap'] = 40
        if len(option['baseOption']['yAxis']) < 2:
            option['baseOption']['yAxis'].append({
                'type': 'value', 
                'name': '占比',
                'axisLabel': {
                    'formatter': '{value}%'
                },
                'min': 0,
                'max': 100,
            })
    option['baseOption']['series'] = [
        {'name': read_type, 'type': 'bar', 'stack': stack_by} 
        if read_type not in cur_workflows and not read_type.endswith('占比') and read_type != '用户数量'
        else ({'name': read_type, 'type': 'line'} if read_type not in cur_workflows 
              else {'name': read_type, 'type': 'bar', 'stack': stack_by}) 
        for read_type in types
    ]
    option['options'] = [
        {
            'series': [
                {'name': read_type, 'data': plot_data[read_type], 'yAxisIndex': 1 if '占比' in read_type else 0
                }
                for read_type in types
            ]
        }
        for plot_data in data_list
    ]
    return option

def get_sub_type(call):
    query_params = call.query_params
    json_body = call.json_body or query_params
    json_body.pop('format','')
    json_body.pop('lang', '')
    json_body.pop('date', '')
    
    if not json_body:
        return ''
    
    page = json_body.pop('page', '')
    limit = json_body.pop('limit', '')
    event_id = json_body.pop('event_id', '')
    is_hitl = json_body.pop('isHitl', '')
    thread_id = json_body.pop('thread_id', '')
    if type(page) == list:
        page = page[0]
    if type(limit) == list:
        limit = limit[0]
    filters = json_body.pop('filters', {})
    filter_count = 0
    for key in filters:
        if key in ['location', 'locations']:
            loc = filters[key]
            if type(loc) == list:
                if len(loc) <=2 or len(loc) == 8:
                    continue
            if type(loc) == dict:
                loc = loc.get('data', '')
                if len(loc) <= 2 or len(loc) == 8:
                    continue
        if filters[key]:
            val = filters[key]
            if type(val) == list:
                if len(val):
                    filter_count += 1
                    break
            if type(val) == dict:
                val = val.get('data', '')
                if len(val):
                    filter_count += 1
                    break
    print('filter_count', filter_count, 'json_body', json_body, 'page', page, 'limit', limit)
    if not filter_count and not json_body:
        return '进入页面'
    elif limit in [1, "1"]:
        return ''
    elif page not in [1, "1"]:
        return '翻页'
    return '搜索'

def get_task_type(task, save=True):
    context = task.thread.context
    type = 'explore'
    if context:
        type = context.get('reference_type', 'explore')
        if type == 'discoverArticle':
            type = 'discover'
        if type == 'workflow':
            type = context.get('reference', 'workflow')
            if len(type) >= 30 or any(char.isdigit() for char in type):
                type = 'workflow'
    # return type + '提问'
    if task.agent_name.startswith('mindsearchofficialsite'):
        type = '医学搜索'
    elif task.agent_name.startswith('mindsearchpubmed'):
        type = 'PubMed搜索'
    elif task.agent_name in ['mindsearch', 'mindsearchen', 'mindsearchcn', 'mindsearchjp'] or task.agent_name.startswith('mindsearchv2'):
        type = '全网搜索'
    elif task.agent_name == 'planning':
        type = 'Agent'
    if not task.idx:
        ret = type + '首问'
    else:
        ret = type + '追问'
    if save:
        task.type = ret
        task.save()
    return ret

def get_reference(task):
    reference = None
    reference_type = None
    if task and task.context:
        reference = task.context.get('reference', None)
        reference_type = task.context.get('reference_type', None)
    if not reference or reference == 'tool':
        if task.thread and task.thread.context:
            reference = task.thread.context.get('reference', 'Unknown')
            reference_type = task.thread.context.get('reference_type', None)
    if reference:
        if reference_type == 'conference':
            reference = '会议'
        elif reference == 'clinical-result':
            reference = '临床结果'
        elif reference == 'conference':
            reference = '会议'
        elif reference == 'drug-compete':
            reference = '药品竞争'
        elif reference == 'catalyst':
            reference = '催化剂事件'
        elif reference == 'discover':
            reference = '热点文章'
        else:
            return '未知'
        if task.idx == 0:
            return reference + '首问'
        else:
            return reference + '追问'
    else:
        return '未知'