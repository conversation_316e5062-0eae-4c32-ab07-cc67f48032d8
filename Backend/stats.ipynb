{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import datetime\n", "import datetime\n", "import pandas as pd\n", "from django.urls import reverse\n", "from django.http import FileResponse, JsonResponse, Http404\n", "from django.contrib.admin.views.decorators import staff_member_required\n", "import os\n", "import django\n", "os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'NoahBackend.settings.docker_local')\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()\n", "from API.models import Thread, Task\n", "from Discover.models import APICall\n", "from Workflow.models import WorkflowTask\n", "from Dashboard.utils import dataframe_csv_fileresponse, get_task_type\n", "from Dashboard.module_view_reusables import *\n"]}, {"cell_type": "code", "execution_count": 104, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["call_stat1\n", " date      type  2024-11-29  2024-12-05\n", "0     buzz每日快讯           4           1\n"]}], "source": ["apicall_filters = {'api_name': 'GetDiscoverArticleDetailsView'}\n", "thread_filters = {'context__reference_type': 'buzz'}\n", "thread_filters['owner__isnull'] = False\n", "apicall_filters['user__isnull'] = False\n", "calls = []\n", "calls += [\n", "    {\n", "        'user': call.user.email if call.user else (call.ip if call.ip else '匿名用户'),\n", "        'date': call.time.date(),\n", "        'type': 'discover热点文章'\n", "    } for call in APICall.objects.filter(**apicall_filters)\n", "]\n", "calls += [\n", "    {\n", "        'user': thread.owner.email if thread.owner else (thread.ip if thread.ip else '匿名用户'),\n", "        'date': thread.time_created.index(),\n", "        'type': 'buzz每日快讯'\n", "    } for thread in Thread.objects.filter(**thread_filters)\n", "]\n", "data = pd.DataFrame(calls)\n", "# call_stat = call_stat.unstack(fill_value=0).reset_index(drop=False)\n", "# call_stat.groupby('date').mean()"]}, {"cell_type": "code", "execution_count": 281, "metadata": {}, "outputs": [{"data": {"text/plain": ["[(datetime.date(2024, 11, 29), 2), (datetime.date(2024, 12, 5), 1)]"]}, "execution_count": 281, "metadata": {}, "output_type": "execute_result"}], "source": ["data = stat\n", "data = list(data.groupby(['date']).count()['user'].to_dict().items())\n", "data\n"]}, {"cell_type": "code", "execution_count": 280, "metadata": {}, "outputs": [{"data": {"text/plain": ["'2024-11-29'"]}, "execution_count": 280, "metadata": {}, "output_type": "execute_result"}], "source": ["str(data[0][0])"]}, {"cell_type": "code", "execution_count": 234, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>type</th>\n", "      <th>user</th>\n", "      <th>buzz每日快讯</th>\n", "      <th>total</th>\n", "      <th>year</th>\n", "      <th>yearmonth</th>\n", "      <th>yearweek</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2024-11-29</th>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2024</td>\n", "      <td>11</td>\n", "      <td>48</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-11-30</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2024</td>\n", "      <td>11</td>\n", "      <td>48</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-12-01</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2024</td>\n", "      <td>12</td>\n", "      <td>48</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-12-02</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2024</td>\n", "      <td>12</td>\n", "      <td>49</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-12-03</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2024</td>\n", "      <td>12</td>\n", "      <td>49</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-12-04</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2024</td>\n", "      <td>12</td>\n", "      <td>49</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-12-05</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>2024</td>\n", "      <td>12</td>\n", "      <td>49</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["type        user  buzz每日快讯  total  year  yearmonth  yearweek\n", "2024-11-29     2         2      2  2024         11        48\n", "2024-11-30     0         0      0  2024         11        48\n", "2024-12-01     0         0      0  2024         12        48\n", "2024-12-02     0         0      0  2024         12        49\n", "2024-12-03     0         0      0  2024         12        49\n", "2024-12-04     0         0      0  2024         12        49\n", "2024-12-05     1         1      1  2024         12        49"]}, "execution_count": 235, "metadata": {}, "output_type": "execute_result"}], "source": ["data.assign(\n", "    year=data.index.map(lambda a: a.year),\n", "    yearmonth=data.index.map(lambda a: a.month),\n", "    yearweek=data.index.map(lambda a: a.isocalendar().week)\n", ")"]}, {"cell_type": "code", "execution_count": 232, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>type</th>\n", "      <th>user</th>\n", "      <th>date</th>\n", "      <th>buzz每日快讯</th>\n", "      <th>total</th>\n", "      <th>year</th>\n", "      <th>yearmonth</th>\n", "      <th>yearweek</th>\n", "      <th>month</th>\n", "      <th>week</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td><EMAIL></td>\n", "      <td>2024-11-29</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>2024</td>\n", "      <td>11</td>\n", "      <td>48</td>\n", "      <td>2024-11-01</td>\n", "      <td>2024-12-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td><EMAIL></td>\n", "      <td>2024-11-29</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>2024</td>\n", "      <td>11</td>\n", "      <td>48</td>\n", "      <td>2024-11-01</td>\n", "      <td>2024-12-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td><EMAIL></td>\n", "      <td>2024-12-05</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>2024</td>\n", "      <td>12</td>\n", "      <td>49</td>\n", "      <td>2024-12-01</td>\n", "      <td>2024-12-08</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["type                user        date  buzz每日快讯  total  year  yearmonth  \\\n", "0     <EMAIL>  2024-11-29         1      1  2024         11   \n", "1     <EMAIL>  2024-11-29         3      3  2024         11   \n", "2      <EMAIL>  2024-12-05         1      1  2024         12   \n", "\n", "type  yearweek       month        week  \n", "0           48  2024-11-01  2024-12-01  \n", "1           48  2024-11-01  2024-12-01  \n", "2           49  2024-12-01  2024-12-08  "]}, "execution_count": 232, "metadata": {}, "output_type": "execute_result"}], "source": ["data = stat\n", "data = data.assign(\n", "    year=data.index.map(lambda a: a.year),\n", "    yearmonth=data.index.map(lambda a: a.month),\n", "    yearweek=data.index.map(lambda a: a.isocalendar().week)\n", ")\n", "data = data.assign(\n", "    month=data.apply(\n", "        lambda a: datetime.date(\n", "            year=a['year'], month=a['yearmonth'], day=1\n", "        ),\n", "        axis=1\n", "    ),\n", "    week=data.apply(\n", "        lambda a: datetime.date.fromisocalendar(\n", "            year=a['year'],\n", "            week=a['yearweek'],\n", "            day=7\n", "        ),\n", "        axis=1\n", "    )\n", ")\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["workflow_filters = {'workflow': 'clinical-result'}\n", "thread_filters = {'context__reference_type': 'workflow'}\n", "thread_filters['owner__isnull'] = False\n", "workflow_filters['owner__isnull'] = False"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def get_params(param_json):\n", "    non_empty = {}\n", "    if not param_json:\n", "        return non_empty\n", "    for key, value in param_json.items():\n", "        if isinstance(value, list):\n", "            if value:\n", "                non_empty[key] = 1\n", "        elif isinstance(value, dict):\n", "            if 'data' in value and value['data']:\n", "                non_empty[key] = 1\n", "    return non_empty"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["workflow_threads = Thread.objects.filter(**thread_filters)\n", "calls = [\n", "    {\n", "        'user': wf_task.owner.email if wf_task.owner else (wf_task.ip if wf_task.ip else '匿名用户'),\n", "        'date': wf_task.time_created.date(),\n", "        'type': '临床结果',\n", "        **get_params(wf_task.parameter_json)\n", "    } for wf_task in WorkflowTask.objects.filter(**workflow_filters)\n", "] + [\n", "    {\n", "        'user': task.owner.email if wf_thread.owner else (task.ip if task.ip else '匿名用户'),\n", "        'date': task.time_created.date(),\n", "        'type': '临床结果',\n", "        **get_params(task.parameter_json)\n", "    } for wf_thread in workflow_threads for task in Task.objects.filter(thread=wf_thread) if task.parameter_json\n", "]"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>type</th>\n", "      <th>date</th>\n", "      <th>user</th>\n", "      <th>companies</th>\n", "      <th>临床结果</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2024-09-03</td>\n", "      <td><EMAIL></td>\n", "      <td>0.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2024-09-04</td>\n", "      <td><EMAIL></td>\n", "      <td>0.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2024-09-04</td>\n", "      <td><EMAIL></td>\n", "      <td>1.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2024-11-14</td>\n", "      <td><EMAIL></td>\n", "      <td>0.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2024-12-02</td>\n", "      <td><EMAIL></td>\n", "      <td>0.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2024-12-05</td>\n", "      <td><EMAIL></td>\n", "      <td>0.0</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2024-12-06</td>\n", "      <td><EMAIL></td>\n", "      <td>0.0</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2024-12-09</td>\n", "      <td><EMAIL></td>\n", "      <td>0.0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2024-12-12</td>\n", "      <td><EMAIL></td>\n", "      <td>0.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["type        date                user  companies  临床结果\n", "0     2024-09-03   <EMAIL>        0.0     1\n", "1     2024-09-04  <EMAIL>        0.0     1\n", "2     2024-09-04  <EMAIL>        1.0     1\n", "3     2024-11-14  <EMAIL>        0.0     1\n", "4     2024-12-02   <EMAIL>        0.0     1\n", "5     2024-12-05   <EMAIL>        0.0     3\n", "6     2024-12-06   <EMAIL>        0.0     5\n", "7     2024-12-09   <EMAIL>        0.0     2\n", "8     2024-12-12   <EMAIL>        0.0     1"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["data = pd.DataFrame(calls)\n", "data.fillna(0, inplace=True)\n", "cols = set(data.columns)\n", "cols.discard('type')\n", "cols = list(cols) + ['type']\n", "data.groupby(cols)[cols[0]].count().unstack(fill_value=0).reset_index(drop=False)\n", "\n"]}, {"cell_type": "code", "execution_count": 94, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>companies</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2024-09-03</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2024-09-04</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2024-11-14</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2024-12-02</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2024-12-05</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2024-12-06</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2024-12-09</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2024-12-12</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         date  companies\n", "0  2024-09-03        0.0\n", "1  2024-09-04        1.0\n", "2  2024-11-14        0.0\n", "3  2024-12-02        0.0\n", "4  2024-12-05        0.0\n", "5  2024-12-06        0.0\n", "6  2024-12-09        0.0\n", "7  2024-12-12        0.0"]}, "execution_count": 94, "metadata": {}, "output_type": "execute_result"}], "source": ["data.groupby('date').sum(numeric_only=True).reset_index(drop=False)"]}, {"cell_type": "code", "execution_count": 90, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["date 2024-09-03\n", "companies 0.0\n", "date 2024-09-04\n", "companies 1.0\n", "date 2024-11-14\n", "companies 0.0\n", "date 2024-12-02\n", "companies 0.0\n", "date 2024-12-05\n", "companies 0.0\n", "date 2024-12-06\n", "companies 0.0\n", "date 2024-12-09\n", "companies 0.0\n", "date 2024-12-12\n", "companies 0.0\n"]}], "source": ["ir = data.groupby('date').sum(True).reset_index(drop=False).iterrows()\n", "for i, r in ir:\n", "    for k, v in r.items():\n", "        print(k, v)\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                  user        date  type  companies\n", "0   <EMAIL>  2024-09-04  临床结果        0.0\n", "1    <EMAIL>  2024-12-06  临床结果        0.0\n", "2    <EMAIL>  2024-12-06  临床结果        0.0\n", "3    <EMAIL>  2024-12-06  临床结果        0.0\n", "4    <EMAIL>  2024-12-12  临床结果        0.0\n", "5   <EMAIL>  2024-11-14  临床结果        0.0\n", "6    <EMAIL>  2024-12-06  临床结果        0.0\n", "7    <EMAIL>  2024-12-02  临床结果        0.0\n", "8    <EMAIL>  2024-12-09  临床结果        0.0\n", "9   <EMAIL>  2024-09-04  临床结果        1.0\n", "10   <EMAIL>  2024-09-03  临床结果        0.0\n", "11   <EMAIL>  2024-12-05  临床结果        0.0\n", "12   <EMAIL>  2024-12-05  临床结果        0.0\n", "13   <EMAIL>  2024-12-05  临床结果        0.0\n", "14   <EMAIL>  2024-12-06  临床结果        0.0\n", "15   <EMAIL>  2024-12-09  临床结果        0.0\n"]}, {"data": {"text/plain": ["[{'companies': [['2024-09-03', 0],\n", "   ['2024-09-04', 1],\n", "   ['2024-11-14', 0],\n", "   ['2024-12-02', 0],\n", "   ['2024-12-05', 0],\n", "   ['2024-12-06', 0],\n", "   ['2024-12-09', 0],\n", "   ['2024-12-12', 0]],\n", "  'count': [['2024-09-03', 1],\n", "   ['2024-09-04', 2],\n", "   ['2024-11-14', 1],\n", "   ['2024-12-02', 1],\n", "   ['2024-12-05', 3],\n", "   ['2024-12-06', 5],\n", "   ['2024-12-09', 2],\n", "   ['2024-12-12', 1]]},\n", " {'companies': [['2024-09-08', 1],\n", "   ['2024-11-17', 0],\n", "   ['2024-12-08', 0],\n", "   ['2024-12-15', 0]],\n", "  'count': [['2024-09-08', 3],\n", "   ['2024-11-17', 1],\n", "   ['2024-12-08', 9],\n", "   ['2024-12-15', 3]]},\n", " {'companies': [['2024-09-01', 1], ['2024-11-01', 0], ['2024-12-01', 0]],\n", "  'count': [['2024-09-01', 3], ['2024-11-01', 1], ['2024-12-01', 12]]},\n", " {'companies': [['2024.0', 1]], 'count': [['2024', 16]]}]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["get_data_by_param_type_count(data, ['companies'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"ename": "SyntaxError", "evalue": "invalid syntax (<expr>, line 1)", "output_type": "error", "traceback": ["Traceback \u001b[36m(most recent call last)\u001b[39m:\n", "  File \u001b[92m/opt/miniconda3/lib/python3.13/site-packages/IPython/core/interactiveshell.py:3699\u001b[39m in \u001b[95mrun_code\u001b[39m\n    exec(code_obj, self.user_global_ns, self.user_ns)\n", "  Cell \u001b[92mIn[10]\u001b[39m\u001b[92m, line 4\u001b[39m\n    result = ne.evaluate(\"2 + 3 * 4=\")\n", "  File \u001b[92m/opt/miniconda3/lib/python3.13/site-packages/numexpr/necompiler.py:978\u001b[39m in \u001b[95mevaluate\u001b[39m\n    raise e\n", "  File \u001b[92m/opt/miniconda3/lib/python3.13/site-packages/numexpr/necompiler.py:881\u001b[39m in \u001b[95mvalidate\u001b[39m\n    _names_cache.c[expr_key] = getExprNames(ex, context, sanitize=sanitize)\n", "  File \u001b[92m/opt/miniconda3/lib/python3.13/site-packages/numexpr/necompiler.py:724\u001b[39m in \u001b[95mgetExprNames\u001b[39m\n    ex = stringToExpression(text, {}, context, sanitize)\n", "\u001b[36m  \u001b[39m\u001b[36mFile \u001b[39m\u001b[32m/opt/miniconda3/lib/python3.13/site-packages/numexpr/necompiler.py:294\u001b[39m\u001b[36m in \u001b[39m\u001b[35mstringToExpression\u001b[39m\n\u001b[31m    \u001b[39m\u001b[31mc = compile(s, '<expr>', 'eval', flags)\u001b[39m\n", "  \u001b[36mFile \u001b[39m\u001b[32m<expr>:1\u001b[39m\n\u001b[31m    \u001b[39m\u001b[31m2 + 3 * 4=\u001b[39m\n             ^\n\u001b[31mSyntaxError\u001b[39m\u001b[31m:\u001b[39m invalid syntax\n"]}], "source": ["import numexpr as ne\n", "\n", "# 计算简单数学表达式（字符串形式）\n", "result = ne.evaluate(\"2 + 3 * 4\")\n", "print(result)  # 输出：14\n", "\n", "# 支持变量\n", "a = 5\n", "b = 10\n", "result = ne.evaluate(\"(a + b) / 2\")  # 等价于 5 + 10/2\n", "print(result)  # 输出：10.0\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 2}