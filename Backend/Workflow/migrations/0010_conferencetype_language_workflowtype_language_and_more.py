# Generated by Django 5.1.3 on 2025-01-06 03:03

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('Workflow', '0009_remove_conferencetype_id_remove_workflowtype_id_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='conferencetype',
            name='language',
            field=models.Char<PERSON>ield(default='', max_length=100),
        ),
        migrations.AddField(
            model_name='workflowtype',
            name='language',
            field=models.CharField(default='', max_length=100),
        ),
        migrations.AlterField(
            model_name='conferencetype',
            name='name',
            field=models.Char<PERSON>ield(default='', max_length=100, serialize=False),
        ),
        migrations.AlterField(
            model_name='conferencetype',
            name='path',
            field=models.CharField(default='', max_length=100),
        ),
        migrations.AlterField(
            model_name='workflowtype',
            name='name',
            field=models.Char<PERSON>ield(default='', max_length=100, serialize=False),
        ),
        migrations.Alter<PERSON>ield(
            model_name='workflowtype',
            name='path',
            field=models.CharField(default='', max_length=100),
        ),
        migrations.AlterUniqueTogether(
            name='conferencetype',
            unique_together={('path', 'language')},
        ),
        migrations.AlterUniqueTogether(
            name='workflowtype',
            unique_together={('path', 'language')},
        ),
    ]
