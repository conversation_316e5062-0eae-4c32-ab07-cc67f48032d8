import django
import os
from django.conf import settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'NoahBackend.settings.docker_migration')
django.setup()

from API.models import Task
from datetime import datetime, timedelta
import csv
import openpyxl
from openpyxl import Workbook
# Get current time and calculate the date 30 days ago
now = datetime.now()
ninety_days_ago = now - timedelta(days=90)

# Query tasks created in the past 30 days
tasks = Task.objects.filter(time_created__gte=ninety_days_ago)
# Open a CSV file to write the data
# Create a new Excel workbook
workbook = Workbook()
sheet = workbook.active
sheet.title = "Tasks 90 Days"

# Define the column headers
headers = ['Time Created', 'Agent Name', 'User Email', 'IP', 'Question', 'Task Time', 'Error', 'Status']
sheet.append(headers)

# Function to sanitize strings for Excel
def sanitize_for_excel(value):
    if isinstance(value, str):
        # Remove control characters and other illegal Excel characters
        return ''.join(char for char in value if ord(char) >= 32)
    return value

# Write the data rows
for task in tasks:
    agent_name = task.agent_name if task.agent_name else task.agent.name
    waiting_time_seconds = 0
    time_dict = {}
    if task.result_json and (events:=task.result_json.get('events', [])):
        for event in events:
            if event.get('type') == 'statusUpdate' and event.get('agentStatus') in ['waiting', 'waitingFeedback']:
                if (id:=event.get('id','')) and (startedAt:=event.get('startedAt', None)):
                    time_dict[id] = startedAt
            elif event.get('type') == 'statusUpdate' and event.get('agentStatus') == 'running':
                if (id:=event.get('id','')) and (startedAt:=event.get('startedAt', None)) and id in time_dict:
                    waiting_time_seconds += event.get('startedAt') - time_dict[id]
    time_spent = task.time_updated - task.time_created - timedelta(seconds=waiting_time_seconds)
    
    if agent_name == 'planning' and time_spent.total_seconds() > 3600:
        time_spent = None
    elif time_spent.total_seconds() > 1200:
        time_spent = None
    elif time_spent.total_seconds() < 8:
        time_spent = timedelta(seconds=8) 
        
    sheet.append([
        task.time_created.replace(tzinfo=None),
        agent_name,
        task.owner.email if task.owner else '匿名',
        task.ip,
        sanitize_for_excel(task.question),
        time_spent,
        bool(task.result_json and task.result_json.get('error', '')),
        task.task_status
    ])

# Save the workbook
workbook.save('tasks_90_days.xlsx')

print(f"Excel file 'tasks_90_days.xlsx' created with {tasks.count()} tasks.")